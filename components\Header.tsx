import React, { useState, useEffect, useRef } from 'react';
import { LogoIcon, ChevronsLeftIcon, ChevronsRightIcon, ClockIcon, ThermometerIcon, PrinterIcon, CalendarClockIcon, LogOutIcon, CalendarIcon, GlobeIcon, ChevronDownIcon } from './icons';
import Notifications from './Notifications';
import { useAppState, useAppActions } from '../context/AppContext';
import { useTranslation } from '../i18n/I18nProvider';
import UserMenu from './UserMenu';

const UtilityButton: React.FC<{ onClick?: () => void; children: React.ReactNode; 'aria-label': string; title: string; }> = 
({ onClick, children, 'aria-label': ariaLabel, title }) => (
  <button
    onClick={onClick}
    className="flex items-center space-x-2 p-2 rounded-lg text-slate-400 hover:bg-slate-800 hover:text-white transition-colors"
    aria-label={ariaLabel}
    title={title}
  >
    {children}
  </button>
);


const getWeekNumber = (d: Date): number => {
    d = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
    d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay()||7));
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(),0,1));
    const weekNo = Math.ceil(( ( (d.valueOf() - yearStart.valueOf()) / 86400000) + 1)/7);
    return weekNo;
};

const LanguageSwitcher: React.FC = () => {
    const { language, setLanguage, t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);

    const languages = {
        en: 'English',
        fr: 'Français',
        ar: 'العربية'
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
            setIsOpen(false);
          }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    
    return (
        <div className="relative" ref={menuRef}>
            <button onClick={() => setIsOpen(!isOpen)} className="flex items-center space-x-2 p-2 rounded-lg text-slate-400 hover:bg-slate-800 hover:text-white transition-colors">
                <GlobeIcon className="h-5 w-5"/>
                <span className="hidden md:inline text-sm font-semibold">{languages[language]}</span>
                <ChevronDownIcon className="h-4 w-4 hidden md:inline"/>
            </button>
            {isOpen && (
                <div className="absolute end-0 mt-2 w-40 bg-slate-800 border border-slate-700 rounded-lg shadow-xl z-30 animate-fade-in">
                    <ul className="py-1">
                        {Object.entries(languages).map(([key, name]) => (
                            <li key={key}>
                                <button
                                    onClick={() => { setLanguage(key as 'en' | 'fr' | 'ar'); setIsOpen(false); }}
                                    className={`w-full text-start px-4 py-2 text-sm hover:bg-brand-accent hover:text-white transition-colors ${language === key ? 'font-bold text-white' : 'text-slate-300'}`}
                                >
                                    {name}
                                </button>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};


const Header: React.FC = () => {
  const { appState, isSidebarOpen } = useAppState();
  const { toggleSidebar, handleLogout, openBreakSchedulerModal, handleSetCurrentUser } = useAppActions();
  const { t } = useTranslation();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  if (!appState || !appState.currentUser) return null;

  const weekNumber = getWeekNumber(currentTime);

  return (
    <header className="bg-gradient-to-t from-sidebar-gradient-start via-sidebar-gradient-middle to-sidebar-gradient-end fixed top-0 start-0 end-0 z-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left Zone */}
          <div className="flex items-center space-x-3">
             <button
              onClick={toggleSidebar}
              className="p-2 rounded-full text-slate-400 hover:bg-slate-800 hover:text-white transition-colors"
              aria-label={isSidebarOpen ? t('header.collapseSidebar') : t('header.expandSidebar')}
             >
                {isSidebarOpen ? <ChevronsLeftIcon className="h-6 w-6" /> : <ChevronsRightIcon className="h-6 w-6" />}
             </button>
             <div className="flex items-center space-x-2">
                <LogoIcon className="h-8 w-8" />
                <h1 className="text-xl sm:text-2xl font-black text-slate-100 tracking-tighter hidden sm:block">
                  RackX
                </h1>
             </div>
          </div>

          {/* Center Zone - Utility Bar */}
          <div className="flex-1 flex justify-center items-center space-x-2 sm:space-x-4">
              <div className="flex items-center space-x-2 p-2 text-slate-400">
                <ClockIcon className="h-5 w-5" />
                <span className="font-mono text-sm font-semibold tracking-wider">{currentTime.toLocaleTimeString()}</span>
              </div>
              <div className="w-px h-6 bg-slate-700 hidden sm:block"></div>
               <div className="hidden sm:flex items-center space-x-2 p-2 text-slate-400">
                <ThermometerIcon className="h-5 w-5" />
                <span className="font-mono text-sm font-semibold tracking-wider">22°C</span>
              </div>
              <div className="w-px h-6 bg-slate-700 hidden sm:block"></div>
              <div className="hidden sm:flex items-center space-x-2 p-2 text-slate-400" title={t('header.currentWeek')}>
                <CalendarIcon className="h-5 w-5" />
                <span className="font-mono text-sm font-semibold tracking-wider">W{weekNumber}</span>
              </div>
              <div className="w-px h-6 bg-slate-700 hidden lg:block"></div>
              <div className="hidden lg:flex items-center space-x-1">
                <UtilityButton onClick={() => window.print()} aria-label={t('header.print')} title={t('header.print')}>
                    <PrinterIcon className="h-5 w-5"/>
                </UtilityButton>
                <UtilityButton onClick={openBreakSchedulerModal} aria-label={t('header.breakScheduler')} title={t('header.breakScheduler')}>
                    <CalendarClockIcon className="h-5 w-5"/>
                </UtilityButton>
              </div>
          </div>

          {/* Right Zone */}
          <div className="flex items-center space-x-2">
             <LanguageSwitcher />
             <Notifications appState={appState} />
             <UserMenu
                currentUser={appState.currentUser}
                users={appState.users}
                onSelectUser={handleSetCurrentUser}
                onLogout={handleLogout}
             />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;