-- This script creates a table to store connection configurations for various data sources.

-- Ensure you are using your application's database.
-- USE YourDbName;
-- GO

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DataSources' and xtype='U')
BEGIN
    CREATE TABLE dbo.DataSources (
        Id INT IDENTITY(1,1) PRIMARY KEY,

        -- A user-friendly name for the connection (e.g., "Production DB").
        Name NVARCHAR(100) NOT NULL UNIQUE,

        -- The type of data source, e.g., 'SQL_SERVER', 'SUPABASE', 'ERP'.
        Type NVARCHAR(50) NOT NULL,

        -- A JSON string containing the specific connection details.
        -- This is flexible to hold different parameters for different types.
        Config NVARCHAR(MAX) NOT NULL,

        -- Timestamps for tracking when the record was created and last modified.
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );
    PRINT 'Table "dbo.DataSources" created successfully.';
END
ELSE
BEGIN
    PRINT 'Table "dbo.DataSources" already exists.';
END
GO

-- This trigger automatically updates the 'UpdatedAt' timestamp whenever a row is modified.
IF OBJECT_ID('trg_DataSources_Update', 'TR') IS NOT NULL
   DROP TRIGGER trg_DataSources_Update;
GO

CREATE TRIGGER trg_DataSources_Update
ON dbo.DataSources
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE ds
    SET UpdatedAt = GETUTCDATE()
    FROM dbo.DataSources ds
    INNER JOIN inserted i ON ds.Id = i.Id;
END;
GO

PRINT 'Trigger "trg_DataSources_Update" for updating timestamps has been created/updated.';
GO

/*
-- EXAMPLE JSON FOR 'Config' COLUMN:

-- For SQL Server:
-- { "host": "localhost", "port": 1433, "user": "sa", "password": "your_password", "database": "TestDB", "encrypt": false }

-- For Supabase:
-- { "projectUrl": "https://<id>.supabase.co", "anonKey": "your_anon_key" }

-- For a generic ERP with an API key:
-- { "apiUrl": "https://api.erp.com/v1", "apiKey": "your_api_key" }
*/
