import React, { useState, useMemo } from 'react';
import { ClipboardListIcon } from './icons';
import { Request, UserType, AppState } from '../types';

interface RequestsPageProps {
  appState: AppState;
  onApproveRequest: (id: number) => void;
  onRejectRequest: (id: number) => void;
  isEmbedded?: boolean;
  filterByRackXId?: number;
}

type RequestManagerTab = 'pending' | 'history';

const RequestsPage: React.FC<RequestsPageProps> = (props) => {
  const { appState, onApproveRequest, onRejectRequest, isEmbedded = false, filterByRackXId } = props;
  const { currentUser, requests, users, workshops, catalog } = appState;
  const [requestManagerTab, setRequestManagerTab] = useState<RequestManagerTab>('pending');

  const userMap = useMemo(() => new Map(users.map(u => [u.id, u])), [users]);
  const rackxMap = useMemo(() => new Map(workshops.map(w => [w.id, w])), [workshops]);
  const catalogMap = useMemo(() => new Map(catalog.map(c => [c.id, c])), [catalog]);

  const { pendingRequests, requestHistory } = useMemo(() => {
    
    const visibleRequests = requests.filter(req => {
        // If a specific RackX context is provided (i.e., we are on a detail page), it's the primary filter.
        if (filterByRackXId) {
            return req.to_workshop_id === filterByRackXId || 
                   req.from_workshop_id === filterByRackXId || 
                   req.workshop_id === filterByRackXId;
        }

        // Otherwise (on the global requests page, now removed), use role-based filtering.
        if (currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') return true;
        if (currentUser.role === 'TeamLeader') {
            if (req.type === 'TRANSFER' && req.to_workshop_id === currentUser.primary_workshop_id) return true;
            if (req.type === 'CONSUMPTION' && req.workshop_id === currentUser.primary_workshop_id) return true;
        }
        if (req.created_by_user_id === currentUser.id) return true;
        
        return false;
    });

    return {
      pendingRequests: [...visibleRequests].filter(r => r.status === 'Pending').reverse(),
      requestHistory: [...visibleRequests].filter(r => r.status !== 'Pending').reverse(),
    };
  }, [requests, currentUser, filterByRackXId]);

  const canApprove = (request: Request) => {
    if (currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') {
        return request.created_by_user_id !== currentUser.id;
    }
    if (currentUser.role === 'TeamLeader') {
        const isMyTeamRequest = (request.type === 'TRANSFER' && request.to_workshop_id === currentUser.primary_workshop_id) ||
                              (request.type === 'CONSUMPTION' && request.workshop_id === currentUser.primary_workshop_id);
        return isMyTeamRequest && request.created_by_user_id !== currentUser.id;
    }
    return false;
  };

  const RequestTabButton: React.FC<{ tabId: RequestManagerTab; label: string, count: number }> = ({ tabId, label, count }) => (
    <button
        onClick={() => setRequestManagerTab(tabId)}
        className={`px-4 py-2 text-sm font-semibold rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-brand-accent flex items-center space-x-2 ${
        requestManagerTab === tabId ? 'bg-brand-accent text-slate-900' : 'text-slate-300 hover:bg-slate-700'
        }`}
    >
        <span>{label}</span>
        <span className={`px-2 py-0.5 rounded-full text-xs font-bold ${requestManagerTab === tabId ? 'bg-white text-brand-accent' : 'bg-slate-600 text-slate-200'}`}>{count}</span>
    </button>
  );

  const RequestCard: React.FC<{req: Request}> = ({ req }) => {
    const requester = userMap.get(req.created_by_user_id);
    const fromRackX = req.from_workshop_id ? rackxMap.get(req.from_workshop_id) : null;
    const toRackX = req.to_workshop_id ? rackxMap.get(req.to_workshop_id) : null;
    const consumptionRackX = req.workshop_id ? rackxMap.get(req.workshop_id) : null;
    
    return (
        <div key={req.id} className="bg-slate-900/50 p-4 rounded-lg border border-slate-700">
            <div className="flex justify-between items-start gap-4">
                <div>
                    <div className="flex items-center gap-3">
                        <span className={`px-2 py-0.5 text-xs font-semibold rounded-full ${req.type === 'TRANSFER' ? 'bg-slate-700 text-slate-300' : 'bg-purple-900/50 text-purple-300'}`}>{req.type}</span>
                        <h4 className="font-bold text-slate-100">Request #{req.id}</h4>
                    </div>
                    {req.type === 'TRANSFER' && req.items.length === 1 && (
                      <p className="text-sm text-slate-300 mt-2">{req.items[0].quantity}x {catalogMap.get(req.items[0].item_id)?.name || 'Unknown'}</p>
                    )}
                    <p className="text-sm text-slate-400 mt-1">
                      {req.type === 'TRANSFER' ? `From: ${fromRackX?.name || 'N/A'} → To: ${toRackX?.name || 'N/A'}` : `From RackX: ${consumptionRackX?.name || 'N/A'}`}
                    </p>
                    <p className="text-xs text-slate-500">By: {requester?.name || 'Unknown'} on {new Date(req.created_at).toLocaleDateString()}</p>
                </div>
                 <span className={`flex-shrink-0 px-2.5 py-1 text-xs font-semibold rounded-full ${
                    req.status === 'Pending' ? 'bg-yellow-900/50 text-yellow-300' :
                    req.status === 'Approved' ? 'bg-green-900/50 text-green-300' :
                    'bg-red-900/50 text-red-300'
                }`}>{req.status}</span>
            </div>
             {(req.reason || req.type === 'CONSUMPTION') && (
                <div className="mt-3 pt-3 border-t border-slate-700/50 space-y-2">
                    {req.reason && <p className="text-sm text-slate-300 whitespace-pre-wrap">{req.reason}</p>}
                    {req.type === 'CONSUMPTION' && (
                      <div>
                        <p className="text-sm font-semibold text-slate-300 mb-1">Items Logged:</p>
                        <ul className="text-sm text-slate-400 list-disc list-inside">
                          {req.items.map(item => <li key={item.item_id}>{item.quantity}x {catalogMap.get(item.item_id)?.name}</li>)}
                        </ul>
                         <p className="text-sm font-bold text-slate-300 mt-2">Total Cost: <span className="text-green-400">€{req.total_cost?.toFixed(2)}</span></p>
                      </div>
                    )}
                </div>
            )}
            {req.status === 'Pending' && canApprove(req) && (
                <div className="flex justify-end space-x-3 mt-4">
                    <button onClick={() => onRejectRequest(req.id)} className="px-3 py-1.5 text-sm font-semibold text-white bg-red-600 hover:bg-red-500 rounded-md transition">Reject</button>
                    <button onClick={() => onApproveRequest(req.id)} className="px-3 py-1.5 text-sm font-semibold text-white bg-green-600 hover:bg-green-500 rounded-md transition">Approve</button>
                </div>
            )}
        </div>
    )
  }

  const renderRequestList = (reqs: Request[]) => {
      if (reqs.length === 0) {
          return <p className="text-center text-slate-400 py-8">
            {requestManagerTab === 'pending' ? 'No pending requests.' : 'No request history.'}
          </p>
      }
      return reqs.map(req => <RequestCard key={req.id} req={req} />)
  }

  return (
    <div className={`${isEmbedded ? '' : 'animate-fade-in max-w-7xl mx-auto'}`}>
      {!isEmbedded && (
        <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
            <ClipboardListIcon className="w-6 h-6 mr-3 text-brand-accent" />
            <h2 className="text-2xl font-bold text-slate-100">Requests & Approvals</h2>
            </div>
        </div>
      )}
      <div className={`${isEmbedded ? '' : 'bg-slate-800/50 rounded-lg shadow-lg border border-slate-700'}`}>
        <div className={`p-4 ${isEmbedded ? '' : 'border-b border-slate-700/50'}`}>
            <div className="flex items-center space-x-2" role="tablist">
                <RequestTabButton tabId="pending" label="Pending" count={pendingRequests.length} />
                <RequestTabButton tabId="history" label="History" count={requestHistory.length} />
            </div>
        </div>
        <div className={`${isEmbedded ? 'mt-4' : 'p-4'} space-y-4`}>
            {renderRequestList(requestManagerTab === 'pending' ? pendingRequests : requestHistory)}
        </div>
    </div>
    </div>
  );
}

export default RequestsPage;