<div align="center">
<img width="1200" height="475" alt="GHBanner" src="https://github.com/user-attachments/assets/0aa67016-6eaf-458a-adb2-6e31a0763ed6" />
</div>

# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

View your app in AI Studio: https://ai.studio/apps/drive/1Yfsl0ypcslM6W0eUkERAQrKl_U9WATm_

## Run Locally

**Prerequisites:**  Node.js


1. Install dependencies:
   `npm install`
2. Set the `GEMINI_API_KEY` in [.env.local](.env.local) to your Gemini API key
3. Run the app:
   `npm run dev`

## Run with Docker

You can also run this application in a Docker container. This is a good way to run the app in a consistent and isolated environment.

**Prerequisites:** Docker

1.  **Build the Docker image:**
    You need to provide your `GEMINI_API_KEY` as a build argument.
    ```bash
    docker build --build-arg GEMINI_API_KEY="YOUR_API_KEY" -t rackx .
    ```
    Replace `"YOUR_API_KEY"` with your actual Gemini API key.

2.  **Run the Docker container:**
    ```bash
    docker run -p 8080:80 rackx
    ```
    The application will be accessible at [http://localhost:8080](http://localhost:8080).
