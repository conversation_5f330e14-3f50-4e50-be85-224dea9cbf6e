import React, { useState, FormEvent, useEffect, useMemo } from 'react';
import { Workshop } from '../types';
import { XIcon } from './icons';
import { useAppState } from '../context/AppContext';

interface EditWorkshopModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (workshopData: Workshop) => void;
    workshop: Workshop | null;
}

const EditWorkshopModal: React.FC<EditWorkshopModalProps> = ({ isOpen, onClose, onSave, workshop }) => {
    const { appState } = useAppState();
    const { programs, subPrograms } = appState!;

    const [editedWorkshop, setEditedWorkshop] = useState<Workshop | null>(workshop);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    useEffect(() => {
        setEditedWorkshop(workshop);
        if (isOpen) {
            setErrors({});
        }
    }, [isOpen, workshop]);

    const availableSubPrograms = useMemo(() => {
        if (!editedWorkshop) return [];
        return subPrograms.filter(sp => sp.program_id === editedWorkshop.program_id);
    }, [subPrograms, editedWorkshop]);

    if (!isOpen || !editedWorkshop) return null;

    const validate = () => {
        const tempErrors: { [key: string]: string } = {};
        if (!editedWorkshop.name.trim()) tempErrors.name = 'Workshop name is required.';
        if (!editedWorkshop.location_desc.trim()) tempErrors.location_desc = 'Location description is required.';
        if (editedWorkshop.sub_program_id === -1) tempErrors.sub_program_id = 'A sub-program must be selected.';
        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (validate()) {
            onSave(editedWorkshop);
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const processedValue = name === 'program_id' ? parseInt(value, 10) : value;
        setEditedWorkshop(prev => prev ? { ...prev, [name]: processedValue } : null);
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-lg relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Edit Workshop</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4">
                        <div>
                            <label htmlFor="name" className={labelClasses}>Workshop Name</label>
                            <input type="text" id="name" name="name" value={editedWorkshop.name} onChange={handleChange} className={inputClasses} required />
                            {errors.name && <p className="text-status-error text-xs mt-1">{errors.name}</p>}
                        </div>
                        <div>
                            <label htmlFor="location_desc" className={labelClasses}>Location Description</label>
                            <input type="text" id="location_desc" name="location_desc" value={editedWorkshop.location_desc} onChange={handleChange} className={inputClasses} required />
                            {errors.location_desc && <p className="text-status-error text-xs mt-1">{errors.location_desc}</p>}
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="program_id" className={labelClasses}>Program</label>
                                <select id="program_id" name="program_id" value={editedWorkshop.program_id} onChange={handleChange} className={inputClasses}>
                                    {programs.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="sub_program_id" className={labelClasses}>Sub-Program</label>
                                <select id="sub_program_id" name="sub_program_id" value={editedWorkshop.sub_program_id} onChange={handleChange} className={inputClasses} disabled={availableSubPrograms.length === 0}>
                                    {availableSubPrograms.map(sp => <option key={sp.id} value={sp.id}>{sp.name}</option>)}
                                </select>
                                {errors.sub_program_id && <p className="text-status-error text-xs mt-1">{errors.sub_program_id}</p>}
                            </div>
                        </div>

                        <div>
                            <label htmlFor="type" className={labelClasses}>Type</label>
                            <select id="type" name="type" value={editedWorkshop.type} onChange={handleChange} className={inputClasses}>
                                <option value="Local">Local</option>
                                <option value="Central">Central</option>
                            </select>
                        </div>
                    </div>

                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">Cancel</button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditWorkshopModal;
