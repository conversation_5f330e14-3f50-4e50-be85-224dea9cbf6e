
import React, { useState } from 'react';
import { CatalogCustomField } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { BoxIcon, PlusIcon, XCircleIcon, SaveIcon } from './icons';

const CatalogManagementPage: React.FC = () => {
    const { appState } = useAppState();
    const { handleUpdateCustomFields } = useAppActions();
    const { catalogCustomFields } = appState!;

    const [isAddingField, setIsAddingField] = useState(false);
    const [newFieldName, setNewFieldName] = useState('');
    const [newFieldType, setNewFieldType] = useState<'text' | 'number' | 'date' | 'boolean'>('text');
    
    const handleAddCustomField = (e: React.FormEvent) => {
        e.preventDefault();
        if (!newFieldName.trim()) return;
        
        const newFieldId = newFieldName.trim().toLowerCase().replace(/[^a-z0-9]/g, '_');
        if (catalogCustomFields.some(f => f.id === newFieldId)) {
            alert('A field with this name (or a similar one) already exists.');
            return;
        }

        const newField: CatalogCustomField = {
            id: newFieldId,
            name: newFieldName.trim(),
            type: newFieldType,
        };
        handleUpdateCustomFields([...catalogCustomFields, newField]);
        
        setNewFieldName('');
        setNewFieldType('text');
        setIsAddingField(false);
    };
    
    const handleDeleteCustomField = (fieldId: string) => {
        if (window.confirm('Are you sure you want to delete this field? All data in this column will be permanently removed from every catalog item.')) {
            handleUpdateCustomFields(catalogCustomFields.filter(f => f.id !== fieldId));
        }
    };
    
    const handleCancelAddField = () => {
        setIsAddingField(false);
        setNewFieldName('');
        setNewFieldType('text');
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";

    return (
        <div role="tabpanel" className="animate-fade-in space-y-8">
            <div>
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                        <BoxIcon className="w-6 h-6 mr-3 text-brand-accent" />
                        <h3 className="text-xl font-bold text-slate-200">
                            Catalog Structure
                        </h3>
                    </div>
                </div>
                <p className="text-slate-400 mt-2 mb-6">
                    Define custom data fields for the master catalog. These fields will appear as editable columns on the main Item Catalog page. Item data can be edited there.
                </p>
            </div>

            <div className="bg-slate-800/50 rounded-lg shadow-lg border border-slate-700">
                <div className="p-4 sm:p-6 flex items-center justify-between border-b border-slate-700">
                    <h4 className="text-lg font-semibold text-slate-200">Custom Fields</h4>
                    {!isAddingField && (
                        <button 
                            onClick={() => setIsAddingField(true)}
                            className="flex items-center px-3 py-1.5 bg-transparent border border-brand-accent text-brand-accent text-sm font-semibold rounded-md shadow-md hover:bg-brand-accent/20 transition"
                        >
                            <PlusIcon className="w-5 h-5 mr-2" />
                            Add Field
                        </button>
                    )}
                </div>
                
                <div className="p-4 sm:p-6">
                    {catalogCustomFields.length === 0 && !isAddingField ? (
                        <p className="text-center py-8 text-slate-400">No custom fields defined. Add one to get started.</p>
                    ) : (
                        <ul className="space-y-3">
                            {catalogCustomFields.map(field => (
                                <li key={field.id} className="p-3 flex justify-between items-center bg-slate-900/50 rounded-lg border border-slate-700/50">
                                    <div>
                                        <p className="font-semibold text-slate-200">{field.name}</p>
                                        <span className="text-xs font-mono capitalize px-2 py-0.5 mt-1 inline-block rounded-full bg-slate-700 text-slate-300">{field.type}</span>
                                    </div>
                                    <button onClick={() => handleDeleteCustomField(field.id)} className="p-2 rounded-full text-red-500 hover:text-red-400 hover:bg-red-500/10 transition-colors">
                                        <XCircleIcon className="w-5 h-5" />
                                    </button>
                                </li>
                            ))}
                        </ul>
                    )}
                    
                    {isAddingField && (
                        <form onSubmit={handleAddCustomField} className="mt-4 pt-4 border-t border-slate-700 animate-fade-in">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="text-sm font-medium text-slate-300 mb-1 block">Field Name</label>
                                    <input 
                                        type="text"
                                        value={newFieldName}
                                        onChange={(e) => setNewFieldName(e.target.value)}
                                        placeholder="e.g., Supplier"
                                        className={inputClasses}
                                        autoFocus
                                    />
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-slate-300 mb-1 block">Field Type</label>
                                    <select value={newFieldType} onChange={e => setNewFieldType(e.target.value as any)} className={inputClasses}>
                                        <option value="text">Text</option>
                                        <option value="number">Number</option>
                                        <option value="date">Date</option>
                                        <option value="boolean">Yes/No (Boolean)</option>
                                    </select>
                                </div>
                            </div>
                            <div className="flex justify-end gap-3">
                                <button type="button" onClick={handleCancelAddField} className="px-4 py-2 bg-slate-600 text-white font-semibold rounded-md shadow-md hover:bg-slate-500 transition">
                                    Cancel
                                </button>
                                <button type="submit" className="flex items-center justify-center px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                                    <SaveIcon className="w-5 h-5 mr-2" />
                                    Save Field
                                </button>
                            </div>
                        </form>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CatalogManagementPage;
