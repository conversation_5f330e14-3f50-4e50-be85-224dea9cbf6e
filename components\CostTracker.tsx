import React from 'react';
import { ConsumedItem } from '../types';
import { EuroIcon } from './icons';
import { useTranslation } from '../i18n/I18nProvider';

interface CostTrackerProps {
  consumedItems: ConsumedItem[];
  totalCost: number;
  className?: string;
}

const CostTracker: React.FC<CostTrackerProps> = ({ consumedItems, totalCost, className }) => {
  const { t } = useTranslation();

  return (
    <div className={`bg-slate-800/50 p-4 sm:p-6 rounded-lg shadow-lg border border-slate-700 animate-fade-in ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
            <EuroIcon className="w-6 h-6 me-3 text-green-400" />
            <h3 className="text-xl font-bold text-slate-100">{t('costTracker.title')}</h3>
        </div>
      </div>

      <p className="text-5xl font-bold text-green-400 tracking-tight">
        €{totalCost.toFixed(2)}
      </p>
      
      <div className="mt-6">
        <h4 className="text-sm font-semibold text-slate-400 uppercase tracking-wider">{t('costTracker.consumedItems')}</h4>
        {consumedItems.length === 0 ? (
          <p className="text-slate-400 mt-2 text-sm">{t('costTracker.noItems')}</p>
        ) : (
          <ul className="mt-2 space-y-2 text-sm max-h-48 overflow-y-auto pe-2">
            {consumedItems.map(item => (
              <li key={item.id} className="flex justify-between items-center bg-slate-800 p-2 rounded-md">
                <span className="text-slate-300">{item.name}</span>
                <span className="font-mono text-slate-400">x{item.quantity}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default CostTracker;