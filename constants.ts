import { AppState, CatalogItem, Workshop, WorkshopInventoryItem, CatalogCustomField, Request, AuditLog, BreakSchedule, Program, SubProgram } from './types';
import { INITIAL_USERS } from './users';

export const PROGRAM_TAB_COLORS = [
    '#17648B', // Original brand-accent
    '#008080', // Teal
    '#8B4513', // SaddleBrown
    '#4682B4', // SteelBlue
    '#2E8B57', // SeaGreen
];

export const STAT_CARD_THEMES = [
  // Theme 0: Default (reordered from original)
  [
    { color: "text-amber-300", gradient: "from-yellow-700 to-amber-900" },
    { color: "text-teal-300", gradient: "from-teal-800 to-teal-600" },
    { color: "text-violet-300", gradient: "from-purple-900 to-violet-800" },
    { color: "text-indigo-300", gradient: "from-blue-800 to-indigo-900"},
  ],
  // Theme 1: Teal/Green
  [
    { color: "text-emerald-300", gradient: "from-emerald-800 to-emerald-600" },
    { color: "text-green-300", gradient: "from-green-700 to-green-900" },
    { color: "text-lime-300", gradient: "from-lime-900 to-lime-800" },
    { color: "text-cyan-300", gradient: "from-cyan-800 to-cyan-900"},
  ],
  // Theme 2: Red/Pink
  [
    { color: "text-red-300", gradient: "from-red-800 to-red-600" },
    { color: "text-rose-300", gradient: "from-rose-700 to-rose-900" },
    { color: "text-pink-300", gradient: "from-pink-900 to-pink-800" },
    { color: "text-fuchsia-300", gradient: "from-fuchsia-800 to-fuchsia-900"},
  ],
   // Theme 3: Blue/Purple
  [
    { color: "text-sky-300", gradient: "from-sky-800 to-sky-600" },
    { color: "text-blue-300", gradient: "from-blue-700 to-blue-900" },
    { color: "text-indigo-300", gradient: "from-indigo-900 to-indigo-800" },
    { color: "text-purple-300", gradient: "from-purple-800 to-purple-900"},
  ],
];

const INITIAL_CATALOG_CUSTOM_FIELDS: CatalogCustomField[] = [
  { id: 'supplier', name: 'Supplier', type: 'text' },
  { id: 'internal_pn', name: 'Internal P/N', type: 'text' },
];

const INITIAL_PROGRAMS: Program[] = [
  { id: 1, name: 'General Program' },
];

const INITIAL_DEPARTMENTS: SubProgram[] = [
  { id: 1, name: 'Default Department', program_id: 1 },
];

const INITIAL_WORKSHOPS: Workshop[] = [
    { id: 1, name: 'RackX Central', location_desc: 'Main warehouse', type: 'Central', sub_program_id: 1 },
    { id: 2, name: 'RackX Assembly 1', location_desc: 'First floor assembly', type: 'Local', sub_program_id: 1 },
    { id: 3, name: 'RackX Assembly 2', location_desc: 'Second floor assembly', type: 'Local', sub_program_id: 1 },
    { id: 4, name: 'RackX QC', location_desc: 'QC lab', type: 'Local', sub_program_id: 1 },
    { id: 5, name: 'RackX Paint', location_desc: 'Paint booth area', type: 'Local', sub_program_id: 1 },
    { id: 6, name: 'RackX Final Assembly', location_desc: 'Final assembly line', type: 'Local', sub_program_id: 1 },
    { id: 7, name: 'RackX Electrical', location_desc: 'Electronics lab', type: 'Local', sub_program_id: 1 },
];

const INITIAL_CATALOG: CatalogItem[] = [
    { id: 1, name: 'Safety Goggles', description: 'Protective eyewear', sku: 'SG-001', image_url: '/img/placeholder.jpg', category: 'Safety', default_unit_price: 5.99, is_restricted: false, custom_fields: { supplier: 'SafeCo', internal_pn: 'PN-12345' } },
    { id: 2, name: 'Work Gloves', description: 'Heavy-duty work gloves', sku: 'WG-001', image_url: '/img/placeholder.jpg', category: 'Safety', default_unit_price: 12.50, is_restricted: false, custom_fields: { supplier: 'GloveCorp', internal_pn: 'PN-67890' } },
];
const INITIAL_WORKSHOP_INVENTORY: WorkshopInventoryItem[] = [];
const INITIAL_REQUESTS: Request[] = [];
const INITIAL_AUDIT_LOG: AuditLog[] = [];
const INITIAL_BREAK_SCHEDULES: Record<number, BreakSchedule> = {};

export const INITIAL_DATA: AppState = {
  programs: INITIAL_PROGRAMS,
  subPrograms: INITIAL_DEPARTMENTS,
  catalog: INITIAL_CATALOG,
  catalogCustomFields: INITIAL_CATALOG_CUSTOM_FIELDS,
  workshops: INITIAL_WORKSHOPS,
  workshopInventories: INITIAL_WORKSHOP_INVENTORY,
  consumed: [],
  users: INITIAL_USERS,
  currentUser: INITIAL_USERS.find(u => u.role === 'Admin') || INITIAL_USERS[0],
  requests: INITIAL_REQUESTS,
  auditLog: INITIAL_AUDIT_LOG,
  stagedImports: [],
  breakSchedules: INITIAL_BREAK_SCHEDULES,
};
