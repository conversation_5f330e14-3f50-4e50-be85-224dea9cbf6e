import React, { useState, useMemo } from 'react';
import { UserType, AppState, Program, SubProgram, Workshop } from '../types';
import { BoxIcon, ChevronDownIcon, XIcon } from './icons';
import { useTranslation } from '../i18n/I18nProvider';

interface LoginPageProps {
  onLogin: (user: UserType) => void;
  appState: AppState;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin, appState }) => {
  const { programs, subPrograms, workshops, users } = appState;
  const { t } = useTranslation();
  
  const [expanded, setExpanded] = useState({
      programs: new Set<number>(),
      subPrograms: new Set<number>(),
      workshops: new Set<number>()
  });
  
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [passwordInput, setPasswordInput] = useState('');
  const [loginError, setLoginError] = useState('');

  const handleSelectUser = (user: UserType) => {
    setSelectedUser(user);
    setPasswordInput('');
    setLoginError('');
  };

  const handleAttemptLogin = () => {
    if (!selectedUser) return;
    if (selectedUser.password === passwordInput) {
      onLogin(selectedUser);
    } else {
      setLoginError(t('login.passwordError'));
    }
  };

  const toggleExpansion = (type: 'programs' | 'subPrograms' | 'workshops', id: number) => {
    setExpanded(prev => {
        const newSet = new Set(prev[type]);
        if (newSet.has(id)) {
            newSet.delete(id);
        } else {
            newSet.add(id);
        }
        return { ...prev, [type]: newSet };
    });
  };

  const admins = users.filter(u => u.role === 'Admin');
  
  const subProgramsByProgramId = useMemo(() => 
    subPrograms.reduce((acc, sp) => {
      if (!acc[sp.program_id]) acc[sp.program_id] = [];
      acc[sp.program_id].push(sp);
      return acc;
    }, {} as Record<number, SubProgram[]>)
  , [subPrograms]);
  
  const workshopsBySubProgramId = useMemo(() =>
    workshops.reduce((acc, w) => {
      if (w.type === 'Local') {
        if (!acc[w.sub_program_id]) acc[w.sub_program_id] = [];
        acc[w.sub_program_id].push(w);
      }
      return acc;
    }, {} as Record<number, Workshop[]>)
  , [workshops]);

  const workshopDetails = useMemo(() => {
    return workshops.reduce((acc, workshop) => {
      const usersInWorkshop = users.filter(u => u.primary_workshop_id === workshop.id && u.role !== 'Admin');
      const teamLead = usersInWorkshop.find(u => u.role === 'TeamLeader');
      acc[workshop.id] = {
        users: usersInWorkshop,
        teamLead,
      };
      return acc;
    }, {} as Record<number, { users: UserType[]; teamLead?: UserType }>);
  }, [workshops, users]);

  const UserCard: React.FC<{ user: UserType }> = ({ user }) => (
    <button
      onClick={() => handleSelectUser(user)}
      className="group text-center p-4 bg-slate-800 rounded-lg border border-slate-700 hover:border-brand-accent hover:bg-slate-700/50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-brand-accent"
    >
      <img src={user.avatarUrl} alt={user.name} className="mx-auto h-16 w-16 rounded-full bg-slate-700 object-cover" />
      <p className="mt-3 font-semibold text-slate-200 truncate">{user.name}</p>
      <p className="text-xs text-slate-400 truncate">{user.role}</p>
    </button>
  );

  const ExpandableRow: React.FC<{ title: string; subtitle: string; onClick: () => void, isExpanded: boolean }> = ({ title, subtitle, onClick, isExpanded }) => (
    <button
      onClick={onClick}
      className="w-full text-start p-3 bg-slate-800 rounded-lg border border-transparent hover:border-brand-accent/50 hover:bg-slate-700/50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-brand-accent/50 flex items-center justify-between"
    >
      <div>
        <h4 className="font-semibold text-slate-100">{title}</h4>
        <p className="text-sm text-slate-400">{subtitle}</p>
      </div>
      <ChevronDownIcon className={`w-5 h-5 text-slate-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
    </button>
  );

  return (
    <>
    <div className="min-h-screen flex flex-col items-center justify-center bg-slate-950 p-4 animate-fade-in">
        <div className="flex items-center space-x-3 mb-6">
            <BoxIcon className="h-10 w-10 text-brand-accent" />
            <h1 className="text-4xl font-bold text-slate-100 tracking-tight">{t('login.title')}</h1>
        </div>
        
        <div className="w-full max-w-4xl space-y-8">
            {/* Admin Section */}
            <div className="bg-[var(--card-bg-panel)] backdrop-blur-md border border-slate-700 rounded-xl shadow-card p-6">
                <h3 className="text-xl font-bold text-slate-100 mb-4">{t('login.adminSection')}</h3>
                 <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    {admins.map(user => <UserCard key={user.id} user={user} />)}
                </div>
            </div>

            {/* Program/User Selection Section */}
            <div className="bg-[var(--card-bg-panel)] backdrop-blur-md border border-slate-700 rounded-xl shadow-card p-6">
                <h3 className="text-xl font-bold text-slate-100 mb-4">{t('login.programSection')}</h3>
                <div className="space-y-1">
                    {programs.map(program => (
                        <div key={program.id} className="bg-slate-900/50 rounded-lg">
                            <ExpandableRow
                                title={program.name}
                                subtitle={t('login.subPrograms', { count: subProgramsByProgramId[program.id]?.length || 0 })}
                                isExpanded={expanded.programs.has(program.id)}
                                onClick={() => toggleExpansion('programs', program.id)}
                            />
                            {expanded.programs.has(program.id) && (
                                <div className="ps-4 border-s-2 border-slate-700 ms-4">
                                    {(subProgramsByProgramId[program.id] || []).map(subProgram => (
                                        <div key={subProgram.id} className="my-1">
                                            <ExpandableRow
                                                title={subProgram.name}
                                                subtitle={t('login.rackx', { count: workshopsBySubProgramId[subProgram.id]?.length || 0 })}
                                                isExpanded={expanded.subPrograms.has(subProgram.id)}
                                                onClick={() => toggleExpansion('subPrograms', subProgram.id)}
                                            />
                                            {expanded.subPrograms.has(subProgram.id) && (
                                                <div className="ps-4 border-s-2 border-slate-600 ms-4">
                                                     {(workshopsBySubProgramId[subProgram.id] || []).map(workshop => {
                                                        const details = workshopDetails[workshop.id];
                                                        const workshopUsers = details?.users || [];
                                                        const teamLead = details?.teamLead;
                                                        const subtitle = teamLead ? t('login.userLead', { name: teamLead.name }) : t('login.userCount', { count: workshopUsers.length });
                                                        
                                                        return (
                                                        <div key={workshop.id} className="my-1">
                                                            <ExpandableRow
                                                                title={workshop.name}
                                                                subtitle={subtitle}
                                                                isExpanded={expanded.workshops.has(workshop.id)}
                                                                onClick={() => toggleExpansion('workshops', workshop.id)}
                                                            />
                                                            {expanded.workshops.has(workshop.id) && (
                                                                <div className="p-4 bg-slate-800/50 rounded-b-lg">
                                                                    {(workshopUsers.length > 0) ? (
                                                                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                                                            {workshopUsers.map(user => <UserCard key={user.id} user={user} />)}
                                                                        </div>
                                                                    ) : (
                                                                        <p className="text-center text-sm text-slate-500 py-4">{t('login.noUsers')}</p>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                     )})}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </div>
        <p className="text-xs text-slate-500 mt-8">{t('login.defaultPasswordNotice')}</p>
    </div>
    {selectedUser && (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in" onClick={() => setSelectedUser(null)}>
            <div className="bg-[var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-sm p-6 text-center relative" onClick={e => e.stopPropagation()}>
                <button onClick={() => setSelectedUser(null)} className="absolute top-2 end-2 text-slate-400 hover:text-white p-2 rounded-full" aria-label="Close login prompt">
                    <XIcon className="w-5 h-5"/>
                </button>
                <img src={selectedUser.avatarUrl} alt={selectedUser.name} className="mx-auto h-24 w-24 rounded-full bg-slate-700 object-cover border-4 border-slate-600" />
                <h3 className="text-2xl font-bold text-slate-100 mt-4">{selectedUser.name}</h3>
                <p className="text-slate-400">{selectedUser.role}</p>
                
                <form onSubmit={(e) => { e.preventDefault(); handleAttemptLogin(); }} className="mt-6">
                    <div>
                        <label htmlFor="password-input" className="sr-only">{t('login.passwordPrompt')}</label>
                        <input
                        id="password-input"
                        type="password"
                        value={passwordInput}
                        onChange={(e) => { setPasswordInput(e.target.value); setLoginError(''); }}
                        className="w-full bg-slate-700 border border-slate-600 rounded-md p-3 text-slate-200 text-center text-lg tracking-widest focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition"
                        placeholder={t('login.passwordPrompt')}
                        autoFocus
                        />
                        {loginError && <p className="text-red-400 text-xs mt-2">{loginError}</p>}
                    </div>
                    <button
                        type="submit"
                        className="mt-4 w-full bg-brand-accent text-brand-text-on-accent font-bold py-3 px-8 rounded-md text-lg shadow-lg hover:bg-brand-accent-hover transition-transform transform hover:scale-105 duration-300 focus:outline-none focus:ring-4 focus:ring-amber-500/50"
                    >
                        {t('login.login')}
                    </button>
                </form>
            </div>
        </div>
    )}
    </>
  );
};

export default LoginPage;