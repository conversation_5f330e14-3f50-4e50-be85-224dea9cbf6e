export default {
    "app.loading": "Chargement de RackX...",
    "intro.welcome": "Bienvenue sur RackX",
    "intro.description": "La plateforme logistique intelligente pour l'industrie aérospatiale. Optimisez votre inventaire, gérez vos ateliers et accélérez la production.",
    "intro.getStarted": "Commencer",
    "login.title": "RackX",
    "login.adminSection": "Administration du système",
    "login.programSection": "Sélectionner l'accès au programme",
    "login.subPrograms": "{{count}} Sous-programmes",
    "login.rackx": "{{count}} RackX",
    "login.userLead": "Chef: {{name}}",
    "login.userCount": "{{count}} Utilisateurs",
    "login.noUsers": "Aucun utilisateur assigné à ce RackX.",
    "login.passwordPrompt": "Mot de passe",
    "login.passwordError": "Mot de passe incorrect. Veuillez réessayer.",
    "login.login": "Se connecter",
    "login.defaultPasswordNotice": "Le mot de passe par défaut pour tous les utilisateurs est : 1111",
    "header.print": "Imprimer la page",
    "header.breakScheduler": "Planificateur de pauses d'équipe",
    "header.logout": "Déconnexion",
    "header.collapseSidebar": "Réduire le volet latéral",
    "header.expandSidebar": "Développer le volet latéral",
    "header.currentWeek": "Numéro de la semaine actuelle",
    "header.notifications": "Voir les notifications",
    "header.language": "Langue",
    "notifications.title": "Notifications",
    "notifications.markAllRead": "Marquer tout comme lu",
    "notifications.noNotifications": "Vous n'avez aucune nouvelle notification.",
    "notifications.requestStatusChanged": "{{actorName}} a {{action}} {{requestSummary}}.",
    "notifications.requestStatusApproved": "approuvé",
    "notifications.requestStatusRejected": "rejeté",
    "notifications.requestSummaryDefault": "votre demande",
    "notifications.requestSummaryTransfer": "votre demande pour {{quantity}}x {{itemName}}",
    "notifications.requestSummaryConsumption": "votre journal de consommation de {{count}} article(s)",
    "notifications.profileUpdated": "{{actorName}} a mis à jour votre profil.",
    "notifications.userAssigned": "{{actorName}} a assigné {{assignedUserName}} à votre RackX.",
    "sidebar.dashboard": "Tableau de bord",
    "sidebar.myRackX": "Mon RackX",
    "sidebar.inventoryCatalog": "Catalogue d'inventaire",
    "sidebar.browseRackX": "Parcourir les RackX",
    "sidebar.reports": "Rapports",
    "sidebar.documentation": "Documentation",
    "sidebar.settings": "Paramètres",
    "sidebar.adminPanel": "Panneau d'administration",
    "costTracker.title": "Coût de la session",
    "costTracker.consumedItems": "Articles consommés",
    "costTracker.noItems": "Utilisez un article de l'inventaire pour le voir ici.",
    "dashboard.title": "Tableau de bord",
    "dashboard.welcome": "Bienvenue, {{name}}. Voici le pouls de vos opérations.",
    "dashboard.totalInventoryValue": "Valeur totale de l'inventaire",
    "dashboard.lowStockItems": "Articles à stock faible",
    "dashboard.pendingRequests": "Demandes en attente",
    "dashboard.localWorkshops": "Ateliers locaux",
    "dashboard.itemsUnit": "Articles",
    "dashboard.requestsUnit": "Demandes",
    "dashboard.rackxUnit": "RackX",
    "dashboard.spendTrendTitle": "Tendance des dépenses de {{programName}} (30j)",
    "dashboard.spendTrendNoData": "Aucune donnée de dépense pour les 30 derniers jours.",
    "dashboard.rackxSpendTitle": "Répartition des dépenses par RackX (30j)",
    "dashboard.topConsumedTitle": "Top 5 des articles consommés",
    "dashboard.workshopHealthTitle": "Santé des ateliers",
    "dashboard.workshopHealthLead": "Chef: {{name}}",
    "dashboard.workshopHealthLowStock": "{{count}} faible(s)",
    "dashboard.workshopHealthHealthy": "Sain",
    "dashboard.workshopHealthCritical": "{{count}} critique(s)",
    "dashboard.requestStatusTitle": "Statut des demandes",
    "dashboard.donutTotal": "Total",
    "dashboard.donutPending": "En attente",
    "dashboard.donutApproved": "Approuvé",
    "dashboard.donutRejected": "Rejeté",
    "dashboard.noData": "Aucune donnée",
    "activity.title": "Fil d'activité",
    "activity.noActivity": "Aucune activité système pour le moment.",
    "time.years": "ans",
    "time.months": "mois",
    "time.days": "jours",
    "time.hours": "heures",
    "time.minutes": "minutes",
    "time.justNow": "à l'instant",
    "time.ago": "il y a",
    "approvals.title": "Approbations en attente",
    "approvals.noApprovals": "Aucun élément en attente de votre approbation.",
    "approvals.reject": "Rejeter la demande {{id}}",
    "approvals.approve": "Approuver la demande {{id}}",
    "approvals.requestTypeConsumption": "Journal de consommation ({{count}} articles)",
    "approvals.requestTypeUnknown": "Demande inconnue",
    "approvals.requestBy": "Par {{name}}",
    "reports.title": "Rapports & Statistiques",
    "reports.description": "Indicateurs de performance clés et analyses du système.",
    "reports.topConsumedTitle": "Articles les plus consommés",
    "reports.noConsumptionData": "Aucune donnée de consommation disponible pour le moment.",
    "reports.mostActiveTitle": "Utilisateurs les plus actifs",
    "reports.noUserData": "Aucune activité d'utilisateur enregistrée pour le moment.",
    "reports.actionsUnit": "actions",
    "reports.stockTrendsTitle": "Tendances des stocks",
    "reports.stockTrendsDesc": "L'analyse des tendances historiques des stocks est en cours de développement et sera disponible dans une future mise à jour.",
    "myRackx.noWorkshop": "Aucun atelier assigné",
    "myRackx.noWorkshopDesc": "Vous n'êtes actuellement assigné à aucun atelier. Veuillez contacter un administrateur.",
    "myRackx.pageTitle": "{{name}}",
    "myRackx.managedBy": "Géré par {{name}}.",
    "myRackx.noLead": "Aucun chef d'équipe assigné.",
    "myRackx.yourWorkspace": "Votre espace de travail opérationnel principal.",
    "myRackx.viewingWorkspace": "Consultation de l'espace de travail d'une autre équipe.",
    "myRackx.smartReplenish": "Réapprovisionnement intelligent",
    "myRackx.requestTransfer": "Demander un transfert de nouvel article",
    "myRackx.inventoryTitle": "Inventaire",
    "myRackx.searchPlaceholder": "Rechercher par ID, nom ou SKU...",
    "myRackx.tableHeaderItem": "Article",
    "myRackx.tableHeaderStock": "Stock",
    "myRackx.tableHeaderConsume": "Qté à consommer",
    "myRackx.noInventory": "Cet atelier n'a pas d'inventaire.",
    "myRackx.noSearchResults": "Aucun article ne correspond à \"{{term}}\".",
    "myRackx.logConsumptionTitle": "Enregistrer la consommation",
    "myRackx.logConsumptionPlaceholder": "Saisissez une quantité dans la liste d'inventaire pour enregistrer la consommation d'un article.",
    "myRackx.logConsumptionDisabled": "La consommation ne peut être enregistrée que depuis votre propre RackX.",
    "myRackx.logConsumptionTotal": "Total :",
    "myRackx.logConsumptionSubmit": "Soumettre pour approbation",
    "myRackx.requestsTitle": "Demandes de l'atelier",
    "myRackx.pendingTeamRequests": "Demandes d'équipe en attente",
    "myRackx.myPendingRequests": "Mes demandes en attente",
    "smartReplenish.modalTitle": "Réapprovisionnement intelligent pour {{name}}",
    "smartReplenish.title": "Prêt pour l'analyse intelligente",
    "smartReplenish.description": "Utilisez l'IA pour analyser l'inventaire de cet atelier et suggérer automatiquement les articles à réapprovisionner depuis l'entrepôt central.",
    "smartReplenish.button": "Analyser & Suggérer des articles",
    "smartReplenish.loadingTitle": "Analyse de l'inventaire...",
    "smartReplenish.loadingDesc": "L'IA analyse les niveaux de stock et l'utilisation récente pour générer des suggestions intelligentes.",
    "smartReplenish.errorTitle": "L'analyse a échoué",
    "smartReplenish.tryAgain": "Réessayer",
    "smartReplenish.noReplenishTitle": "Aucun réapprovisionnement nécessaire",
    "smartReplenish.noReplenishDesc": "L'IA a déterminé que l'inventaire de {{name}} est bien approvisionné pour le moment.",
    "smartReplenish.tableHeaderItem": "Article",
    "smartReplenish.tableHeaderReason": "Raison",
    "smartReplenish.tableHeaderQty": "Qté suggérée",
    "smartReplenish.submitButton": "Créer les demandes de transfert",
    "logConsumption.modalTitle": "Justifier la consommation",
    "logConsumption.description": "Veuillez indiquer la raison de la consommation de ces articles.",
    "logConsumption.purpose": "Raison",
    "logConsumption.orderId": "N° de commande de travail",
    "logConsumption.fabOrderId": "N° d'ordre de fabrication",
    "logConsumption.duration": "Temps de fabrication (heures)",
    "logConsumption.notes": "Notes supplémentaires (facultatif)",
    "logConsumption.notesPlaceholder": "Ajoutez tout autre détail pertinent...",
    "logConsumption.submit": "Soumettre la justification",
    "logConsumption.purpose_Fabrication": "Fabrication",
    "logConsumption.purpose_Assembly": "Assemblage",
    "logConsumption.purpose_Repair": "Réparation",
    "logConsumption.purpose_Maintenance": "Maintenance",
    "logConsumption.purpose_General_Use": "Usage général",
    "cancel": "Annuler",
    "close": "Fermer"
}