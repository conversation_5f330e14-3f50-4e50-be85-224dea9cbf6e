import React, { useMemo } from 'react';
import { LayoutDashboardIcon, BarChartIcon, TrendingUpIcon, EuroIcon, AlertTriangleIcon, WrenchIcon, ClipboardListIcon, UserIcon } from './icons';
import StatCard from './StatCard';
import { AppState, Page, Request, CatalogItem, Workshop, TopConsumedItem, Program, SubProgram } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { useTranslation } from '../i18n/I18nProvider';
import { Donut<PERSON>hart, HorizontalBarChart, LineChart, DashboardCard, MONOTONE_BLUE_GRADIENTS } from './DashboardPage';
import { STAT_CARD_THEMES } from '../constants';

const ProgramDashboard: React.FC<{ programName: string }> = ({ programName }) => {
  const { appState } = useAppState();
  const { t } = useTranslation();
  const { requests, catalog, workshops, workshopInventories, programs, subPrograms, users } = appState!;

  const program = useMemo(() => programs.find(p => p.name.toLowerCase() === programName.toLowerCase()), [programs, programName]);

  const programWorkshops = useMemo(() => {
    if (!program) return [];
    const programSubProgramIds = subPrograms.filter(sp => sp.program_id === program.id).map(sp => sp.id);
    return workshops.filter(w => programSubProgramIds.includes(w.sub_program_id));
  }, [program, subPrograms, workshops]);

  const programWorkshopIds = useMemo(() => programWorkshops.map(w => w.id), [programWorkshops]);

  const totalInventoryValue = useMemo(() => {
    const catalogPriceMap = new Map(catalog.map(i => [i.id, i.default_unit_price]));
    return workshopInventories
      .filter(inv => programWorkshopIds.includes(inv.workshop_id))
      .reduce((acc, inv) => {
        const price = catalogPriceMap.get(inv.item_id) || 0;
        return acc + (inv.quantity * price);
      }, 0);
  }, [programWorkshopIds, workshopInventories, catalog]);

  const lowStockItemsCount = useMemo(() =>
    workshopInventories
      .filter(i => programWorkshopIds.includes(i.workshop_id) && i.quantity > 0 && i.quantity < i.low_stock_threshold)
      .length
  , [programWorkshopIds, workshopInventories]);

  const approvedConsumptionsLast30Days = useMemo(() => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return requests.filter(r =>
      r.type === 'CONSUMPTION' &&
      r.status === 'Approved' &&
      new Date(r.created_at) >= thirtyDaysAgo &&
      programWorkshopIds.includes(r.workshop_id!)
    );
  }, [requests, programWorkshopIds]);

  const totalSpend = useMemo(() =>
    approvedConsumptionsLast30Days.reduce((acc, req) => acc + (req.total_cost || 0), 0)
  , [approvedConsumptionsLast30Days]);

  const spendTrend = useMemo(() => {
    const spendTrendByDate = new Map<string, number>();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const dateKey = date.toISOString().split('T')[0];
      spendTrendByDate.set(dateKey, 0);
    }

    approvedConsumptionsLast30Days.forEach(req => {
      if (req.total_cost) {
        const reqDate = new Date(req.created_at);
        reqDate.setHours(0, 0, 0, 0);
        const dateKey = reqDate.toISOString().split('T')[0];
        if (spendTrendByDate.has(dateKey)) {
          spendTrendByDate.set(dateKey, spendTrendByDate.get(dateKey)! + req.total_cost);
        }
      }
    });

    const formatLabel = (dateString: string) => {
      const date = new Date(dateString);
      return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
    };

    return Array.from(spendTrendByDate.entries())
      .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
      .map(([date, spend]) => ({ label: formatLabel(date), value: parseFloat(spend.toFixed(2)) }));
  }, [approvedConsumptionsLast30Days]);

  const spendByWorkshop = useMemo(() => {
    const workshopSpendMap = new Map<number, number>();
    approvedConsumptionsLast30Days.forEach(req => {
      if (req.total_cost && req.workshop_id) {
        const currentSpend = workshopSpendMap.get(req.workshop_id) || 0;
        workshopSpendMap.set(req.workshop_id, currentSpend + req.total_cost);
      }
    });

    const workshopNameMap = new Map(workshops.map(w => [w.id, w.name]));
    return Array.from(workshopSpendMap.entries())
      .map(([workshopId, spend]) => ({
        workshopName: workshopNameMap.get(workshopId) || `RackX #${workshopId}`,
        spend: parseFloat(spend.toFixed(2)),
      }))
      .filter(d => d.spend > 0)
      .sort((a, b) => b.spend - a.spend);
  }, [approvedConsumptionsLast30Days, workshops]);

  const workshopCount = programWorkshops.length;

  const programTheme = useMemo(() => {
    if (!program) return STAT_CARD_THEMES[0];
    const programIndex = programs.findIndex(p => p.id === program.id);
    // Use programIndex + 1 to avoid using the general theme (index 0) for the first program
    return STAT_CARD_THEMES[(programIndex + 1) % STAT_CARD_THEMES.length];
  }, [program, programs]);

  if (!program) {
    return <div>Program not found: {programName}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <StatCard icon={<EuroIcon />} title={t('dashboard.totalInventoryValue')} value={`€${totalInventoryValue.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`} color={programTheme[0].color} gradient={programTheme[0].gradient} />
        <StatCard icon={<AlertTriangleIcon />} title={t('dashboard.lowStockItems')} value={`${lowStockItemsCount} ${t('dashboard.itemsUnit')}`} color={programTheme[1].color} gradient={programTheme[1].gradient} />
        <StatCard icon={<WrenchIcon />} title={t('dashboard.workshopCount')} value={`${workshopCount} ${t('dashboard.rackxUnit')}`} color={programTheme[2].color} gradient={programTheme[2].gradient} />
        <StatCard icon={<TrendingUpIcon />} title={t('dashboard.totalSpend30d')} value={`€${totalSpend.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`} color={programTheme[3].color} gradient={programTheme[3].gradient} />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <DashboardCard className="flex flex-col">
          <div className="p-8">
            <h3 className="text-base font-semibold text-slate-200 flex items-center mb-4">
              <TrendingUpIcon className="w-5 h-5 mr-2 text-sky-400" />
              {t('dashboard.spendTrendTitle', { programName: program.name })}
            </h3>
            <LineChart data={spendTrend} noDataLabel={t('dashboard.spendTrendNoData')} />
          </div>
        </DashboardCard>
        <DashboardCard className="flex flex-col">
          {spendByWorkshop && spendByWorkshop.length > 0 ? (
            <div className="p-8">
              <h4 className="text-md font-semibold text-slate-200 flex items-center mb-4">
                <WrenchIcon className="w-5 h-5 mr-2 text-sky-400" />
                {t('dashboard.rackxSpendTitle')}
              </h4>
              <DonutChart data={
                spendByWorkshop.map((d, i) => ({
                  label: d.workshopName,
                  value: d.spend,
                  gradient: MONOTONE_BLUE_GRADIENTS[i % MONOTONE_BLUE_GRADIENTS.length]
                }))
              } valuePrefix="€" totalLabel={t('dashboard.donutTotal')} noDataLabel={t('dashboard.noData')} />
            </div>
          ) : (
            <div className="p-8 flex-grow flex items-center justify-center">
                <p className="text-slate-500">{t('dashboard.noSpendData')}</p>
            </div>
          )}
        </DashboardCard>
      </div>
    </div>
  );
};

export default ProgramDashboard;
