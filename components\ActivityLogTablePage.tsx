

import React, { useState, useMemo, useEffect } from 'react';
import { AppState, AuditLog } from '../types';
import { FilterIcon, FileDownIcon, ChevronsUpDownIcon, XIcon, CalendarIcon } from './icons';
import { api } from '../services/api';
import { useAppState } from '../context/AppContext';

const ITEMS_PER_PAGE = 15;

interface ActivityLogTablePageProps {}

const ActivityLogTablePage: React.FC<ActivityLogTablePageProps> = () => {
  const { appState } = useAppState();
  const { users, workshops } = appState!;
  
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [totalLogs, setTotalLogs] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  
  const [filters, setFilters] = useState<{ user?: number; type?: string; startDate?: string; endDate?: string, searchTerm?: string }>({});
  const [sortConfig, setSortConfig] = useState<{ key: keyof AuditLog | 'user'; direction: 'asc' | 'desc' }>({ key: 'created_at', direction: 'desc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedRow, setExpandedRow] = useState<number | null>(null);

  const userMap = useMemo(() => new Map(users.map(u => [u.id, u.name])), [users]);
  const workshopMap = useMemo(() => new Map(workshops.map(w => [w.id, w.name])), [workshops]);

  useEffect(() => {
    const fetchLogs = async () => {
        setIsLoading(true);
        const { logs: fetchedLogs, total } = await api.getAuditLog({
            page: currentPage,
            limit: ITEMS_PER_PAGE,
            sortKey: sortConfig.key,
            sortDirection: sortConfig.direction,
            filters: {
                userId: filters.user,
                actionType: filters.type,
                startDate: filters.startDate,
                endDate: filters.endDate,
                searchTerm: filters.searchTerm,
            }
        });
        setLogs(fetchedLogs);
        setTotalLogs(total);
        setIsLoading(false);
    };
    fetchLogs();
  }, [currentPage, sortConfig, filters]);

  const getLogMessage = (log: AuditLog): string => {
    const details = log.details as any;
    switch(log.action) {
      case 'TRANSFER_REQUEST_CREATE': return `Transfer request created for ${workshopMap.get(details.to) || 'a RackX'}.`;
      case 'TRANSFER_REQUEST_APPROVE': return `Approved transfer request #${details.requestId}.`;
      case 'CONSUMPTION_LOG_CREATE': return `Consumption log created from ${workshopMap.get(details.workshopId) || 'a RackX'}.`;
      case 'USER_CREATED': return `New user created: ${details.name}.`;
      case 'USER_UPDATED': return `Profile updated for user: ${details.name}.`;
      case 'DATA_PROCESSED': return `Processed import "${details.fileName}".`;
      default: return `Performed action: ${log.action}`;
    }
  };

  const totalPages = Math.ceil(totalLogs / ITEMS_PER_PAGE);

  const requestSort = (key: typeof sortConfig.key) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
    setCurrentPage(1);
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const processedValue = name === 'user' ? (value ? parseInt(value, 10) : undefined) : value;
    setFilters(prev => ({ ...prev, [name]: processedValue }));
    setCurrentPage(1);
  };
  
  const resetFilters = () => {
      setFilters({});
      setCurrentPage(1);
  }

  const exportToCsv = async () => {
    // Fetch all logs matching current filters for export
    const { logs: allLogs } = await api.getAuditLog({ filters, sortKey: sortConfig.key, sortDirection: sortConfig.direction });
    const headers = ['ID', 'Timestamp', 'User', 'Action', 'Message', 'Details'];
    const rows = allLogs.map(log => [
        log.id,
        new Date(log.created_at).toISOString(),
        `"${userMap.get(log.user_id) || 'Unknown'}"`,
        log.action,
        `"${getLogMessage(log).replace(/"/g, '""')}"`,
        `"${JSON.stringify(log.details).replace(/"/g, '""')}"`
    ].join(','));
    const csvString = [headers.join(','), ...rows].join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `rackx_activity_log_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
  };

  const uniqueActionTypes = useMemo(() => [...new Set(appState.auditLog.map(log => log.action))], [appState.auditLog]);

  const SortableHeader: React.FC<{ sortKey: typeof sortConfig.key, children: React.ReactNode}> = ({ sortKey, children }) => (
    <th scope="col" className="px-4 py-3">
        <button onClick={() => requestSort(sortKey)} className="flex items-center gap-1 font-semibold tracking-wider text-left w-full transition-colors hover:text-white">
            {children}
            <ChevronsUpDownIcon className={`w-4 h-4 text-slate-500 ${sortConfig.key === sortKey ? 'text-white' : ''}`} />
        </button>
    </th>
  )

  const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md py-2 px-3 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";

  return (
    <div className="animate-fade-in">
        <h3 className="text-xl font-semibold text-slate-200 mb-4">Activity Log Explorer</h3>
        <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700 mb-6">
            <h4 className="text-lg font-semibold text-slate-200 flex items-center mb-4"><FilterIcon className="w-5 h-5 mr-2" /> Filters</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <input type="text" name="searchTerm" placeholder="Search details..." value={filters.searchTerm || ''} onChange={handleFilterChange} className={inputClasses} />
                <select name="user" value={filters.user || ''} onChange={handleFilterChange} className={inputClasses}>
                    <option value="">All Users</option>
                    {users.map(u => <option key={u.id} value={u.id}>{u.name}</option>)}
                </select>
                <select name="type" value={filters.type || ''} onChange={handleFilterChange} className={inputClasses}>
                    <option value="">All Action Types</option>
                    {uniqueActionTypes.map(type => <option key={type} value={type}>{type}</option>)}
                </select>
                <div className="grid grid-cols-2 gap-2">
                    <div className="relative">
                        <CalendarIcon className="w-4 h-4 absolute top-1/2 left-3 -translate-y-1/2 text-slate-400"/>
                        <input type="date" name="startDate" value={filters.startDate || ''} onChange={handleFilterChange} className={`${inputClasses} pl-9`} title="Start Date"/>
                    </div>
                    <div className="relative">
                        <CalendarIcon className="w-4 h-4 absolute top-1/2 left-3 -translate-y-1/2 text-slate-400"/>
                         <input type="date" name="endDate" value={filters.endDate || ''} onChange={handleFilterChange} className={`${inputClasses} pl-9`} title="End Date"/>
                    </div>
                </div>
            </div>
             <div className="flex justify-end gap-3 mt-4">
                <button onClick={resetFilters} className="px-4 py-2 text-sm bg-slate-600 text-white font-semibold rounded-md hover:bg-slate-500 transition flex items-center gap-2"><XIcon className="w-4 h-4"/> Reset Filters</button>
                <button onClick={exportToCsv} className="flex items-center px-4 py-2 text-sm bg-green-700 text-white font-semibold rounded-md hover:bg-green-600 transition">
                    <FileDownIcon className="w-4 h-4 mr-2" />
                    Export CSV
                </button>
            </div>
        </div>

        <div className="overflow-x-auto bg-slate-900/50 rounded-lg border border-slate-700">
            <table className="w-full text-sm text-left text-slate-300">
                <thead className="text-xs text-slate-400 uppercase bg-slate-800">
                    <tr>
                        <SortableHeader sortKey="created_at">Timestamp</SortableHeader>
                        <SortableHeader sortKey="user">User</SortableHeader>
                        <SortableHeader sortKey="action">Action</SortableHeader>
                        <th scope="col" className="px-4 py-3 font-semibold tracking-wider">Message</th>
                    </tr>
                </thead>
                <tbody className={isLoading ? 'opacity-50' : ''}>
                    {logs.map(log => (
                        <React.Fragment key={log.id}>
                            <tr onClick={() => setExpandedRow(expandedRow === log.id ? null : log.id)} className="border-b border-slate-700 last:border-b-0 hover:bg-slate-700/50 transition-colors duration-200 cursor-pointer">
                                <td className="px-4 py-3 whitespace-nowrap">{new Date(log.created_at).toLocaleString()}</td>
                                <td className="px-4 py-3">{userMap.get(log.user_id) || 'Unknown'}</td>
                                <td className="px-4 py-3 font-mono">{log.action}</td>
                                <td className="px-4 py-3">{getLogMessage(log)}</td>
                            </tr>
                            {expandedRow === log.id && (
                                <tr className="bg-slate-900">
                                    <td colSpan={4} className="p-4">
                                        <h4 className="font-semibold text-slate-200 mb-2">Log Details:</h4>
                                        <pre className="text-xs bg-slate-800 p-3 rounded-md text-amber-300 whitespace-pre-wrap font-mono">{JSON.stringify(log.details, null, 2)}</pre>
                                    </td>
                                </tr>
                            )}
                        </React.Fragment>
                    ))}
                </tbody>
            </table>
            {logs.length === 0 && !isLoading && <p className="text-center py-8 text-slate-400">No logs match the current filters.</p>}
            {isLoading && <p className="text-center py-8 text-slate-400 animate-pulse-subtle">Loading logs...</p>}
        </div>

        <div className="flex justify-between items-center mt-4 text-sm text-slate-400">
            <span>Page {currentPage} of {totalPages} ({totalLogs} total logs)</span>
            <div className="flex gap-2">
                <button onClick={() => setCurrentPage(p => Math.max(1, p - 1))} disabled={currentPage === 1 || isLoading} className="px-3 py-1 bg-slate-600 text-white font-semibold rounded-md disabled:opacity-50 transition">Previous</button>
                <button onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))} disabled={currentPage === totalPages || isLoading} className="px-3 py-1 bg-slate-600 text-white font-semibold rounded-md disabled:opacity-50 transition">Next</button>
            </div>
        </div>
    </div>
  );
};

export default ActivityLogTablePage;