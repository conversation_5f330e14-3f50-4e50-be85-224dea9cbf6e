
import React, { useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Workshop } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { WrenchIcon, SendIcon, ArrowLeftIcon } from './icons';

const WorkshopListPage: React.FC = () => {
  const { appState } = useAppState();
  const { openRequestModal } = useAppActions();
  const navigate = useNavigate();
  
  const { workshops, users, requests, currentUser } = appState!;

  // For non-admins, go directly to their assigned RackX detail page.
  useEffect(() => {
    if (currentUser.role !== 'Admin' && currentUser.role !== 'Manager') {
      const myWorkshopId = currentUser.primary_workshop_id;
      if (myWorkshopId) {
        navigate(`/workshops/${myWorkshopId}`, { replace: true });
      }
    }
  }, [currentUser, navigate]);

  if (currentUser.role !== 'Admin' && currentUser.role !== 'Manager') {
    // Ren<PERSON> a fallback while redirecting, or if user has no workshop.
    return (
        <div className="animate-fade-in max-w-7xl mx-auto text-center py-16">
            <h2 className="text-2xl font-bold text-slate-100">No Workshop Assigned</h2>
            <p className="text-slate-400 mt-2">You are not currently assigned to a Workshop. Please contact an administrator.</p>
        </div>
    );
  }
  
  const workshopsWithData = useMemo(() => {
    return workshops.map(workshop => {
      const team = users.filter(u => u.primary_workshop_id === workshop.id);
      const teamLead = team.find(u => u.role === 'TeamLeader');
      const pendingReqs = requests.filter(r => (r.to_workshop_id === workshop.id || r.workshop_id === workshop.id) && r.status === 'Pending');
      
      return {
        ...workshop,
        teamSize: team.length,
        teamLead: teamLead?.name || 'N/A',
        pendingRequests: pendingReqs.length,
      }
    })
  }, [workshops, users, requests]);

  const onSelectWorkshop = (workshop: Workshop) => {
    navigate(`/workshops/${workshop.id}`);
  };

  const renderWorkshopTable = (workshopList: typeof workshopsWithData) => {
    if (workshopList.length === 0) {
      return <p className="text-slate-400 text-center py-8">No Local Workshops have been created.</p>;
    }

    return (
      <div className="overflow-x-auto bg-slate-900/50 rounded-lg border border-slate-700">
        <table className="w-full text-sm text-left text-slate-300">
          <thead className="text-xs text-slate-400 uppercase bg-slate-800">
            <tr>
              <th scope="col" className="px-6 py-3 font-semibold tracking-wider">Workshop Name</th>
              <th scope="col" className="px-6 py-3 font-semibold tracking-wider">TeamLeader</th>
              <th scope="col" className="px-6 py-3 font-semibold tracking-wider text-center">Team Size</th>
              <th scope="col" className="px-6 py-3 font-semibold tracking-wider text-center">Pending Requests</th>
              <th scope="col" className="px-6 py-3"><span className="sr-only">View</span></th>
            </tr>
          </thead>
          <tbody>
            {workshopList.map(workshop => (
              <tr key={workshop.id} className="border-b border-slate-700 last:border-b-0 hover:bg-slate-700/50 transition-colors duration-200">
                <td className="px-6 py-4 font-medium text-slate-100 whitespace-nowrap">{workshop.name}</td>
                <td className="px-6 py-4">{workshop.teamLead}</td>
                <td className="px-6 py-4 text-center">{workshop.teamSize}</td>
                <td className={`px-6 py-4 text-center font-bold ${workshop.pendingRequests > 0 ? 'text-amber-400 animate-pulse-subtle' : ''}`}>{workshop.pendingRequests}</td>
                <td className="px-6 py-4 text-right">
                  <button onClick={() => onSelectWorkshop(workshop)} className="text-sky-400 hover:text-sky-300 font-semibold text-xs flex items-center">
                    View
                    <ArrowLeftIcon className="w-3 h-3 ml-1 transform rotate-180" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="animate-fade-in max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
            <WrenchIcon className="w-6 h-6 mr-3 text-sky-400" />
            <h2 className="text-2xl font-bold text-slate-100">Workshop Management</h2>
        </div>
        <button
            onClick={openRequestModal}
            className="flex items-center px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 focus:ring-brand-accent"
        >
            <SendIcon className="w-5 h-5 mr-2" />
            Request Item Transfer
        </button>
      </div>
      
      <div className="space-y-8">
        <div>
          <h3 className="text-xl font-semibold text-slate-200 mb-4">Central Workshop</h3>
          {renderWorkshopTable(workshopsWithData.filter(w => w.type === 'Central'))}
        </div>
        <div>
          <h3 className="text-xl font-semibold text-slate-200 mb-4">Local Workshops</h3>
          {renderWorkshopTable(workshopsWithData.filter(w => w.type === 'Local'))}
        </div>
      </div>
    </div>
  );
};

export default WorkshopListPage;