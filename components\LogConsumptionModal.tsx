import React, { useState, FormEvent, useEffect } from 'react';
import { XIcon, SendIcon } from './icons';
import { useTranslation } from '../i18n/I18nProvider';

interface LogConsumptionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (reason: string) => void;
}

const OPERATOR_PURPOSES = ['Fabrication', 'Assembly', 'Repair', 'Maintenance', 'General Use'] as const;
type OperatorPurpose = typeof OPERATOR_PURPOSES[number];

const LogConsumptionModal: React.FC<LogConsumptionModalProps> = ({ isOpen, onClose, onSubmit }) => {
    const { t } = useTranslation();
    const [purpose, setPurpose] = useState<OperatorPurpose>('Fabrication');
    const [orderId, setOrderId] = useState('');
    const [duration, setDuration] = useState('');
    const [notes, setNotes] = useState('');
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    
    useEffect(() => {
        if(isOpen) {
            setPurpose('Fabrication');
            setOrderId('');
            setDuration('');
            setNotes('');
            setErrors({});
        }
    }, [isOpen]);

    if (!isOpen) return null;

    const validate = () => {
        const tempErrors: { [key: string]: string } = {};
        if ((purpose === 'Fabrication' || purpose === 'Assembly' || purpose === 'Repair' || purpose === 'Maintenance') && !orderId.trim()) {
            tempErrors.orderId = 'An Order or Work ID is required for this purpose.';
        }
        if (purpose === 'Fabrication' && (!duration.trim() || Number(duration) <= 0)) {
            tempErrors.duration = 'A positive duration in hours is required for fabrication.';
        }
        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (validate()) {
            const reasonParts = [`Purpose: ${t(`logConsumption.purpose_${purpose.replace(' ', '_')}` as any)}`];
            if (orderId.trim()) {
                const label = purpose === 'Fabrication' ? t('logConsumption.fabOrderId') : t('logConsumption.orderId');
                reasonParts.push(`${label}: ${orderId.trim()}`);
            }
            if (purpose === 'Fabrication' && duration.trim()) {
                reasonParts.push(`${t('logConsumption.duration')}: ${duration.trim()}`);
            }
            if (notes.trim()) {
                reasonParts.push(`${t('logConsumption.notes')}: ${notes.trim()}`);
            }
            const reasonString = reasonParts.join('\n');
            onSubmit(reasonString);
        }
    }
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        if(name === 'purpose') setPurpose(value as OperatorPurpose);
        if(name === 'orderId') setOrderId(value);
        if(name === 'duration') setDuration(value);
        if(name === 'notes') setNotes(value);
        if (errors[name]) setErrors(prev => ({...prev, [name]: ''}));
    };

    let orderLabel = '';
    if (purpose === 'Fabrication') orderLabel = t('logConsumption.fabOrderId');
    if (purpose === 'Assembly' || purpose === 'Repair' || purpose === 'Maintenance') orderLabel = t('logConsumption.orderId');

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            onClick={onClose}
        >
            <div 
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-md relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">{t('logConsumption.modalTitle')}</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label={t('close')}>
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>
                
                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4">
                        <p className="text-sm text-slate-400">{t('logConsumption.description')}</p>
                        <div>
                            <label htmlFor="purpose" className={labelClasses}>{t('logConsumption.purpose')}</label>
                            <select id="purpose" name="purpose" value={purpose} onChange={handleChange} className={inputClasses}>
                                {OPERATOR_PURPOSES.map(p => <option key={p} value={p}>{t(`logConsumption.purpose_${p.replace(' ', '_')}` as any)}</option>)}
                            </select>
                        </div>
                        {orderLabel && (
                             <div className="animate-fade-in">
                                <label htmlFor="orderId" className={labelClasses}>{orderLabel}</label>
                                <input type="text" id="orderId" name="orderId" value={orderId} onChange={handleChange} className={inputClasses} placeholder="e.g., FO-1701 or WO-2983" />
                                {errors.orderId && <p className="text-red-400 text-xs mt-1">{errors.orderId}</p>}
                            </div>
                        )}
                         {purpose === 'Fabrication' && (
                            <div className="animate-fade-in">
                                <label htmlFor="duration" className={labelClasses}>{t('logConsumption.duration')}</label>
                                <input type="number" id="duration" name="duration" min="0" step="0.5" value={duration} onChange={handleChange} className={inputClasses} placeholder="e.g., 8"/>
                                {errors.duration && <p className="text-red-400 text-xs mt-1">{errors.duration}</p>}
                            </div>
                        )}
                        <div>
                            <label htmlFor="notes" className={labelClasses}>{t('logConsumption.notes')}</label>
                            <textarea id="notes" name="notes" value={notes} onChange={handleChange} className={`${inputClasses} min-h-[80px]`} placeholder={t('logConsumption.notesPlaceholder')}/>
                        </div>
                    </div>
                    
                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-600 text-white font-semibold rounded-md shadow-md hover:bg-slate-500 transition">
                            {t('cancel')}
                        </button>
                        <button type="submit" className="flex items-center gap-2 px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                            <SendIcon className="w-5 h-5" />
                            {t('logConsumption.submit')}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default LogConsumptionModal;