import React, { useState } from 'react';
import { SettingsIcon, UsersIcon, PlugZapIcon, BookOpenIcon, InfoIcon } from './icons';
import { UserType, Page } from '../types';
import UserManagementPage from './UserManagementPage';
import AddUserModal from './AddUserModal';
import { useAppState, useAppActions } from '../context/AppContext';
import AboutPage from './AboutPage';

type Tab = 'general' | 'erp' | 'about' | 'users';

const SettingsPage: React.FC = () => {
  const { appState } = useAppState();
  const { handleUpdateUser } = useAppActions();
  const { currentUser } = appState!;
  const [activeTab, setActiveTab] = useState<Tab>('general');
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false); // TeamLeaders cannot add users, this is for future extension

  const TabButton: React.FC<{ tabId: Tab; label: string, icon: React.ReactNode }> = ({ tabId, label, icon }) => {
    const isActive = activeTab === tabId;
    return (
      <button
        onClick={() => setActiveTab(tabId)}
        className={`flex items-center px-3 py-2 text-sm font-semibold rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-brand-accent ${
          isActive
            ? 'bg-brand-accent text-brand-text-on-accent'
            : 'text-slate-300 hover:bg-slate-700'
        }`}
        role="tab"
        aria-selected={isActive}
      >
        {icon}
        <span className="ml-2">{label}</span>
      </button>
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div role="tabpanel" className="animate-fade-in">
            <h3 className="text-lg font-semibold text-slate-200">General Settings</h3>
            <p className="text-slate-400 mt-2">
              General application settings and preferences for your account will be managed here. This section is currently under development.
            </p>
          </div>
        );
      case 'erp':
        return (
          <div role="tabpanel" className="animate-fade-in">
            <h3 className="flex items-center text-lg font-semibold text-slate-200">
              <PlugZapIcon className="w-5 h-5 mr-2" />
              ERP Integration
            </h3>
            <p className="text-slate-400 mt-2">
              Connect to your company's ERP system to sync inventory data automatically. Configuration options will appear here once connected.
            </p>
          </div>
        );
      case 'about':
        return <AboutPage />;
      case 'users':
        if (currentUser.role !== 'TeamLeader' && currentUser.role !== 'Supervisor' && currentUser.role !== 'Line Leader') return null;
        return <UserManagementPage 
                  onOpenAddUserModal={() => setIsAddUserModalOpen(true)}
                />;
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex flex-col h-full">
        <div className="[background-image:var(--card-bg-panel)] backdrop-blur-md rounded-lg shadow-lg border border-slate-800 overflow-hidden animate-fade-in flex flex-col flex-grow">
          <div className="p-4 sm:p-6 border-b border-slate-700/50 flex-shrink-0">
            <div className="flex items-center">
              <SettingsIcon className="w-8 h-8 mr-3 text-brand-accent" />
              <h2 className="text-3xl font-bold text-slate-100">Settings</h2>
            </div>
          </div>
          <div className="p-4 border-b border-slate-700/50 flex-shrink-0">
            <div className="flex items-center space-x-2 flex-wrap gap-y-2" role="tablist" aria-label="Settings Tabs">
              <TabButton tabId="general" label="General" icon={<SettingsIcon className="w-4 h-4" />} />
              <TabButton tabId="erp" label="ERP" icon={<PlugZapIcon className="w-4 h-4" />} />
              {(currentUser.role === 'TeamLeader' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') && <TabButton tabId="users" label="Users" icon={<UsersIcon className="w-4 h-4" />} />}
              <TabButton tabId="about" label="About" icon={<InfoIcon className="w-4 h-4" />} />
            </div>
          </div>
          <div className="overflow-y-auto flex-grow p-6 sm:p-8">
            {renderContent()}
          </div>
        </div>
      </div>
      
      {/* AddUserModal is not used by TeamLeaders but kept for potential future functionality */}
       <AddUserModal
        isOpen={isAddUserModalOpen}
        onClose={() => setIsAddUserModalOpen(false)}
        onSave={() => { /* Not implemented for non-admins */ }}
      />
    </>
  );
};

export default SettingsPage;