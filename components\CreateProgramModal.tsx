import React, { useState, FormEvent, useEffect } from 'react';
import { Program } from '../types';
import { XIcon } from './icons';

interface CreateProgramModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (programData: Omit<Program, 'id'>) => void;
}

const CreateProgramModal: React.FC<CreateProgramModalProps> = ({ isOpen, onClose, onSave }) => {
    const [programName, setProgramName] = useState('');
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (isOpen) {
            setProgramName('');
            setError(null);
        }
    }, [isOpen]);

    if (!isOpen) return null;

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (!programName.trim()) {
            setError('Program name is required.');
            return;
        }
        onSave({ name: programName.trim() });
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-md relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Create New Parent Program</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6">
                        <div>
                            <label htmlFor="name" className={labelClasses}>Parent Program Name</label>
                            <input type="text" id="name" name="name" value={programName} onChange={(e) => setProgramName(e.target.value)} className={inputClasses} required />
                        </div>
                        {error && <p className="text-status-error text-xs mt-1">{error}</p>}
                    </div>

                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">Cancel</button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Create Program</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default CreateProgramModal;
