import { GoogleGenAI, Type } from "@google/genai";
import { Workshop, WorkshopInventoryItem, CatalogItem, Request, AISuggestion } from '../types';

// This guard is important to prevent crashes in environments where env vars aren't defined.
const API_KEY = process.env.API_KEY;
const ai = API_KEY ? new GoogleGenAI({ apiKey: API_KEY }) : null;

const suggestionSchema = {
    type: Type.OBJECT,
    properties: {
        suggestions: {
            type: Type.ARRAY,
            description: "List of replenishment suggestions.",
            items: {
                type: Type.OBJECT,
                properties: {
                    item_id: { type: Type.INTEGER, description: "The ID of the catalog item." },
                    quantity: { type: Type.INTEGER, description: "The suggested quantity to reorder." },
                    reason: { type: Type.STRING, description: "A brief justification for the suggestion. For example: 'Low stock' or 'High recent usage'." }
                },
                required: ["item_id", "quantity", "reason"]
            }
        }
    }
};


export const getSmartReplenishmentSuggestion = async (
    rackx: Workshop,
    rackxInventory: (WorkshopInventoryItem & { catalogItem?: CatalogItem })[],
    centralInventory: (WorkshopInventoryItem & { catalogItem?: CatalogItem })[],
    recentRequests: Request[]
): Promise<AISuggestion[]> => {

    if (!ai) {
        console.error("Gemini API key not configured.");
        throw new Error("The AI service is not configured. Please set the API_KEY environment variable.");
    }

    const systemInstruction = "You are a logistics and inventory management expert for an aerospace manufacturing company. Your task is to analyze inventory data for a specific workshop (a 'RackX') and suggest a replenishment order from the central warehouse. Provide your response in JSON format according to the provided schema. Only suggest items that are available in the central warehouse. Base your suggestions on current stock, low stock thresholds, and recent consumption patterns. Prioritize items that are critically low. Do not suggest items that the workshop already has in sufficient quantity or that are not available in the central warehouse.";

    const promptData = {
        rackxToRestock: {
            id: rackx.id,
            name: rackx.name,
        },
        rackxInventoryStatus: rackxInventory.map(item => ({
            item_id: item.item_id,
            name: item.catalogItem?.name,
            current_stock: item.quantity,
            low_stock_threshold: item.low_stock_threshold,
        })),
        centralWarehouseAvailability: centralInventory.map(item => ({
            item_id: item.item_id,
            name: item.catalogItem?.name,
            available_stock: item.quantity,
        })),
        recentActivity: recentRequests.map(req => ({
            type: req.type,
            status: req.status,
            date: req.created_at,
            items: req.items.map(item => ({
                item_id: item.item_id,
                name: centralInventory.find(ci => ci.item_id === item.item_id)?.catalogItem?.name || 'Unknown',
                quantity: item.quantity,
            }))
        })),
    };

    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: `Analyze the following inventory data and provide replenishment suggestions:\n${JSON.stringify(promptData)}`,
        config: {
            responseMimeType: "application/json",
            responseSchema: suggestionSchema,
        }
    });
    
    const jsonStr = response.text.trim();
    if (!jsonStr) {
        return [];
    }

    const result = JSON.parse(jsonStr);
    
    if (!result.suggestions) {
        return [];
    }

    // Enrich suggestions with catalog data for easier display
    const catalogMap = new Map(centralInventory.map(i => [i.item_id, i.catalogItem]));
    const centralStockMap = new Map(centralInventory.map(i => [i.item_id, i.quantity]));

    const enrichedSuggestions = result.suggestions
        .map((s: any) => {
            const catalogItem = catalogMap.get(s.item_id);
            const centralStock = centralStockMap.get(s.item_id) || 0;
            if (!catalogItem || centralStock < s.quantity) return null; // Filter out invalid or out-of-stock items

            return {
                ...s,
                name: catalogItem.name,
                sku: catalogItem.sku,
                stock: centralStock,
            };
        })
        .filter(Boolean) as AISuggestion[]; // Filter out nulls

    return enrichedSuggestions;
};
