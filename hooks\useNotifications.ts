import { useState, useEffect, useMemo } from 'react';
import { AppState, AuditLog, UserType, CatalogItem, Workshop, Request } from '../types';

const NOTIFICATIONS_STORAGE_KEY = 'rackx-read-notifications';

export interface UINotification {
    id: number;
    type: string;
    payload: any;
    createdAt: string;
    isRead: boolean;
}

const getReadNotificationIds = (): Set<number> => {
    try {
        const item = localStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
        if (!item) return new Set<number>();

        const parsedItem: unknown = JSON.parse(item);
        if (Array.isArray(parsedItem)) {
            return new Set<number>(parsedItem.filter((id): id is number => typeof id === 'number'));
        }
        return new Set<number>();
    } catch (error) {
        console.error('Error reading read notifications from localStorage', error);
        return new Set<number>();
    }
}

const setReadNotificationIds = (ids: Set<number>) => {
    try {
        localStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(Array.from(ids)));
    } catch (error) {
        console.error('Error writing read notifications to localStorage', error);
    }
}

export const useNotifications = (appState: AppState) => {
    const { auditLog, currentUser, users, workshops, catalog, requests } = appState;
    const [readIds, setReadIds] = useState<Set<number>>(getReadNotificationIds());

    const userMap = useMemo(() => new Map(users.map(u => [u.id, u])), [users]);
    const catalogMap = useMemo(() => new Map(catalog.map(c => [c.id, c])), [catalog]);

    const notifications = useMemo(() => {
        const generatedNotifications: UINotification[] = [];

        auditLog.forEach(log => {
            const actor = userMap.get(log.user_id);
            // Don't notify users about their own actions
            if (!actor || actor.id === currentUser.id) {
                return;
            }

            const details = log.details as any;
            let relevantNotificationPayload: { type: string, payload: any } | null = null;
            
            switch (log.action) {
                case 'TRANSFER_REQUEST_APPROVE':
                case 'TRANSFER_REQUEST_REJECT':
                case 'CONSUMPTION_LOG_APPROVE':
                case 'CONSUMPTION_LOG_REJECT': {
                    const request = requests.find(r => r.id === details.requestId);
                    if (request && request.created_by_user_id === currentUser.id) {
                        const actionText = log.action.includes('APPROVE') ? 'approved' : 'rejected';
                        
                        let requestSummaryKey = 'notifications.requestSummaryDefault';
                        let summaryOptions: any = {};

                        if (request.type === 'TRANSFER' && request.items.length > 0) {
                            const mainItem = request.items[0];
                            const catalogEntry = catalogMap.get(mainItem.item_id);
                            requestSummaryKey = 'notifications.requestSummaryTransfer';
                            summaryOptions = { quantity: mainItem.quantity, itemName: catalogEntry?.name || 'item' };
                        } else if (request.type === 'CONSUMPTION') {
                            requestSummaryKey = 'notifications.requestSummaryConsumption';
                            summaryOptions = { count: request.items.length };
                        }

                        relevantNotificationPayload = {
                            type: 'REQUEST_STATUS_CHANGED',
                            payload: {
                                actorName: actor.name,
                                action: actionText,
                                requestSummaryKey,
                                summaryOptions
                            }
                        };
                    }
                    break;
                }
                case 'USER_UPDATED': {
                    if (details.userId === currentUser.id) {
                         relevantNotificationPayload = {
                            type: 'USER_PROFILE_UPDATED',
                            payload: { actorName: actor.name }
                         };
                    } else if (currentUser.role === 'TeamLeader') {
                        const updatedUser = userMap.get(details.userId);
                        if(updatedUser && updatedUser.primary_workshop_id === currentUser.primary_workshop_id) {
                             relevantNotificationPayload = {
                                type: 'USER_ASSIGNED',
                                payload: {
                                    actorName: actor.name,
                                    assignedUserName: details.name
                                }
                             };
                        }
                    }
                    break;
                }
            }

            if (relevantNotificationPayload) {
                generatedNotifications.push({
                    id: log.id,
                    type: relevantNotificationPayload.type,
                    payload: relevantNotificationPayload.payload,
                    createdAt: log.created_at,
                    isRead: readIds.has(log.id)
                });
            }
        });

        return generatedNotifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }, [auditLog, currentUser, readIds, userMap, catalogMap, requests]);

    const unreadCount = useMemo(() => notifications.filter(n => !n.isRead).length, [notifications]);

    const markAllAsRead = () => {
        const allIds = new Set<number>(notifications.map(n => n.id));
        setReadIds(allIds);
        setReadNotificationIds(allIds);
    };

    const markOneAsRead = (id: number) => {
        const newIds = new Set<number>(readIds);
        newIds.add(id);
        setReadIds(newIds);
        setReadNotificationIds(newIds);
    }
    
    // Clean up old read notifications from localStorage occasionally
    useEffect(() => {
        const allCurrentIds = new Set(auditLog.map(log => log.id));
        const keptReadIds = Array.from(readIds).filter((id: any): id is number => {
            return typeof id === 'number' && allCurrentIds.has(id);
        });
        if (keptReadIds.length !== readIds.size) {
            const newReadIds = new Set<number>(keptReadIds);
            setReadIds(newReadIds);
            setReadNotificationIds(newReadIds);
        }
    }, [auditLog, readIds]);


    return { notifications, unreadCount, markAllAsRead, markOneAsRead };
};