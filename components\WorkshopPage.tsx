


import React from 'react';
import { useAppState } from '../context/AppContext';
import { WrenchIcon } from './icons';
import MyRackxPage from './MyRackxPage';

const WorkshopPage: React.FC = () => {
    const { selectedWorkshopId } = useAppState();
    
    if (!selectedWorkshopId) {
        return (
            <div className="flex flex-col h-full">
                <div className="bg-slate-800/50 rounded-lg shadow-lg border border-slate-700 overflow-hidden animate-fade-in flex flex-col flex-grow items-center justify-center">
                    <div className="text-center p-8">
                        <WrenchIcon className="mx-auto h-12 w-12 text-slate-500" />
                        <h2 className="mt-4 text-xl font-bold text-slate-100">Browse Workshops</h2>
                        <p className="mt-1 text-slate-400">Select a specific Workshop from the "Browse RackX" menu in the sidebar to view its details.</p>
                    </div>
                </div>
            </div>
        );
    }
    
    // This page acts as a viewer for any selected workshop.
    // We reuse the MyRackxPage component, which contains the full detail view logic.
    return <MyRackxPage workshopToShowId={selectedWorkshopId} />;
};

export default WorkshopPage;