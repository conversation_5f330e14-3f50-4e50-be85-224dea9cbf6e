import React, { useState, useEffect } from 'react';

const DashboardSettings = () => {
  const [tabSettings, setTabSettings] = useState({
    DAHER: true,
    Sabca: true,
    Pilatus: true,
  });

  useEffect(() => {
    const savedSettings = localStorage.getItem('dashboardTabSettings');
    if (savedSettings) {
      setTabSettings(JSON.parse(savedSettings));
    }
  }, []);

  const handleCheckboxChange = (tabName) => {
    const newSettings = {
      ...tabSettings,
      [tabName]: !tabSettings[tabName],
    };
    setTabSettings(newSettings);
    localStorage.setItem('dashboardTabSettings', JSON.stringify(newSettings));
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-2">Dashboard Tab Visibility</h3>
      <p className="text-sm text-slate-400 mb-4">
        Use these settings to show or hide program-specific tabs on the main dashboard. Changes are saved locally to your browser.
      </p>
      <div className="space-y-2">
        {Object.keys(tabSettings).map((tabName) => (
          <label key={tabName} className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={tabSettings[tabName]}
              onChange={() => handleCheckboxChange(tabName)}
              className="form-checkbox h-5 w-5 text-blue-600"
            />
            <span className="text-gray-200">{tabName}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default DashboardSettings;
