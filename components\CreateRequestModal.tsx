import React, { useState, FormEvent, useEffect, useMemo, useCallback } from 'react';
import { Request, AppState } from '../types';
import { XIcon } from './icons';
import { useAppState, useAppActions } from '../context/AppContext';

interface CreateRequestModalProps {
    isOpen: boolean;
    onClose: () => void;
    onCreateRequest: (requestData: Omit<Request, 'id' | 'status' | 'created_at' | 'created_by_user_id'>) => void;
}

// Kept for non-operator roles
const STANDARD_REASON_TYPES = ['General Stock', 'Fabrication Order', 'Project / Task', 'Other'] as const;
type StandardReasonType = typeof STANDARD_REASON_TYPES[number];

// New, more specific purpose choices for operators
const OPERATOR_PURPOSES = ['Fabrication', 'Assembly', 'Repair', 'Other'] as const;
type OperatorPurpose = typeof OPERATOR_PURPOSES[number];

const CreateRequestModal: React.FC<CreateRequestModalProps> = ({ isOpen, onClose, onCreateRequest }) => {
    const { appState } = useAppState();
    
    // Guard against rendering if state is not ready.
    if (!isOpen || !appState) {
        return null;
    }

    const { currentUser, workshops, catalog, workshopInventories } = appState;
    const isOperator = currentUser.role === 'Operator';
    const isTeamLeader = currentUser.role === 'TeamLeader';

    const getInitialState = useCallback(() => ({
        to_workshop_id: String(currentUser.primary_workshop_id || ''),
        item_id: '',
        quantity: 1,
        // For non-operators
        reasonType: 'General Stock' as StandardReasonType,
        reasonDetail: '',
        reasonNotes: '',
        // For operators
        purpose: 'Fabrication' as OperatorPurpose,
        orderId: '',
        duration: '',
    }), [currentUser.primary_workshop_id]);

    const [request, setRequest] = useState(getInitialState());
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    
    useEffect(() => {
        if (isOpen) {
            setRequest(getInitialState());
            setErrors({});
        }
    }, [isOpen, getInitialState]);

    const availableItems = useMemo(() => {
        const centralInventory = workshopInventories.filter(wi => wi.workshop_id === 1 && wi.quantity > 0);
        
        let userInventoryItemIds: Set<number> | null = null;
        if (isOperator || isTeamLeader) {
            const userInventory = workshopInventories.filter(wi => wi.workshop_id === currentUser.primary_workshop_id);
            userInventoryItemIds = new Set(userInventory.map(wi => wi.item_id));
        }

        return centralInventory
            .map(wi => {
                if (userInventoryItemIds && userInventoryItemIds.has(wi.item_id)) {
                    return null;
                }
                const catalogItem = catalog.find(ci => ci.id === wi.item_id);
                if (!catalogItem) return null;
                return { ...catalogItem, stock: wi.quantity };
            })
            .filter(Boolean) as (any & { stock: number })[];
    }, [workshopInventories, catalog, currentUser, isOperator, isTeamLeader]);
    
    const validate = () => {
        const tempErrors: { [key: string]: string } = {};
        if (!request.to_workshop_id) tempErrors.to_workshop_id = 'Destination RackX is required.';
        if (!request.item_id) tempErrors.item_id = 'You must select an item.';
        if (request.quantity < 1) tempErrors.quantity = 'Quantity must be at least 1.';
        
        const selectedItem = availableItems.find(i => i.id === parseInt(request.item_id, 10));
        if (selectedItem && request.quantity > selectedItem.stock) {
            tempErrors.quantity = `Not enough stock. Only ${selectedItem.stock} available.`;
        }
        
        if (isOperator) {
            if ((request.purpose === 'Fabrication' || request.purpose === 'Assembly' || request.purpose === 'Repair') && !request.orderId.trim()) {
                tempErrors.orderId = 'An Order ID is required for this purpose.';
            }
            if (request.purpose === 'Fabrication' && (!request.duration.trim() || Number(request.duration) <= 0)) {
                tempErrors.duration = 'A positive duration in hours is required.';
            }
        } else {
            if ((request.reasonType === 'Fabrication Order' || request.reasonType === 'Project / Task') && !request.reasonDetail.trim()) {
                tempErrors.reasonDetail = 'An identifier is required for this reason type.';
            }
        }

        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (validate()) {
            let reasonString = '';
            if (isOperator) {
                const reasonParts = [`Purpose: ${request.purpose}`];
                if (request.orderId.trim()) {
                    const label = request.purpose === 'Fabrication' ? 'Fabrication Order #' : 'Work Order #';
                    reasonParts.push(`${label}: ${request.orderId.trim()}`);
                }
                if (request.purpose === 'Fabrication' && request.duration.trim()) {
                    reasonParts.push(`Est. Duration (hrs): ${request.duration.trim()}`);
                }
                if (request.reasonNotes.trim()) {
                    reasonParts.push(`Notes: ${request.reasonNotes.trim()}`);
                }
                reasonString = reasonParts.join('\n');
            } else {
                const reasonParts = [`Request Type: ${request.reasonType}`];
                if (request.reasonDetail.trim()) {
                    const detailLabel = request.reasonType === 'Fabrication Order' ? 'Order ID' : 'Project/Task ID';
                    reasonParts.push(`${detailLabel}: ${request.reasonDetail.trim()}`);
                }
                if (request.reasonNotes.trim()) {
                    reasonParts.push(`Notes: ${request.reasonNotes.trim()}`);
                }
                reasonString = reasonParts.join('\n');
            }

            onCreateRequest({
                type: 'TRANSFER',
                from_workshop_id: 1, // Central
                to_workshop_id: parseInt(request.to_workshop_id, 10),
                items: [{
                    item_id: parseInt(request.item_id, 10),
                    quantity: request.quantity,
                }],
                reason: reasonString,
            });
        }
    };
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        const processedValue = name === 'quantity' ? parseInt(value, 10) || 1 : value;

        setRequest(prev => {
            const newState = { ...prev, [name]: processedValue };
            // Reset dependent fields when purpose/reasonType changes
            if (name === 'purpose' || name === 'reasonType') {
                newState.orderId = '';
                newState.duration = '';
                newState.reasonDetail = '';
            }
            return newState;
        });

        // Clear errors for fields that are being changed
        if (errors[name]) {
            setErrors(prev => {
                const newErrors = {...prev};
                delete newErrors[name as keyof typeof errors];
                return newErrors;
            });
        }
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition disabled:opacity-60 disabled:cursor-not-allowed";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    const renderOperatorForm = () => {
        let orderLabel = '';
        if (request.purpose === 'Fabrication') orderLabel = 'Fabrication Order #';
        if (request.purpose === 'Assembly' || request.purpose === 'Repair') orderLabel = 'Work Order #';

        return (
            <div className="space-y-4">
                <div>
                    <label htmlFor="purpose" className={labelClasses}>Purpose</label>
                    <select id="purpose" name="purpose" value={request.purpose} onChange={handleChange} className={inputClasses}>
                        {OPERATOR_PURPOSES.map(p => <option key={p} value={p}>{p}</option>)}
                    </select>
                </div>
                {orderLabel && (
                     <div className="animate-fade-in">
                        <label htmlFor="orderId" className={labelClasses}>{orderLabel}</label>
                        <input type="text" id="orderId" name="orderId" value={request.orderId} onChange={handleChange} className={inputClasses} placeholder="e.g., FO-1701" />
                        {errors.orderId && <p className="text-status-error text-xs mt-1">{errors.orderId}</p>}
                    </div>
                )}
                 {request.purpose === 'Fabrication' && (
                    <div className="animate-fade-in">
                        <label htmlFor="duration" className={labelClasses}>Est. Duration (hours)</label>
                        <input type="number" id="duration" name="duration" min="0" step="0.5" value={request.duration} onChange={handleChange} className={inputClasses} placeholder="e.g., 8"/>
                        {errors.duration && <p className="text-status-error text-xs mt-1">{errors.duration}</p>}
                    </div>
                )}
                <div>
                    <label htmlFor="reasonNotes" className={labelClasses}>Additional Notes (Optional)</label>
                    <textarea id="reasonNotes" name="reasonNotes" value={request.reasonNotes} onChange={handleChange} className={`${inputClasses} min-h-[80px]`} placeholder="Add any other relevant details..."/>
                </div>
            </div>
        );
    };

    const renderStandardForm = () => {
         const getReasonDetailLabel = () => {
            if (request.reasonType === 'Fabrication Order') return 'Fabrication Order ID';
            if (request.reasonType === 'Project / Task') return 'Project / Task ID';
            return '';
        };

        return (
            <div className="space-y-4">
                 <div>
                    <label htmlFor="reasonType" className={labelClasses}>Reason for Request</label>
                    <select id="reasonType" name="reasonType" value={request.reasonType} onChange={handleChange} className={inputClasses}>
                        {STANDARD_REASON_TYPES.map(type => <option key={type} value={type}>{type}</option>)}
                    </select>
                </div>
                {(request.reasonType === 'Fabrication Order' || request.reasonType === 'Project / Task') && (
                    <div className="animate-fade-in">
                        <label htmlFor="reasonDetail" className={labelClasses}>{getReasonDetailLabel()}</label>
                        <input type="text" id="reasonDetail" name="reasonDetail" value={request.reasonDetail} onChange={handleChange} className={inputClasses} placeholder="e.g., FO-1701"/>
                        {errors.reasonDetail && <p className="text-status-error text-xs mt-1">{errors.reasonDetail}</p>}
                    </div>
                )}
                <div>
                    <label htmlFor="reasonNotes" className={labelClasses}>Additional Notes (Optional)</label>
                    <textarea id="reasonNotes" name="reasonNotes" value={request.reasonNotes} onChange={handleChange} className={`${inputClasses} min-h-[80px]`} placeholder="Add any other relevant details..."/>
                </div>
            </div>
        );
    };

    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            onClick={onClose}
        >
            <div 
                className="[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-lg relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Request Item Transfer</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>
                
                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="item_id" className={labelClasses}>Item</label>
                                <select id="item_id" name="item_id" value={request.item_id} onChange={handleChange} className={inputClasses}>
                                    <option value="" disabled>Select an item...</option>
                                    {availableItems.length > 0 ? availableItems.map(item => (
                                        <option key={item.id} value={item.id}>{item.name} (Stock: {item.stock})</option>
                                    )) : (
                                        <option disabled>No new items available to request.</option>
                                    )}
                                </select>
                                {errors.item_id && <p className="text-status-error text-xs mt-1">{errors.item_id}</p>}
                            </div>
                            <div>
                                <label htmlFor="quantity" className={labelClasses}>Quantity</label>
                                <input type="number" id="quantity" name="quantity" value={request.quantity} onChange={handleChange} min="1" className={inputClasses} />
                                {errors.quantity && <p className="text-status-error text-xs mt-1">{errors.quantity}</p>}
                            </div>
                        </div>

                        <div>
                            <label htmlFor="to_workshop_id" className={labelClasses}>Deliver to RackX</label>
                            <select id="to_workshop_id" name="to_workshop_id" value={request.to_workshop_id} onChange={handleChange} className={inputClasses} disabled={isOperator || isTeamLeader}>
                                {workshops.filter(w => w.type === 'Local').map(w => (
                                    <option key={w.id} value={w.id}>{w.name}</option>
                                ))}
                            </select>
                            {(isOperator || isTeamLeader) && <p className="text-xs text-slate-400 mt-1">You can only request transfers to your primary assigned RackX.</p>}
                            {errors.to_workshop_id && <p className="text-status-error text-xs mt-1">{errors.to_workshop_id}</p>}
                        </div>

                        <div className="p-4 bg-slate-900/40 rounded-lg border border-slate-700/50">
                           {isOperator ? renderOperatorForm() : renderStandardForm()}
                        </div>
                    </div>
                    
                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">
                            Cancel
                        </button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                            Submit Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default CreateRequestModal;