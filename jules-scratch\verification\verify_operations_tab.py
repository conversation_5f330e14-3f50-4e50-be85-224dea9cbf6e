from playwright.sync_api import sync_playwright, <PERSON>, expect

def run(playwright):
    browser = playwright.chromium.launch(headless=True)
    context = browser.new_context()
    page = context.new_page()

    # 1. Navigate to the application
    page.goto("http://localhost:5173/")

    # 2. Log in as Admin
    page.locator('button.group:has-text("Admin")').click()
    page.get_by_label("Password").fill("1111")
    page.get_by_role("button", name="Login").click()

    # 3. Navigate to the Admin Console
    page.get_by_role("button", name="Admin Panel").click()

    # 4. Click on the Operations tab
    page.get_by_role("tab", name="Operations").click()

    # 5. Take a screenshot
    page.screenshot(path="jules-scratch/verification/operations_tab.png")

    browser.close()

with sync_playwright() as playwright:
    run(playwright)
