import React, { useState } from 'react';
import { PlusIcon, ChevronDownIcon, WrenchIcon, UserIcon, BoxIcon, UsersIcon, ArchiveIcon } from './icons';

interface AdminEmptyStateProps {
    onAddDepartment: () => void;
    onAddParent: () => void;
    onAddRack: () => void;
    onAddUser: () => void;
    onAddInventory: () => void;
    onAddTeam: () => void;
}

const AdminEmptyState: React.FC<AdminEmptyStateProps> = ({
    onAddDepartment,
    onAddParent,
    onAddRack,
    onAddUser,
    onAddInventory,
    onAddTeam,
}) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const menuItems = [
        { label: 'Add Program', icon: <ArchiveIcon className="w-5 h-5 mr-3" />, action: onAddParent },
        { label: 'Manage Operations', icon: <WrenchIcon className="w-5 h-5 mr-3" />, action: onAddDepartment },
        { label: 'Add User', icon: <UserIcon className="w-5 h-5 mr-3" />, action: onAddUser },
        { label: 'Add to Catalog', icon: <BoxIcon className="w-5 h-5 mr-3" />, action: onAddInventory },
        { label: 'Create Team', icon: <UsersIcon className="w-5 h-5 mr-3" />, action: onAddTeam },
    ];

    return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
            <div className="relative">
                <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    className="relative flex items-center justify-center w-40 h-40 bg-slate-700/50 border-2 border-dashed border-slate-600 rounded-full hover:bg-slate-700/80 hover:border-slate-500 transition-all duration-300 ease-in-out cursor-pointer group"
                >
                    <PlusIcon className="w-20 h-20 text-slate-500 group-hover:text-slate-400 transition-transform duration-300 group-hover:scale-110" />
                    <ChevronDownIcon className={`absolute bottom-5 w-6 h-6 text-slate-400 transition-transform duration-300 ${isMenuOpen ? 'rotate-180' : ''}`} />
                </button>

                {isMenuOpen && (
                    <div className="absolute top-full mt-4 w-64 bg-slate-800 border border-slate-700 rounded-lg shadow-lg z-10 animate-fade-in-up">
                        <ul>
                            {menuItems.map((item, index) => (
                                <li key={index}>
                                    <button
                                        onClick={() => {
                                            item.action();
                                            setIsMenuOpen(false);
                                        }}
                                        className="w-full flex items-center px-4 py-3 text-left text-slate-200 hover:bg-slate-700 transition-colors duration-150"
                                    >
                                        {item.icon}
                                        {item.label}
                                    </button>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
            <h2 className="mt-8 text-2xl font-bold text-slate-300">Start Building Your Inventory System</h2>
            <p className="mt-2 text-slate-400 max-w-md">
                Click the button to add your first Program, User, or Catalog item. You can manage your operational hierarchy in the 'Operations' tab.
            </p>
        </div>
    );
};

export default AdminEmptyState;
