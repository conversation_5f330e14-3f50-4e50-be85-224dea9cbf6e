
import React, { useState, useRef, useEffect, forwardRef } from 'react';
import { InfoIcon, DatabaseZapIcon, RouteIcon, ShieldCheckIcon, BoxIcon, UsersIcon, WrenchIcon, SendIcon, ClipboardListIcon, SettingsIcon, type IconProps } from './icons';
import { UserRole } from '../types';

type AboutTab = 'about' | 'structure' | 'guide' | 'permissions';

// Moved from DataStructureTabContent to prevent re-declaration on every render
const SvgLines: React.FC<{ lines: any[] }> = ({ lines }) => (
    <svg className="absolute top-0 left-0 w-full h-full pointer-events-none z-0" aria-hidden="true">
        <defs>
            <marker id="arrow" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                <path d="M 0 0 L 10 5 L 0 10 z" className="fill-slate-600" />
            </marker>
        </defs>
        {lines.map((line, i) => {
             const controlX = line.from.x + (line.to.x - line.from.x) / 2 + (line.controlX || 0);
             const controlY = line.from.y + (line.to.y - line.from.y) / 2 + (line.controlY || 0);
             const path = `M ${line.from.x} ${line.from.y} Q ${controlX} ${controlY} ${line.to.x} ${line.to.y}`;
             return <path key={i} d={path} className="stroke-slate-600" strokeWidth="2" fill="none" markerEnd="url(#arrow)" />;
        })}
    </svg>
);

// Moved from GuideTabContent to prevent re-declaration on every render
const Step = ({icon, title, description}: {icon: React.ReactElement<IconProps>, title: string, description: string}) => (
    <div className="flex items-start gap-4 p-4 bg-slate-900/50 rounded-lg border border-slate-700">
        <div className="flex-shrink-0 p-3 bg-slate-800 rounded-full">
            {React.cloneElement(icon, { className: "w-6 h-6 text-brand-accent" })}
        </div>
        <div>
            <h4 className="font-bold text-lg text-slate-100">{title}</h4>
            <p className="text-slate-400 mt-1">{description}</p>
        </div>
    </div>
);

const AboutPage: React.FC = () => {
    const [activeTab, setActiveTab] = useState<AboutTab>('about');

    const TabButton: React.FC<{ tabId: AboutTab; label: string, icon: React.ReactNode }> = ({ tabId, label, icon }) => {
        const isActive = activeTab === tabId;
        return (
          <button
            onClick={() => setActiveTab(tabId)}
            className={`flex items-center px-3 py-2 text-sm font-semibold rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-brand-accent ${
              isActive
                ? 'bg-brand-accent text-slate-900'
                : 'text-slate-300 hover:bg-slate-700'
            }`}
            role="tab"
            aria-selected={isActive}
          >
            {icon}
            <span className="ml-2">{label}</span>
          </button>
        );
    };

    const renderContent = () => {
        switch(activeTab) {
            case 'about': return <AboutTabContent />;
            case 'structure': return <DataStructureTabContent />;
            case 'guide': return <GuideTabContent />;
            case 'permissions': return <PermissionsTabContent />;
            default: return null;
        }
    }

    return (
        <div role="tabpanel" className="animate-fade-in space-y-6">
            <div className="p-2 border-b border-slate-700/50">
                <div className="flex items-center space-x-2 flex-wrap gap-y-2" role="tablist" aria-label="About Page Tabs">
                    <TabButton tabId="about" label="About RackX" icon={<InfoIcon className="w-4 h-4" />} />
                    <TabButton tabId="structure" label="Data Structure" icon={<DatabaseZapIcon className="w-4 h-4" />} />
                    <TabButton tabId="guide" label="Intuitive Guide" icon={<RouteIcon className="w-4 h-4" />} />
                    <TabButton tabId="permissions" label="Permissions" icon={<ShieldCheckIcon className="w-4 h-4" />} />
                </div>
            </div>
            <div className="p-2">
                {renderContent()}
            </div>
        </div>
    );
};

const AboutTabContent = () => (
    <div className="space-y-4 text-slate-400">
        <h3 className="text-xl font-bold text-slate-100">RackX: Enterprise Blueprint v1.0</h3>
        <p>RackX is an intelligent logistics platform designed to meet the demanding needs of the aerospace manufacturing industry. It provides a robust, scalable framework for managing inventory, workshops, and personnel, ensuring that the right materials are in the right place at the right time.</p>
        <p>The system is built on a modern frontend stack using React and Tailwind CSS, with all application state managed via a simulated API layer backed by your browser's local storage. This ensures a persistent, responsive experience that is ready to be integrated with a live backend database.</p>
    </div>
);

const Entity = forwardRef<HTMLDivElement, {icon: React.ReactElement<IconProps>, title: string, description: string, color?: string}>(
    ({ icon, title, description, color = 'text-brand-accent' }, ref) => (
    <div ref={ref} className={`flex items-start gap-3 p-4 bg-slate-900 rounded-lg border border-slate-700 w-full`}>
        {React.cloneElement(icon, { className: `w-6 h-6 flex-shrink-0 ${color}` })}
        <div>
        <h5 className="font-bold text-slate-100 text-sm">{title}</h5>
        <p className="text-xs text-slate-400">{description}</p>
        </div>
    </div>
));

const ImportReferenceTable: React.FC<{ title: string; fields: { name: string; required: boolean; example: string }[] }> = ({ title, fields }) => (
    <div className="mb-6">
        <h4 className="text-md font-bold text-slate-200 mb-2">{title}</h4>
        <div className="overflow-x-auto bg-slate-900/50 rounded-lg border border-slate-700">
            <table className="w-full text-sm text-left text-slate-300">
                <thead className="text-xs text-slate-400 uppercase bg-slate-800">
                    <tr>
                        <th scope="col" className="px-4 py-2 font-semibold tracking-wider">Field Name</th>
                        <th scope="col" className="px-4 py-2 font-semibold tracking-wider">Required</th>
                        <th scope="col" className="px-4 py-2 font-semibold tracking-wider">Data Type / Example</th>
                    </tr>
                </thead>
                <tbody>
                    {fields.map(field => (
                        <tr key={field.name} className="border-b border-slate-700 last:border-b-0">
                            <td className="px-4 py-2 font-medium">{field.name}</td>
                            <td className="px-4 py-2">
                                {field.required ? 
                                    <span className="text-xs font-bold px-2 py-0.5 bg-amber-900/60 text-amber-300 rounded-full">Yes</span> : 
                                    <span className="text-xs font-semibold px-2 py-0.5 bg-slate-700 text-slate-400 rounded-full">No</span>
                                }
                            </td>
                            <td className="px-4 py-2 font-mono text-slate-400">{field.example}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    </div>
);


const DataStructureTabContent = () => {
    const containerRef = useRef<HTMLDivElement>(null);
    const programRef = useRef<HTMLDivElement>(null);
    const subProgramRef = useRef<HTMLDivElement>(null);
    const workshopRef = useRef<HTMLDivElement>(null);
    const userRef = useRef<HTMLDivElement>(null);
    const itemCatalogRef = useRef<HTMLDivElement>(null);
    const requestRef = useRef<HTMLDivElement>(null);
    const workshopInventoryRef = useRef<HTMLDivElement>(null);
    const dataImportRef = useRef<HTMLDivElement>(null);

    const [lines, setLines] = useState<any[]>([]);
    
    useEffect(() => {
        const calculateLines = () => {
            const refs = {
                program: programRef.current, subProgram: subProgramRef.current, workshop: workshopRef.current,
                user: userRef.current, itemCatalog: itemCatalogRef.current, request: requestRef.current,
                workshopInventory: workshopInventoryRef.current, dataImport: dataImportRef.current,
            };
    
            const container = containerRef.current;
            if (!container || Object.values(refs).some(r => !r)) return;
    
            const getCoords = (el: HTMLElement, side: 'top' | 'bottom' | 'left' | 'right') => {
                const rect = el.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                const x = rect.left - containerRect.left;
                const y = rect.top - containerRect.top;
    
                switch (side) {
                    case 'top': return { x: x + rect.width / 2, y };
                    case 'bottom': return { x: x + rect.width / 2, y: y + rect.height };
                    case 'left': return { x, y: y + rect.height / 2 };
                    case 'right': return { x: x + rect.width, y: y + rect.height / 2 };
                }
            };
    
            const newLines = [
                { from: getCoords(refs.program!, 'right'), to: getCoords(refs.subProgram!, 'left') },
                { from: getCoords(refs.subProgram!, 'right'), to: getCoords(refs.workshop!, 'left') },
                { from: getCoords(refs.user!, 'bottom'), to: getCoords(refs.request!, 'top') },
                { from: getCoords(refs.itemCatalog!, 'bottom'), to: getCoords(refs.workshopInventory!, 'top') },
                { from: getCoords(refs.user!, 'right'), to: getCoords(refs.workshop!, 'left'), controlY: -40 },
                { from: getCoords(refs.request!, 'right'), to: getCoords(refs.workshop!, 'bottom'), controlY: -40 },
                // Data Import arrows
                { from: getCoords(refs.dataImport!, 'top'), to: getCoords(refs.user!, 'left'), controlY: 40, controlX: -40 },
                { from: getCoords(refs.dataImport!, 'top'), to: getCoords(refs.workshopInventory!, 'left') },
                { from: getCoords(refs.dataImport!, 'right'), to: getCoords(refs.itemCatalog!, 'bottom'), controlY: 20 },
            ];
            
            setLines(newLines);
        };
        
        const timer = setTimeout(() => calculateLines(), 100);
        window.addEventListener('resize', calculateLines);
        return () => {
            clearTimeout(timer);
            window.removeEventListener('resize', calculateLines);
        };
    }, []);

    return (
        <div className="space-y-6">
            <h3 className="text-xl font-bold text-slate-100">Application Data Model</h3>
            <div ref={containerRef} className="relative p-4 bg-slate-900/40 rounded-lg border border-slate-700/50">
                <SvgLines lines={lines}/>
                <div className="relative z-10 grid grid-cols-3 gap-x-12 gap-y-20 items-center">
                    {/* Row 1 */}
                    <div className="col-start-1"><Entity ref={programRef} icon={<WrenchIcon />} title="Program" description="A high-level project or aircraft line (e.g., A350)." color="text-indigo-500" /></div>
                    <div className="col-start-2"><Entity ref={subProgramRef} icon={<WrenchIcon />} title="Sub-Program" description="A specific division within a program (e.g., Wing Assembly)." color="text-indigo-500" /></div>
                    <div className="col-start-3"><Entity ref={workshopRef} icon={<WrenchIcon />} title="Workshop (RackX)" description="A physical location with its own inventory." color="text-indigo-500" /></div>
                    
                    {/* Row 2 */}
                    <div className="col-start-1"><Entity ref={userRef} icon={<UsersIcon />} title="User" description="Team member with a specific role." color="text-green-500" /></div>
                    <div className="col-start-2"><Entity ref={requestRef} icon={<SendIcon />} title="Request" description="For Transfers or Consumptions." color="text-green-500" /></div>
                    <div className="col-start-3"><Entity ref={itemCatalogRef} icon={<BoxIcon />} title="Item Catalog" description="Master list of all unique materials." /></div>

                    {/* Row 3 */}
                    <div className="col-start-1"><Entity ref={dataImportRef} icon={<DatabaseZapIcon />} title="Data Import & Staging" description="External data is staged and validated against API requirements (e.g., column names, data types) before being imported to update the Catalog, Users, or Workshop Stock." color="text-amber-500"/></div>
                    <div className="col-start-3"><Entity ref={workshopInventoryRef} icon={<ClipboardListIcon />} title="Workshop Inventory" description="Stock levels of items in a specific RackX." /></div>
                </div>
            </div>

            <div className="mt-12">
                <h3 className="text-xl font-bold text-slate-100">Data Import Reference</h3>
                <p className="text-slate-400 mt-2 mb-6">When using the Data Tools to import external data, your file must contain columns matching the following fields. The column names in your file do not need to match exactly, as you will map them during the import process.</p>
                
                <ImportReferenceTable 
                    title="Users Import" 
                    fields={[
                        { name: 'Full Name', required: true, example: 'Text (e.g., "Jane Doe")' },
                        { name: 'Email', required: true, example: 'Text (e.g., "<EMAIL>")' },
                        { name: 'Role', required: true, example: 'Text ("Admin", "TeamLeader", "Operator")' },
                        { name: 'Primary Workshop ID', required: false, example: 'Number (e.g., 2)' },
                    ]}
                />
                <ImportReferenceTable 
                    title="Inventory (Catalog) Import" 
                    fields={[
                        { name: 'Item Name', required: true, example: 'Text (e.g., "MS20470AD4-4 Rivet")' },
                        { name: 'SKU', required: true, example: 'Text (e.g., "FAS-001")' },
                        { name: 'Initial Quantity', required: true, example: 'Number (for central warehouse stock)' },
                        { name: 'Description', required: false, example: 'Text' },
                        { name: 'Category', required: false, example: 'Text (e.g., "Fasteners")' },
                        { name: 'Unit Price', required: false, example: 'Number (e.g., 0.12)' },
                    ]}
                />
                <ImportReferenceTable 
                    title="Workshop Stock Import" 
                    fields={[
                        { name: 'Workshop ID', required: true, example: 'Number (e.g., 2)' },
                        { name: 'Item SKU', required: true, example: 'Text (e.g., "FAS-001")' },
                        { name: 'Quantity', required: true, example: 'Number (e.g., 2000)' },
                        { name: 'Low Stock Threshold', required: false, example: 'Number (e.g., 400)' },
                    ]}
                />
            </div>
        </div>
    );
};


const GuideTabContent = () => {
    return (
        <div className="space-y-6">
            <h3 className="text-xl font-bold text-slate-100">Core Application Workflow</h3>
            <div className="space-y-4">
                <Step 
                    icon={<SettingsIcon/>} 
                    title="1. Setup (Admin)" 
                    description="The administrator configures the system by defining Programs, creating Workshops (RackX), managing the master Item Catalog, and creating User accounts with assigned roles."
                />
                 <Step 
                    icon={<SendIcon/>} 
                    title="2. Provisioning (Team Leader)" 
                    description="A Team Leader requests a 'TRANSFER' of items from the central warehouse to their local RackX. An Admin must approve this request, which then moves the physical stock within the system."
                />
                <Step 
                    icon={<ClipboardListIcon/>} 
                    title="3. Operation (Operator)" 
                    description="An Operator uses materials from their local RackX. They log this 'CONSUMPTION', which creates a request. Their Team Leader approves the log, which officially deducts the items from the RackX inventory."
                />
            </div>
        </div>
    );
};

const PermissionsTabContent = () => {
    const permissions: { role: UserRole, description: string, capabilities: string[] }[] = [
        {
            role: 'Admin',
            description: "Has full system-wide control and visibility.",
            capabilities: [
                'Create/Edit/Delete Users, Workshops, and Catalog items.',
                'Manage custom fields and data imports/exports.',
                'Approve/Reject any transfer or consumption request.',
                'View all system data and activity logs.'
            ]
        },
        {
            role: 'TeamLeader',
            description: "Manages a specific Workshop (RackX) and its assigned team.",
            capabilities: [
                'Approve consumption logs from their team members.',
                'Request transfers of new items from the central warehouse.',
                'Assign/unassign Operators to their Workshop.',
                'View inventory and activity related to their Workshop.'
            ]
        },
        {
            role: 'Operator',
            description: "Consumes items from their assigned Workshop (RackX).",
            capabilities: [
                'Log consumption of items, creating a request for approval.',
                'Request new items to be transferred to their RackX.',
                'View the inventory of their assigned RackX.',
                'Cannot approve requests or manage other users.'
            ]
        }
    ];

    return (
        <div className="space-y-6">
            <h3 className="text-xl font-bold text-slate-100">User Roles & Capabilities</h3>
            <div className="overflow-x-auto">
                <table className="w-full text-sm text-left">
                    <thead className="bg-slate-800 text-xs text-slate-300 uppercase tracking-wider">
                        <tr>
                            <th className="p-3">Role</th>
                            <th className="p-3">Description</th>
                            <th className="p-3">Key Capabilities</th>
                        </tr>
                    </thead>
                    <tbody className="text-slate-400">
                        {permissions.map(({ role, description, capabilities }) => (
                            <tr key={role} className="border-b border-slate-700 last:border-0">
                                <td className="p-3 align-top font-bold text-slate-200">{role}</td>
                                <td className="p-3 align-top">{description}</td>
                                <td className="p-3 align-top">
                                    <ul className="space-y-2 list-disc list-inside">
                                        {capabilities.map((cap, i) => <li key={i}>{cap}</li>)}
                                    </ul>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default AboutPage;
