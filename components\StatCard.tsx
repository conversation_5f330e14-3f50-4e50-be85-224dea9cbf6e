import React from 'react';

interface StatCardProps {
  icon: React.ReactElement<{ className?: string }>;
  title: string;
  value: string | number;
  color?: string;
  gradient?: string;
}

const StatCard: React.FC<StatCardProps> = ({ icon, title, value, color = 'text-sky-400', gradient = "from-slate-800 to-slate-900" }) => {
  return (
    <div className={`group relative p-8 rounded-2xl overflow-hidden bg-gradient-to-br ${gradient} border border-transparent shadow-card transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1`}>
        <div className="absolute -top-4 -right-4 text-white opacity-20 group-hover:opacity-30 transition-all duration-300 group-hover:scale-110 group-hover:-rotate-6">
            {React.cloneElement(icon, { className: 'w-24 h-24' })}
        </div>
        <div className="relative z-10">
            <div className={`p-1.5 bg-black/20 rounded-full w-fit mb-3 border border-white/10`}>
                {React.cloneElement(icon, { className: `w-5 h-5 ${color}` })}
            </div>
            <p className={`text-3xl font-extrabold text-white tracking-tighter`}>
                {value}
            </p>
            <p className="text-sm font-semibold text-white/80 mt-1">{title}</p>
        </div>
    </div>
  );
};

export default StatCard;