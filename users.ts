
import { UserType } from './types';

export const INITIAL_USERS: UserType[] = [
  // Admins
  { id: 1, name: '<PERSON>', employeeId: 'E00001', role: 'Admin', password: '1111', primary_workshop_id: 1, avatarUrl: 'https://i.pravatar.cc/150?u=E00001', reelImageUrl: 'https://picsum.photos/seed/E00001/600/200' },
  { id: 18, name: '<PERSON>', employeeId: 'E00002', role: 'Admin', password: '1111', primary_workshop_id: 1, avatarUrl: 'https://i.pravatar.cc/150?u=E00002', reelImageUrl: 'https://picsum.photos/seed/E00002/600/200' },
  { id: 24, name: '<PERSON>', employeeId: 'E00003', role: 'Admin', password: '1111', primary_workshop_id: 1, avatarUrl: 'https://i.pravatar.cc/150?u=E00003', reelImageUrl: 'https://picsum.photos/seed/E00003/600/200' },

  // TeamLeaders
  { id: 3, name: '<PERSON>', employeeId: 'E10001', role: 'TeamLeader', password: '1111', primary_workshop_id: 2, avatarUrl: 'https://i.pravatar.cc/150?u=E10001', reelImageUrl: 'https://picsum.photos/seed/E10001/600/200' },
  { id: 19, name: 'Maria Garcia', employeeId: 'E10002', role: 'TeamLeader', password: '1111', primary_workshop_id: 2, avatarUrl: 'https://i.pravatar.cc/150?u=E10002', reelImageUrl: 'https://picsum.photos/seed/E10002/600/200' },
  { id: 4, name: 'Chen Wei', employeeId: 'E10003', role: 'TeamLeader', password: '1111', primary_workshop_id: 3, avatarUrl: 'https://i.pravatar.cc/150?u=E10003', reelImageUrl: 'https://picsum.photos/seed/E10003/600/200' },
  { id: 21, name: 'Carlos Rodriguez', employeeId: 'E10004', role: 'TeamLeader', password: '1111', primary_workshop_id: 3, avatarUrl: 'https://i.pravatar.cc/150?u=E10004', reelImageUrl: 'https://picsum.photos/seed/E10004/600/200' },
  { id: 7, name: 'Priya Patel', employeeId: 'E10005', role: 'TeamLeader', password: '1111', primary_workshop_id: 4, avatarUrl: 'https://i.pravatar.cc/150?u=E10005', reelImageUrl: 'https://picsum.photos/seed/E10005/600/200' },
  { id: 30, name: 'Ava Martinez', employeeId: 'E10006', role: 'TeamLeader', password: '1111', primary_workshop_id: 5, avatarUrl: 'https://i.pravatar.cc/150?u=E10006', reelImageUrl: 'https://picsum.photos/seed/E10006/600/200' },
  { id: 34, name: 'Ethan Thompson', employeeId: 'E10007', role: 'TeamLeader', password: '1111', primary_workshop_id: 6, avatarUrl: 'https://i.pravatar.cc/150?u=E10007', reelImageUrl: 'https://picsum.photos/seed/E10007/600/200' },
  { id: 39, name: 'Zoe Baker', employeeId: 'E10008', role: 'TeamLeader', password: '1111', primary_workshop_id: 7, avatarUrl: 'https://i.pravatar.cc/150?u=E10008', reelImageUrl: 'https://picsum.photos/seed/E10008/600/200' },

  // Operators
  { id: 6, name: 'Ben Carter', employeeId: 'E20001', role: 'Operator', password: '1111', primary_workshop_id: 2, avatarUrl: 'https://i.pravatar.cc/150?u=E20001', reelImageUrl: 'https://picsum.photos/seed/E20001/600/200' },
  { id: 9, name: 'Olga Ivanova', employeeId: 'E20002', role: 'Operator', password: '1111', primary_workshop_id: 2, avatarUrl: 'https://i.pravatar.cc/150?u=E20002', reelImageUrl: 'https://picsum.photos/seed/E20002/600/200' },
  { id: 20, name: 'Fatima Al-Fassi', employeeId: 'E20003', role: 'Operator', password: '1111', primary_workshop_id: 2, avatarUrl: 'https://i.pravatar.cc/150?u=E20003', reelImageUrl: 'https://picsum.photos/seed/E20003/600/200' },
  { id: 26, name: 'James Wilson', employeeId: 'E20004', role: 'Operator', password: '1111', primary_workshop_id: 2, avatarUrl: 'https://i.pravatar.cc/150?u=E20004', reelImageUrl: 'https://picsum.photos/seed/E20004/600/200' },
  { id: 10, name: 'Kenji Tanaka', employeeId: 'E20005', role: 'Operator', password: '1111', primary_workshop_id: 3, avatarUrl: 'https://i.pravatar.cc/150?u=E20005', reelImageUrl: 'https://picsum.photos/seed/E20005/600/200' },
  { id: 11, name: 'Chloe Dubois', employeeId: 'E20006', role: 'Operator', password: '1111', primary_workshop_id: 3, avatarUrl: 'https://i.pravatar.cc/150?u=E20006', reelImageUrl: 'https://picsum.photos/seed/E20006/600/200' },
  { id: 22, name: 'Emily White', employeeId: 'E20007', role: 'Operator', password: '1111', primary_workshop_id: 3, avatarUrl: 'https://i.pravatar.cc/150?u=E20007', reelImageUrl: 'https://picsum.photos/seed/E20007/600/200' },
  { id: 27, name: 'Ahmed Khan', employeeId: 'E20008', role: 'Operator', password: '1111', primary_workshop_id: 3, avatarUrl: 'https://i.pravatar.cc/150?u=E20008', reelImageUrl: 'https://picsum.photos/seed/E20008/600/200' },
  { id: 14, name: 'Noah Williams', employeeId: 'E20009', role: 'Operator', password: '1111', primary_workshop_id: 4, avatarUrl: 'https://i.pravatar.cc/150?u=E20009', reelImageUrl: 'https://picsum.photos/seed/E20009/600/200' },
  { id: 15, name: 'Isabella Brown', employeeId: 'E20010', role: 'Operator', password: '1111', primary_workshop_id: 4, avatarUrl: 'https://i.pravatar.cc/150?u=E20010', reelImageUrl: 'https://picsum.photos/seed/E20010/600/200' },
  { id: 23, name: 'Sophia Rossi', employeeId: 'E20011', role: 'Operator', password: '1111', primary_workshop_id: 4, avatarUrl: 'https://i.pravatar.cc/150?u=E20011', reelImageUrl: 'https://picsum.photos/seed/E20011/600/200' },
  { id: 28, name: 'Liam OConnell', employeeId: 'E20012', role: 'Operator', password: '1111', primary_workshop_id: 4, avatarUrl: 'https://i.pravatar.cc/150?u=E20012', reelImageUrl: 'https://picsum.photos/seed/E20012/600/200' },

  // Central Operators
  { id: 25, name: 'Kevin Lee', employeeId: 'E30001', role: 'Operator', password: '1111', primary_workshop_id: 1, avatarUrl: 'https://i.pravatar.cc/150?u=E30001', reelImageUrl: 'https://picsum.photos/seed/E30001/600/200' },
  { id: 29, name: 'Sandra Dee', employeeId: 'E30002', role: 'Operator', password: '1111', primary_workshop_id: 1, avatarUrl: 'https://i.pravatar.cc/150?u=E30002', reelImageUrl: 'https://picsum.photos/seed/E30002/600/200' },
  
  // Paint Shop (WS 5)
  { id: 31, name: 'Lucas Green', employeeId: 'E40001', role: 'Operator', password: '1111', primary_workshop_id: 5, avatarUrl: 'https://i.pravatar.cc/150?u=E40001', reelImageUrl: 'https://picsum.photos/seed/E40001/600/200' },
  { id: 32, name: 'Mia Campbell', employeeId: 'E40002', role: 'Operator', password: '1111', primary_workshop_id: 5, avatarUrl: 'https://i.pravatar.cc/150?u=E40002', reelImageUrl: 'https://picsum.photos/seed/E40002/600/200' },
  { id: 33, name: 'Jackson Wright', employeeId: 'E40003', role: 'Operator', password: '1111', primary_workshop_id: 5, avatarUrl: 'https://i.pravatar.cc/150?u=E40003', reelImageUrl: 'https://picsum.photos/seed/E40003/600/200' },
  
  // Final Assembly (WS 6)
  { id: 35, name: 'Harper Scott', employeeId: 'E50001', role: 'Operator', password: '1111', primary_workshop_id: 6, avatarUrl: 'https://i.pravatar.cc/150?u=E50001', reelImageUrl: 'https://picsum.photos/seed/E50001/600/200' },
  { id: 36, name: 'Logan Hill', employeeId: 'E50002', role: 'Operator', password: '1111', primary_workshop_id: 6, avatarUrl: 'https://i.pravatar.cc/150?u=E50002', reelImageUrl: 'https://picsum.photos/seed/E50002/600/200' },
  { id: 37, name: 'Evelyn Adams', employeeId: 'E50003', role: 'Operator', password: '1111', primary_workshop_id: 6, avatarUrl: 'https://i.pravatar.cc/150?u=E50003', reelImageUrl: 'https://picsum.photos/seed/E50003/600/200' },
  { id: 38, name: 'Carter Nelson', employeeId: 'E50004', role: 'Operator', password: '1111', primary_workshop_id: 6, avatarUrl: 'https://i.pravatar.cc/150?u=E50004', reelImageUrl: 'https://picsum.photos/seed/E50004/600/200' },
  
  // Electrical Systems (WS 7)
  { id: 40, name: 'Leo Perez', employeeId: 'E60001', role: 'Operator', password: '1111', primary_workshop_id: 7, avatarUrl: 'https://i.pravatar.cc/150?u=E60001', reelImageUrl: 'https://picsum.photos/seed/E60001/600/200' },
  { id: 41, name: 'Grace Turner', employeeId: 'E60002', role: 'Operator', password: '1111', primary_workshop_id: 7, avatarUrl: 'https://i.pravatar.cc/150?u=E60002', reelImageUrl: 'https://picsum.photos/seed/E60002/600/200' },
  { id: 42, name: 'Owen Phillips', employeeId: 'E60003', role: 'Operator', password: '1111', primary_workshop_id: 7, avatarUrl: 'https://i.pravatar.cc/150?u=E60003', reelImageUrl: 'https://picsum.photos/seed/E60003/600/200' },
];