import React, { useState, useMemo, useCallback, useEffect, FC } from 'react';
import { AppState, StagedImport, StagedImportRow, ImportType, DataSource, DataSourceType, DataSourceConfig } from '../types';
import { UploadCloudIcon, CheckCircleIcon, XCircleIcon, FileDownIcon, ChevronsUpDownIcon, FilePlusIcon } from './icons';
import * as XLSX from 'xlsx';
import { useAppState, useAppActions } from '../context/AppContext';
import DataImportModal from './DataImportModal';
import { api } from '../services/api';

// --- Data Source Management ---

const DATA_SOURCE_CONFIGS = {
    SQL_SERVER: {
        name: 'Microsoft SQL Server',
        fields: [
            { name: 'host', label: 'Host/Server Address', type: 'text', required: true },
            { name: 'port', label: 'Port', type: 'number', required: true, defaultValue: 1433 },
            { name: 'database', label: 'Database Name', type: 'text', required: true },
            { name: 'user', label: 'Username', type: 'text', required: true },
            { name: 'password', label: 'Password', type: 'password', required: true },
            { name: 'encrypt', label: 'Encrypt Connection', type: 'checkbox', defaultValue: false },
            { name: 'trustServerCertificate', label: 'Trust Server Certificate', type: 'checkbox', defaultValue: true },
        ]
    },
    SUPABASE: {
        name: 'Supabase',
        fields: [
            { name: 'projectUrl', label: 'Project URL', type: 'text', required: true, placeholder: 'https://<id>.supabase.co' },
            { name: 'anonKey', label: 'Anon (Public) Key', type: 'password', required: true },
            { name: 'serviceRoleKey', label: 'Service Role Key', type: 'password', required: true },
        ]
    },
    ERP: {
        name: 'Generic ERP',
        fields: [
            { name: 'apiUrl', label: 'API Base URL', type: 'text', required: true },
            { name: 'apiKey', label: 'API Key', type: 'password', required: true },
            { name: 'user', label: 'Username', type: 'text', required: false },
            { name: 'password', label: 'Password', type: 'password', required: false },
        ]
    }
};

const DataSourceModal: FC<{
    isOpen: boolean;
    onClose: () => void;
    onSave: () => void;
    dataSource: Partial<DataSource> | null;
}> = ({ isOpen, onClose, onSave, dataSource }) => {
    const [name, setName] = useState('');
    const [type, setType] = useState<DataSourceType>('SQL_SERVER');
    const [config, setConfig] = useState<DataSourceConfig>({});
    const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
    const [isTesting, setIsTesting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (isOpen) {
            if (dataSource && dataSource.Id) {
                setName(dataSource.Name || '');
                setType(dataSource.Type || 'SQL_SERVER');
                setConfig(dataSource.Config || {});
            } else {
                const initialType = 'SQL_SERVER';
                setName('');
                setType(initialType);
                const initialConfig = Object.fromEntries(DATA_SOURCE_CONFIGS[initialType].fields.map(f => [f.name, f.defaultValue ?? '']));
                setConfig(initialConfig);
            }
            setTestResult(null);
            setError(null);
        }
    }, [dataSource, isOpen]);

    useEffect(() => {
        if (isOpen) {
            const newConfig = Object.fromEntries(DATA_SOURCE_CONFIGS[type].fields.map(f => [f.name, f.defaultValue ?? '']));
            setConfig(newConfig);
            setTestResult(null);
        }
    }, [type, isOpen]);

    const handleConfigChange = (field: string, value: string | boolean | number) => {
        setConfig(prev => ({ ...prev, [field]: value }));
    };

    const handleTestConnection = async () => {
        setIsTesting(true);
        setTestResult(null);
        setError(null);
        try {
            const result = await api.testDataSource({ type, config });
            setTestResult(result);
        } catch (err: any) {
            setTestResult({ success: false, message: err.message || 'Failed to connect.' });
        } finally {
            setIsTesting(false);
        }
    };

    const handleSave = async () => {
        const payload = { Name: name, Type: type, Config: config };
        try {
            if (dataSource?.Id) {
                await api.updateDataSource(dataSource.Id, payload);
            } else {
                await api.createDataSource(payload);
            }
            onSave();
            onClose();
        } catch (err: any) {
            console.error("Failed to save data source", err);
            setError(err.message || 'An error occurred while saving.');
        }
    };

    if (!isOpen) return null;

    const currentFields = DATA_SOURCE_CONFIGS[type].fields;
    const isFormValid = name && currentFields.every(f => !f.required || (config[f.name as keyof DataSourceConfig] !== '' && config[f.name as keyof DataSourceConfig] !== undefined));

    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-50 p-4 animate-fade-in-fast" onClick={onClose}>
            <div className="bg-slate-800 rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col" onClick={e => e.stopPropagation()}>
                <div className="p-6 border-b border-slate-700">
                    <h3 className="text-xl font-bold text-white">{dataSource?.Id ? 'Edit Connection' : 'Add New Connection'}</h3>
                </div>
                <div className="p-6 space-y-6 overflow-y-auto">
                    {error && <div className="p-3 rounded-md text-sm bg-red-900/50 text-red-300">Error: {error}</div>}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label htmlFor="ds-name" className="block text-sm font-medium text-slate-300 mb-1">Connection Name</label>
                            <input id="ds-name" type="text" value={name} onChange={e => setName(e.target.value)} placeholder="e.g., Production SQL DB" className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition" />
                        </div>
                        <div>
                            <label htmlFor="ds-type" className="block text-sm font-medium text-slate-300 mb-1">Data Source Type</label>
                            <select id="ds-type" value={type} onChange={e => setType(e.target.value as DataSourceType)} className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition">
                                {Object.entries(DATA_SOURCE_CONFIGS).map(([key, { name }]) => (
                                    <option key={key} value={key}>{name}</option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <div className="pt-4 border-t border-slate-700 space-y-4">
                        <h4 className="text-lg font-semibold text-slate-200">Configuration</h4>
                        {currentFields.map(field => (
                            <div key={field.name}>
                                <label htmlFor={`config-${field.name}`} className="block text-sm font-medium text-slate-300 mb-1">{field.label}</label>
                                {field.type === 'checkbox' ? (
                                    <div className="flex items-center">
                                        <input
                                            id={`config-${field.name}`}
                                            type="checkbox"
                                            checked={!!config[field.name as keyof DataSourceConfig]}
                                            onChange={e => handleConfigChange(field.name, e.target.checked)}
                                            className="h-4 w-4 rounded border-slate-500 bg-slate-700 text-brand-accent focus:ring-brand-accent"
                                        />
                                        <span className="ml-2 text-sm text-slate-300">Enabled</span>
                                    </div>
                                ) : (
                                    <input
                                        id={`config-${field.name}`}
                                        type={field.type}
                                        value={String(config[field.name as keyof DataSourceConfig] ?? '')}
                                        onChange={e => handleConfigChange(field.name, field.type === 'number' ? parseInt(e.target.value, 10) || 0 : e.target.value)}
                                        placeholder={field.placeholder}
                                        className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition"
                                    />
                                )}
                            </div>
                        ))}
                    </div>

                    {testResult && (
                        <div className={`p-3 rounded-md text-sm flex items-center gap-2 ${testResult.success ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'}`}>
                           {testResult.success ? '✓' : '✗'} <span>{testResult.message}</span>
                        </div>
                    )}
                </div>
                <div className="p-4 bg-slate-900/50 border-t border-slate-700 flex justify-between items-center">
                    <button onClick={handleTestConnection} disabled={isTesting || !isFormValid} className="flex items-center gap-2 px-4 py-2 text-sm bg-slate-600 text-white font-semibold rounded-md hover:bg-slate-500 transition disabled:opacity-50 disabled:cursor-not-allowed">
                        {isTesting ? 'Testing...' : '[Test]'}
                    </button>
                    <div className="flex gap-3">
                        <button onClick={onClose} className="px-4 py-2 text-sm font-semibold text-slate-300 hover:text-white transition bg-slate-600/50 hover:bg-slate-600 rounded-md">Cancel</button>
                        <button onClick={handleSave} disabled={!isFormValid} className="px-6 py-2 bg-status-success text-white font-semibold rounded-md shadow-md hover:bg-green-600 transition disabled:bg-slate-500 disabled:cursor-not-allowed">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

const DataSourceManager = () => {
    const [dataSources, setDataSources] = useState<DataSource[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingDataSource, setEditingDataSource] = useState<Partial<DataSource> | null>(null);

    const fetchDataSources = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const sources = await api.getDataSources();
            setDataSources(sources);
        } catch (err: any) {
            setError(err.message || 'Failed to load data sources.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchDataSources();
    }, [fetchDataSources]);

    const handleAdd = () => {
        setEditingDataSource(null);
        setIsModalOpen(true);
    };

    const handleEdit = async (id: number) => {
        try {
            const sourceToEdit = await api.getDataSource(id);
            setEditingDataSource(sourceToEdit);
            setIsModalOpen(true);
        } catch (err: any) {
            alert(`Error fetching data source details: ${err.message}`);
        }
    };

    const handleDelete = async (id: number) => {
        if (window.confirm('Are you sure you want to delete this data source? This cannot be undone.')) {
            try {
                await api.deleteDataSource(id);
                fetchDataSources(); // Refresh list
            } catch (err: any) {
                alert(`Failed to delete data source: ${err.message}`);
                console.error(err);
            }
        }
    };

    const handleSave = () => {
        fetchDataSources();
    };

    return (
        <div>
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-xl font-semibold text-slate-200">Data Source Connections</h3>
                    <p className="text-slate-400 mt-2">Manage connections to external databases and services.</p>
                </div>
                <button
                    onClick={handleAdd}
                    className="flex items-center gap-2 px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition"
                >
                    + Add Connection
                </button>
            </div>
            <div className="mt-4 bg-slate-900/50 rounded-lg border border-slate-700 overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left text-slate-300">
                        <thead className="text-xs text-slate-400 uppercase bg-slate-800">
                            <tr>
                                <th scope="col" className="px-6 py-3">[DB] Name</th>
                                <th scope="col" className="px-6 py-3">[Type] Type</th>
                                <th scope="col" className="px-6 py-3">Last Updated</th>
                                <th scope="col" className="px-6 py-3"><span className="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            {isLoading && (
                                <tr><td colSpan={4} className="text-center p-8 text-slate-500">Loading connections...</td></tr>
                            )}
                            {error && (
                                <tr><td colSpan={4} className="text-center p-8 text-status-error">{error}</td></tr>
                            )}
                            {!isLoading && !error && dataSources.length === 0 && (
                                <tr><td colSpan={4} className="text-center p-8 text-slate-500">No data sources configured.</td></tr>
                            )}
                            {dataSources.map(ds => (
                                <tr key={ds.Id} className="border-b border-slate-700 hover:bg-slate-800/50">
                                    <td className="px-6 py-4 font-medium text-white">{ds.Name}</td>
                                    <td className="px-6 py-4">
                                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                                            ds.Type === 'SQL_SERVER' ? 'bg-sky-900 text-sky-300' :
                                            ds.Type === 'SUPABASE' ? 'bg-emerald-900 text-emerald-300' :
                                            'bg-slate-600 text-slate-300'
                                        }`}>{DATA_SOURCE_CONFIGS[ds.Type]?.name || ds.Type}</span>
                                    </td>
                                    <td className="px-6 py-4">{new Date(ds.UpdatedAt).toLocaleString()}</td>
                                    <td className="px-6 py-4 text-right">
                                        <div className="flex justify-end items-center gap-4">
                                            <button onClick={() => handleEdit(ds.Id)} className="font-medium text-amber-400 hover:underline">Edit</button>
                                            <button onClick={() => handleDelete(ds.Id)} className="font-medium text-red-500 hover:underline">Delete</button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
            <DataSourceModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSave={handleSave}
                dataSource={editingDataSource}
            />
        </div>
    );
};

interface DataToolsPageProps {}

type ImportStage = 'idle' | 'file-selected' | 'mapping' | 'preview';

const IMPORT_CONFIG: { [key in ImportType]: { 
    requiredFields: string[], 
    optionalFields: string[],
    description: string 
} } = {
  'Inventory': { 
      requiredFields: ['Item Name', 'SKU', 'Initial Quantity'], 
      optionalFields: ['Description', 'Category', 'Unit Price'],
      description: "Replaces the entire item catalog. Any existing items not present in this file will be deleted, along with their stock records in all workshops."
  },
  'Users': { 
      requiredFields: ['Full Name', 'Email', 'Role'], 
      optionalFields: ['Primary Workshop ID'],
      description: "Replaces the entire user list. Any existing users not present in this file will be deleted. Be sure to include all necessary admin accounts." 
  },
  'Workshop Stock': { 
      requiredFields: ['Workshop ID', 'Item SKU', 'Quantity'], 
      optionalFields: ['Low Stock Threshold'],
      description: "Sets or overwrites the stock levels for specific items in designated workshops. Only the items listed in the file will be affected; other stock levels will remain unchanged."
  },
  'Add/Update Inventory': {
      requiredFields: ['SKU', 'Item Name'],
      optionalFields: ['Description', 'Category', 'Unit Price', 'Initial Quantity'],
      description: "Adds new items to the catalog or updates existing items matched by SKU. This will not delete any items."
  }
};

const ITEMS_PER_PAGE = 15;

const DataToolsPage: React.FC<DataToolsPageProps> = () => {
  const { appState } = useAppState();
  const { handleStageData } = useAppActions();

  // Import Flow State
  const [importStage, setImportStage] = useState<ImportStage>('idle');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileHeaders, setFileHeaders] = useState<string[]>([]);
  const [allFileRows, setAllFileRows] = useState<string[][]>([]);
  const [filePreviewRows, setFilePreviewRows] = useState<string[][]>([]);
  const [importType, setImportType] = useState<ImportType>('Inventory');
  const [columnMapping, setColumnMapping] = useState<{ [key: string]: string }>({});
  const [validatedRows, setValidatedRows] = useState<StagedImportRow[]>([]);
  const [dragOver, setDragOver] = useState(false);

  // Table State
  const [sortConfig, setSortConfig] = useState<{ key: string, direction: 'asc' | 'desc' } | null>(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);

  const resetImportState = () => {
    setImportStage('idle');
    setSelectedFile(null);
    setFileHeaders([]);
    setFilePreviewRows([]);
    setAllFileRows([]);
    setColumnMapping({});
    setValidatedRows([]);
    setImportType('Inventory');
    setSortConfig(null);
    setGlobalFilter('');
    setCurrentPage(1);
  };

  const handleFileSelect = (file: File | null) => {
    if (!file) return;

    setSelectedFile(file);
    const reader = new FileReader();

    reader.onload = (event) => {
        const fileContent = event.target?.result;
        if (!fileContent) {
            alert("Could not read file content.");
            resetImportState();
            return;
        }
        
        try {
            const workbook = XLSX.read(fileContent, { type: 'binary' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: "" });
            
            const nonEmptyData = jsonData.filter(row => row.some(cell => cell !== null && cell.toString().trim() !== ""));
            if (nonEmptyData.length < 2) {
                alert("The selected file appears to be empty or has no data rows.");
                resetImportState();
                return;
            }

            const headers = nonEmptyData[0].map(h => h.toString());
            const rows = nonEmptyData.slice(1).map(row => row.map(cell => cell.toString()));

            setFileHeaders(headers);
            setAllFileRows(rows);
            setFilePreviewRows(rows.slice(0, 5));
            setImportStage('file-selected');

        } catch(e) {
            console.error("Error parsing Excel file:", e);
            alert("Error parsing file:\nThis can happen with certain formats. Please try re-saving the file as a modern .xlsx or a .csv file.\n\n" + (e as Error).message);
            resetImportState();
        }
    };

    reader.onerror = () => {
        alert("Error reading file.");
        resetImportState();
    };
    
    reader.readAsBinaryString(file);
  };

  const handleProceedToValidation = () => {
    const catalogSkuMap = new Map(appState!.catalog.map(i => [i.sku.toLowerCase(), i]));
    const workshopIdSet = new Set(appState!.workshops.map(w => w.id));
    
    const mapping = columnMapping;

    const getMappedValue = (row: string[], fieldName: string): string => {
        const headerName = mapping[fieldName];
        if (!headerName) return "";
        const headerIndex = fileHeaders.indexOf(headerName);
        return headerIndex !== -1 ? (row[headerIndex] || "") : "";
    };

    const newValidatedRows = allFileRows.map(row => {
        let isValid = true;
        let message = '';
        
        const config = IMPORT_CONFIG[importType];
        
        for (const field of config.requiredFields) {
            const value = getMappedValue(row, field);
            if (!value?.trim()) {
                isValid = false;
                message = `Required field "${field}" is missing.`;
                break;
            }
        }

        if (isValid) {
            if (importType === 'Users' && !/\S+@\S+\.\S+/.test(getMappedValue(row, 'Email'))) {
                isValid = false;
                message = 'Email address is invalid.';
            } else if (importType === 'Workshop Stock') {
                const wsId = getMappedValue(row, 'Workshop ID');
                const sku = getMappedValue(row, 'Item SKU');
                if (!workshopIdSet.has(parseInt(wsId, 10))) {
                    isValid = false; message = `Workshop ID "${wsId}" does not exist.`;
                } else if (!catalogSkuMap.has(sku.toLowerCase())) {
                    isValid = false; message = `Item SKU "${sku}" does not exist in the catalog.`;
                }
            }
        }

        return { data: row, status: isValid ? 'ok' : 'error', message } as StagedImportRow;
    });

    setValidatedRows(newValidatedRows);
    setImportStage('preview');
  };

  const processedRows = useMemo(() => {
    let rows = [...validatedRows];
    if (globalFilter) {
      rows = rows.filter(row => row.data.some(cell => cell.toLowerCase().includes(globalFilter.toLowerCase())));
    }
    if (sortConfig !== null) {
      const headerIndex = fileHeaders.indexOf(sortConfig.key);
      if (headerIndex !== -1) {
        rows.sort((a, b) => {
          const aVal = a.data[headerIndex];
          const bVal = b.data[headerIndex];
          const numA = parseFloat(aVal);
          const numB = parseFloat(bVal);
          let comparison = 0;
          if (!isNaN(numA) && !isNaN(numB)) {
            comparison = numA - numB;
          } else {
            comparison = aVal.localeCompare(bVal);
          }
          return sortConfig.direction === 'asc' ? comparison : -comparison;
        });
      }
    }
    return rows;
  }, [validatedRows, globalFilter, sortConfig, fileHeaders]);

  const paginatedRows = useMemo(() => {
    const start = (currentPage - 1) * ITEMS_PER_PAGE;
    return processedRows.slice(start, start + ITEMS_PER_PAGE);
  }, [processedRows, currentPage]);

  const totalPages = Math.ceil(processedRows.length / ITEMS_PER_PAGE);

  const requestSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };
  
  const handleExport = () => {
    const csvContent = [
        fileHeaders.join(','),
        ...processedRows.map(row => row.data.map(cell => `"${(cell ?? '').toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.setAttribute('download', `validated_data_${selectedFile?.name?.split('.')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleStageForImport = () => {
    if (!selectedFile || validatedRows.length === 0) return;
    const valid = validatedRows.filter(r => r.status === 'ok').length;
    const invalid = validatedRows.length - valid;

    const dataToStage: Omit<StagedImport, 'id' | 'user_id' | 'created_at'> = {
        source: 'csv',
        importType: importType,
        fileName: selectedFile.name,
        status: 'staged',
        rows: validatedRows,
        summary: { total: validatedRows.length, valid, invalid },
        columnMapping: columnMapping,
        fileHeaders: fileHeaders,
    };
    handleStageData(dataToStage);
    resetImportState();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files[0]);
      e.dataTransfer.clearData();
    }
  };

  const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
  const labelClasses = "block text-sm font-medium text-slate-300 mb-1";
  
  const renderImportWizard = () => {
      const config = IMPORT_CONFIG[importType];

      switch(importStage) {
        case 'file-selected':
          return (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-slate-200">2. Select Import Type</h4>
              <p className="text-sm text-slate-400">Your file <span className="font-semibold text-brand-accent">{selectedFile?.name}</span> has been loaded ({allFileRows.length} rows detected). Now, tell us what kind of data this is.</p>
              <div>
                <label htmlFor="importType" className={labelClasses}>Data Type</label>
                <select id="importType" name="importType" value={importType} onChange={e => setImportType(e.target.value as ImportType)} className={inputClasses}>
                    <option value="Inventory">Inventory (Full Replacement)</option>
                    <option value="Add/Update Inventory">Add/Update Inventory</option>
                    <option value="Users">Users</option>
                    <option value="Workshop Stock">Workshop Stock (Levels)</option>
                </select>
                <p className="text-xs text-slate-400 mt-2">{IMPORT_CONFIG[importType].description}</p>
              </div>
              <div className="flex justify-between items-center pt-4 border-t border-slate-700">
                <button onClick={resetImportState} className="text-sm font-semibold text-slate-400 hover:text-white">Cancel</button>
                <button onClick={() => setImportStage('mapping')} className="px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Next: Map Columns</button>
              </div>
            </div>
          );
        
        case 'mapping':
          const mappedHeaders = new Set(Object.values(columnMapping));
          const allRequiredMapped = config.requiredFields.every(field => columnMapping[field]);
          return (
            <div className="space-y-4">
               <h4 className="text-lg font-semibold text-slate-200">3. Map Columns</h4>
               <p className="text-sm text-slate-400">Match the columns from your file to the required application fields. We've provided a preview of your data to help.</p>
               
               <div className="space-y-2">
                    {[...config.requiredFields, ...config.optionalFields].map(field => {
                        const isRequired = config.requiredFields.includes(field);
                        return (
                          <div key={field} className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4 p-3 bg-slate-800/50 rounded-lg transition-colors hover:bg-slate-800/80">
                            <div className="flex items-center gap-2">
                              <label htmlFor={`map-${field}`} className="font-semibold text-slate-200">{field}</label>
                              {isRequired && (
                                <span className="text-xs font-bold px-2 py-0.5 bg-amber-900/60 text-amber-300 rounded-full">Required</span>
                              )}
                            </div>
                            <div className="w-full sm:w-1/2 md:w-2/5">
                              <select 
                                id={`map-${field}`}
                                value={columnMapping[field] || ''}
                                onChange={e => setColumnMapping(prev => ({ ...prev, [field]: e.target.value }))}
                                className={`${inputClasses} text-sm`}
                              >
                                {isRequired ? (
                                  <option value="" disabled>Select a column...</option>
                                ) : (
                                  <option value="">- Ignore this field -</option>
                                )}
                                {fileHeaders.map(header => (
                                  <option key={header} value={header} disabled={mappedHeaders.has(header) && columnMapping[field] !== header}>{header}</option>
                                ))}
                              </select>
                            </div>
                          </div>
                        );
                    })}
               </div>

              <div className="overflow-x-auto mt-4 border border-slate-700 rounded-lg">
                <table className="w-full text-xs">
                    <thead className="bg-slate-800"><tr className="text-left">{fileHeaders.map(h => <th key={h} className="p-2 font-semibold">{h}</th>)}</tr></thead>
                    <tbody>{filePreviewRows.map((row, i) => <tr key={i} className="border-t border-slate-700">{row.map((cell, j) => <td key={j} className="p-2 whitespace-nowrap">{cell}</td>)}</tr>)}</tbody>
                </table>
              </div>
              <div className="flex justify-between items-center pt-4 border-t border-slate-700">
                <button onClick={() => setImportStage('file-selected')} className="text-sm font-semibold text-slate-400 hover:text-white">Back</button>
                <button onClick={handleProceedToValidation} disabled={!allRequiredMapped} className="px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition disabled:bg-slate-500 disabled:text-white">Next: Validate Data</button>
              </div>
            </div>
          );
        
        case 'preview':
          const { valid, invalid } = validatedRows.reduce((acc, row) => {
            if (row.status === 'ok') acc.valid++; else acc.invalid++;
            return acc;
          }, { valid: 0, invalid: 0 });

          return (
             <div className="space-y-6">
                 <div>
                     <h4 className="text-xl font-bold text-slate-100">4. Review & Confirm</h4>
                     <p className="text-sm text-slate-400 mt-1">We've validated your data. Rows with errors will be skipped during the import. You can filter, sort, and review before finalizing.</p>
                 </div>
                 
                 <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700">
                     <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                         <div className="flex items-center gap-6">
                             <div className="flex items-center gap-2">
                                 <CheckCircleIcon className="w-5 h-5 text-status-success" />
                                 <div>
                                     <p className="text-xl font-bold text-slate-100">{valid}</p>
                                     <p className="text-xs font-semibold text-slate-400">Valid Rows</p>
                                 </div>
                             </div>
                              <div className="flex items-center gap-2">
                                 <XCircleIcon className="w-5 h-5 text-status-error" />
                                 <div>
                                     <p className="text-xl font-bold text-slate-100">{invalid}</p>
                                     <p className="text-xs font-semibold text-slate-400">Invalid Rows</p>
                                 </div>
                             </div>
                         </div>
                         <div className="flex items-center gap-3 w-full md:w-auto">
                              <input
                                 type="search"
                                 placeholder="Filter data..."
                                 value={globalFilter}
                                 onChange={e => { setGlobalFilter(e.target.value); setCurrentPage(1); }}
                                 className="w-full md:w-auto bg-slate-700 border border-slate-600 rounded-md p-2 text-sm text-slate-200 focus:ring-2 focus:ring-brand-accent outline-none transition"
                             />
                             <button onClick={handleExport} className="flex-shrink-0 flex items-center justify-center px-4 py-2 text-sm bg-green-700 text-white font-semibold rounded-md hover:bg-green-600 transition">
                                 <FileDownIcon className="w-4 h-4 mr-2" />
                                 Export
                             </button>
                         </div>
                     </div>
                 </div>

                 <div className="bg-slate-900/50 rounded-lg border border-slate-700 overflow-hidden">
                     <div className="overflow-auto max-h-[50vh]">
                         <table className="w-full text-sm">
                             <thead className="bg-slate-800 sticky top-0 z-10">
                                 <tr className="text-left">
                                     {fileHeaders.map(h => (
                                         <th key={h} className="px-4 py-3">
                                             <button onClick={() => requestSort(h)} className="flex items-center gap-1.5 font-semibold tracking-wider text-slate-300 hover:text-white transition-colors">
                                                 {h}
                                                 <ChevronsUpDownIcon className={`w-4 h-4 ${sortConfig?.key === h ? 'text-white' : 'text-slate-500'}`} />
                                             </button>
                                         </th>
                                     ))}
                                     <th className="px-4 py-3 font-semibold tracking-wider">Status</th>
                                 </tr>
                             </thead>
                             <tbody>
                               {paginatedRows.map((row, i) => (
                                 <tr key={i} className={`border-t border-slate-700/80 ${i % 2 === 0 ? 'bg-slate-800/30' : 'bg-transparent'} ${row.status === 'error' ? '!bg-status-error-bg/30' : ''}`}>
                                   {row.data.map((cell, j) => <td key={j} className="px-4 py-2.5 whitespace-nowrap">{cell}</td>)}
                                   <td className="px-4 py-2.5 whitespace-nowrap">
                                     {row.status === 'error' 
                                         ? <span className="flex items-center gap-2 text-status-error font-medium"><XCircleIcon className="w-4 h-4"/> {row.message}</span>
                                         : <span className="flex items-center gap-2 text-status-success font-medium"><CheckCircleIcon className="w-4 h-4"/> OK</span>
                                     }
                                   </td>
                                 </tr>
                               ))}
                             </tbody>
                         </table>
                         {processedRows.length === 0 && (
                            <p className="text-center py-8 text-slate-400">
                                {globalFilter ? 'No rows match your filter.' : 'No data to display.'}
                            </p>
                        )}
                     </div>
                 </div>

                  <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                     <p className="text-sm text-slate-400">Page {currentPage} of {totalPages} ({processedRows.length} rows)</p>
                     <div className="flex gap-2">
                         <button onClick={() => setCurrentPage(p => p - 1)} disabled={currentPage === 1} className="px-4 py-2 text-sm font-semibold bg-slate-600 text-white rounded-md disabled:opacity-50 transition hover:bg-slate-500">Previous</button>
                         <button onClick={() => setCurrentPage(p => p + 1)} disabled={currentPage >= totalPages} className="px-4 py-2 text-sm font-semibold bg-slate-600 text-white rounded-md disabled:opacity-50 transition hover:bg-slate-500">Next</button>
                     </div>
                 </div>

                <div className="flex justify-between items-center pt-5 mt-2 border-t border-slate-700">
                    <button onClick={() => setImportStage('mapping')} className="px-4 py-2 text-sm font-semibold text-slate-300 hover:text-white transition bg-slate-600/50 hover:bg-slate-600 rounded-md">Back</button>
                    
                    <div className="flex items-center gap-4">
                      {invalid > 0 && (
                          <p className="text-sm text-status-warning">Warning: {invalid} invalid rows will be skipped.</p>
                      )}
                      <button onClick={handleStageForImport} className="px-6 py-2 bg-status-success text-white font-semibold rounded-md shadow-md hover:bg-green-600 transition">
                        Stage for Import
                      </button>
                    </div>
                </div>
            </div>
          );

        case 'idle':
        default:
          return (
            <div
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                className={`p-6 border-2 border-dashed rounded-lg transition-colors ${dragOver ? 'border-brand-accent bg-brand-accent/20' : 'border-slate-600 hover:border-brand-accent'}`}
            >
                <input
                    id="file-upload"
                    type="file"
                    className="hidden"
                    accept=".xlsx, .csv"
                    onChange={(e) => handleFileSelect(e.target.files ? e.target.files[0] : null)}
                />
                <label htmlFor="file-upload" className="block text-center cursor-pointer">
                    <UploadCloudIcon className="w-12 h-12 mx-auto text-slate-400" />
                    <p className="mt-2 text-lg font-semibold text-slate-200">Drop your file here or click to browse</p>
                    <p className="text-sm text-slate-400">Supports .xlsx and .csv</p>
                </label>
            </div>
          );
      }
  }


  return (
    <>
      <div role="tabpanel" className="animate-fade-in space-y-8">
          <div>
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-semibold text-slate-200">Simple Import Wizard</h3>
                  <p className="text-slate-400 mt-2">For most common import tasks like replacing the catalog or user list.</p>
                </div>
                <button onClick={() => setIsImportModalOpen(true)} className="flex items-center gap-2 px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white font-semibold rounded-md transition">
                  <FilePlusIcon className="w-5 h-5" />
                  Advanced Import
                </button>
              </div>
               <div className="mt-4 p-4 bg-slate-900/50 rounded-lg border border-slate-700">
                 {renderImportWizard()}
               </div>
          </div>

          <div className="mt-12">
            <DataSourceManager />
          </div>
      </div>
      <DataImportModal isOpen={isImportModalOpen} onClose={() => setIsImportModalOpen(false)} />
    </>
  );
};
export default DataToolsPage;