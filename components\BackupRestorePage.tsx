

import React, { useState } from 'react';
import { SaveIcon, UploadIcon, CopyIcon, CheckCircleIcon, RefreshCwIcon, XCircleIcon } from './icons';
import { api } from '../services/api';
import { AppState } from '../types';
import { useAppActions } from '../context/AppContext';

const BackupRestorePage: React.FC = () => {
    const { handleRestore, handleReset } = useAppActions();
    const [backupData, setBackupData] = useState('');
    const [restoreData, setRestoreData] = useState('');
    const [isCopied, setIsCopied] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const handleCreateBackup = async () => {
        setIsLoading(true);
        setError('');
        setSuccessMessage('');
        const data = await api.getRawDataForBackup();
        setBackupData(data);
        setIsLoading(false);
    };

    const handleCopy = () => {
        navigator.clipboard.writeText(backupData);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
    };

    const onRestore = async () => {
        if (!restoreData.trim()) {
            setError('Restore field cannot be empty.');
            return;
        }
        if (!window.confirm("Are you sure you want to restore? This will overwrite all current data.")) {
            return;
        }
        
        setIsLoading(true);
        setError('');
        setSuccessMessage('');

        try {
            const restoredState = await api.restoreDataFromBackup(restoreData);
            await handleRestore(restoredState);
            setSuccessMessage('Data restored successfully! The application will now use the restored data.');
            setRestoreData('');
        } catch (err) {
            setError('Failed to restore data. The backup data may be corrupted or in an invalid format.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    };
    
    const labelClasses = "block text-sm font-medium text-slate-300 mb-2";

    return (
        <div role="tabpanel" className="animate-fade-in space-y-8">
            {error && <div className="p-3 bg-status-error-bg text-status-error border border-status-error-border rounded-md text-sm">{error}</div>}
            {successMessage && <div className="p-3 bg-status-success-bg text-status-success border border-status-success-border rounded-md text-sm">{successMessage}</div>}
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Create Backup Section */}
                <div>
                    <h3 className="flex items-center text-lg font-semibold text-slate-200">
                        <SaveIcon className="w-5 h-5 mr-2" />
                        Create Backup
                    </h3>
                    <p className="text-slate-400 mt-2 text-sm">
                        Generate a backup of the entire application state. Copy this text and save it in a safe place.
                    </p>
                    <button
                        onClick={handleCreateBackup}
                        disabled={isLoading}
                        className="mt-4 flex items-center px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover disabled:bg-slate-500 disabled:text-white transition"
                    >
                        <SaveIcon className="w-5 h-5 mr-2" />
                        {isLoading ? 'Generating...' : 'Generate Backup'}
                    </button>
                    {backupData && (
                        <div className="mt-4">
                            <textarea
                                readOnly
                                value={backupData}
                                className="w-full h-40 p-2 bg-slate-900 border border-slate-700 rounded-md font-mono text-xs text-slate-400 resize-none"
                                aria-label="Backup data"
                            />
                            <button
                                onClick={handleCopy}
                                className="mt-2 flex items-center px-3 py-1 bg-slate-600 text-white font-semibold rounded-md shadow-sm text-xs hover:bg-slate-500 transition"
                            >
                                {isCopied ? <CheckCircleIcon className="w-4 h-4 mr-2 text-green-400" /> : <CopyIcon className="w-4 h-4 mr-2" />}
                                {isCopied ? 'Copied!' : 'Copy to Clipboard'}
                            </button>
                        </div>
                    )}
                </div>

                {/* Restore from Backup Section */}
                <div>
                    <h3 className="flex items-center text-lg font-semibold text-slate-200">
                        <UploadIcon className="w-5 h-5 mr-2" />
                        Restore from Backup
                    </h3>
                    <p className="text-slate-400 mt-2 text-sm">
                        Paste your previously saved backup text here to restore the application state.
                    </p>
                    <div className="mt-4">
                        <label htmlFor="restore-data" className={labelClasses}>Backup Data</label>
                        <textarea
                            id="restore-data"
                            value={restoreData}
                            onChange={(e) => setRestoreData(e.target.value)}
                            className="w-full h-40 p-2 bg-slate-900 border border-slate-700 rounded-md font-mono text-xs text-slate-400 resize-none"
                            placeholder="Paste your backup text here..."
                            aria-label="Restore data"
                        />
                    </div>
                    <button
                        onClick={onRestore}
                        disabled={isLoading || !restoreData}
                        className="mt-4 flex items-center px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover disabled:bg-slate-500 disabled:text-white transition"
                    >
                        <UploadIcon className="w-5 h-5 mr-2" />
                        {isLoading ? 'Restoring...' : 'Restore Data'}
                    </button>
                </div>
            </div>

            {/* Migrate to Supabase Section */}
            <div className="mt-8 pt-8 border-t border-blue-500/20">
                <h3 className="flex items-center text-lg font-semibold text-blue-400">
                    <UploadIcon className="w-5 h-5 mr-2" />
                    Migrate Data to Supabase
                </h3>
                <p className="text-slate-400 mt-2 text-sm">
                    This will take the current data from the browser's local storage and push it to the configured Supabase database. This will overwrite any existing data in the Supabase tables.
                </p>
                <div className="mt-4 p-4 bg-blue-900/50 border border-blue-500/30 rounded-lg">
                    <p className="font-bold text-blue-300">Note: This is a one-way operation.</p>
                </div>
                <button
                    onClick={async () => {
                        if (!window.confirm("Are you sure you want to migrate data to Supabase? This will overwrite existing data in the database.")) return;
                        setIsLoading(true);
                        setError('');
                        setSuccessMessage('');
                        try {
                            const data = await api.getRawDataForBackup();
                            const result = await api.migrateToSupabase(data);
                            setSuccessMessage(result.message || 'Data migration started successfully!');
                        } catch (err: any) {
                            setError(err.message || 'An unknown error occurred during migration.');
                        } finally {
                            setIsLoading(false);
                        }
                    }}
                    disabled={isLoading}
                    className="mt-4 flex items-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-md shadow-md hover:bg-blue-700 disabled:bg-slate-500 transition"
                >
                    <UploadIcon className="w-5 h-5 mr-2" />
                    {isLoading ? 'Migrating...' : 'Migrate to Supabase'}
                </button>
            </div>

             {/* Reset Data Section */}
            <div className="mt-8 pt-8 border-t border-red-500/20">
                 <h3 className="flex items-center text-lg font-semibold text-red-400">
                    <RefreshCwIcon className="w-5 h-5 mr-2" />
                    Reset Application Data
                </h3>
                 <p className="text-slate-400 mt-2 text-sm">
                    This will permanently delete all data and restore the application to its initial state with demo content. This action cannot be undone.
                </p>
                <div className="mt-4 p-4 bg-status-error-bg border border-status-error-border rounded-lg">
                    <p className="font-bold text-status-error">Warning: This is a destructive action.</p>
                </div>
                 <button
                    onClick={handleReset}
                    disabled={isLoading}
                    className="mt-4 flex items-center px-4 py-2 bg-status-error text-white font-semibold rounded-md shadow-md hover:bg-red-600 disabled:bg-slate-500 transition"
                >
                    <XCircleIcon className="w-5 h-5 mr-2" />
                    Reset All Data
                </button>
            </div>
        </div>
    );
};

export default BackupRestorePage;