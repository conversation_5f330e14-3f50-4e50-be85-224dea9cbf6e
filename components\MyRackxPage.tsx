import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { AppState, Workshop, WorkshopInventoryItem, RequestItemDetail, Request, AISuggestion, Page, CatalogItem } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { WrenchIcon, SparklesIcon, FilePlusIcon, SendIcon, ClipboardListIcon, CheckCircleIcon } from './icons';
import RequestsPage from './RequestsPage';
import SmartReplenishmentModal from './SmartReplenishmentModal';
import StatCard from './StatCard';
import LogConsumptionModal from './LogConsumptionModal';
import { useTranslation } from '../i18n/I18nProvider';

interface MyRackxPageProps {
  workshopToShowId?: number; // Optional override for viewing other workshops
}

type WorkshopInventoryDisplayItem = WorkshopInventoryItem & { catalogItem: CatalogItem };

const MyRackxPage: React.FC<MyRackxPageProps> = ({ workshopToShowId }) => {
    const { appState } = useAppState();
    const { 
        handleLogConsumptionBatch, 
        handleCreateRequestBatch, 
        handleApproveRequest, 
        handleRejectRequest, 
        openRequestModal,
    } = useAppActions();
    const { t } = useTranslation();
    
    const { workshops, users, requests, currentUser, catalog, workshopInventories } = appState!;

    const displayWorkshopId = workshopToShowId ?? currentUser.primary_workshop_id;
    const canPerformActions = displayWorkshopId === currentUser.primary_workshop_id;

    const [isLogConsumptionModalOpen, setIsLogConsumptionModalOpen] = useState(false);
    const [consumptionItems, setConsumptionItems] = useState<Map<number, { quantity: number; name: string; cost: number }>>(new Map());
    const [isSmartReplenishModalOpen, setIsSmartReplenishModalOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');

    const selectedWorkshop = useMemo(() => {
        return displayWorkshopId ? workshops.find(w => w.id === displayWorkshopId) : null;
    }, [displayWorkshopId, workshops]);

    const teamLeader = useMemo(() => {
        if (!selectedWorkshop) return null;
        return users.find(u => u.primary_workshop_id === selectedWorkshop.id && u.role === 'TeamLeader');
    }, [selectedWorkshop, users]);

    const workshopInventory = useMemo(() => {
        if (!selectedWorkshop) return [];
        return workshopInventories
            .filter(wi => wi.workshop_id === selectedWorkshop.id)
            .map((wi): WorkshopInventoryDisplayItem | null => {
                const catalogItem = catalog.find(c => c.id === wi.item_id);
                if (!catalogItem) return null;
                return { ...wi, catalogItem };
            })
            .filter((item): item is WorkshopInventoryDisplayItem => item !== null)
            .sort((a, b) => (a.catalogItem.name || '').localeCompare(b.catalogItem.name || ''));
    }, [selectedWorkshop, workshopInventories, catalog]);
    
    const filteredInventory = useMemo(() => {
        if (!searchTerm.trim()) {
            return workshopInventory;
        }
        const lowercasedFilter = searchTerm.toLowerCase();
        return workshopInventory.filter(item => {
            const nameMatch = item.catalogItem.name.toLowerCase().includes(lowercasedFilter);
            const skuMatch = item.catalogItem.sku.toLowerCase().includes(lowercasedFilter);
            const idMatch = item.item_id.toString().includes(lowercasedFilter);
            return nameMatch || skuMatch || idMatch;
        });
    }, [workshopInventory, searchTerm]);

    const teamLeaderStats = useMemo(() => ({
        pendingRequests: requests.filter(r => r.status === 'Pending' && ((r.to_workshop_id === displayWorkshopId) || (r.workshop_id === displayWorkshopId))).length,
    }), [requests, displayWorkshopId]);
    
    const operatorStats = useMemo(() => ({
        myPendingRequests: requests.filter(r => r.created_by_user_id === currentUser.id && r.status === 'Pending').length,
    }), [requests, currentUser.id]);

    useEffect(() => {
        setConsumptionItems(new Map());
        setSearchTerm('');
    }, [displayWorkshopId]);

    const handleLogConsumption = async (reason?: string) => {
        if (consumptionItems.size === 0 || !displayWorkshopId) return;
        const itemsToLog: RequestItemDetail[] = Array.from(consumptionItems.entries()).map(([item_id, { quantity }]) => ({ item_id, quantity }));
        const totalCost = Array.from(consumptionItems.values()).reduce((sum, item) => sum + item.quantity * item.cost, 0);
        await handleLogConsumptionBatch(displayWorkshopId, itemsToLog, totalCost, reason);
        setConsumptionItems(new Map());
        setIsLogConsumptionModalOpen(false);
    };

    const handleConsumptionSubmitClick = () => {
        if (currentUser.role === 'Operator' && canPerformActions) {
            setIsLogConsumptionModalOpen(true);
        } else {
            handleLogConsumption('Consumption logged by Team Leader.');
        }
    };

    const updateConsumptionItem = (item: WorkshopInventoryDisplayItem, quantity: number) => {
        if (isNaN(quantity) || quantity > item.quantity) return;
        setConsumptionItems(prev => {
            const newMap = new Map(prev);
            const itemId = item.item_id;

            if (quantity <= 0) {
                newMap.delete(itemId);
            } else {
                newMap.set(itemId, { 
                    quantity, 
                    name: item.catalogItem.name || "Unknown", 
                    cost: item.catalogItem.default_unit_price || 0 
                });
            }
            return newMap;
        });
    };
    
    const consumptionTotalCost = useMemo(() => {
        return Array.from(consumptionItems.values()).reduce((sum: number, item: { quantity: number; name: string; cost: number; }) => sum + item.quantity * item.cost, 0);
    }, [consumptionItems]);

    if (!selectedWorkshop) {
        return (
            <div className="animate-fade-in text-center py-16">
                <WrenchIcon className="mx-auto h-12 w-12 text-slate-500" />
                <h2 className="text-2xl font-bold text-slate-100">{t('myRackx.noWorkshop')}</h2>
                <p className="text-slate-400 mt-2">{t('myRackx.noWorkshopDesc')}</p>
            </div>
        );
    }
    
    const renderStats = () => {
        if (currentUser.role === 'TeamLeader') {
            return <StatCard icon={<ClipboardListIcon />} title={t('myRackx.pendingTeamRequests')} value={teamLeaderStats.pendingRequests} color="text-status-warning" />;
        }
        if (currentUser.role === 'Operator') {
            return <StatCard icon={<ClipboardListIcon />} title={t('myRackx.myPendingRequests')} value={operatorStats.myPendingRequests} />;
        }
        return null;
    }
    
    return (
        <>
        <div className="flex flex-col h-full">
            <div className="[background-image:var(--card-bg-data)] backdrop-blur-md rounded-lg shadow-lg border border-slate-800 overflow-hidden animate-fade-in flex flex-col flex-grow">
                <div className="p-4 sm:p-6 border-b border-slate-700/50 flex-shrink-0 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                    <div>
                        <h2 className="text-3xl font-bold text-slate-100">{t('myRackx.pageTitle', { name: selectedWorkshop.name })}</h2>
                        <p className="text-slate-400">
                            {teamLeader ? t('myRackx.managedBy', { name: teamLeader.name }) : t('myRackx.noLead')}
                            {canPerformActions ? t('myRackx.yourWorkspace') : t('myRackx.viewingWorkspace')}
                        </p>
                    </div>
                    {currentUser.role === 'TeamLeader' && (
                        <button 
                            onClick={() => setIsSmartReplenishModalOpen(true)}
                            disabled={!canPerformActions}
                            className="flex-shrink-0 flex items-center gap-2 px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition disabled:bg-slate-500 disabled:cursor-not-allowed">
                            <SparklesIcon className="w-5 h-5"/> {t('myRackx.smartReplenish')}
                        </button>
                    )}
                     {currentUser.role !== 'Operator' && (
                        <button 
                            onClick={openRequestModal}
                            disabled={!canPerformActions}
                            className="flex-shrink-0 flex items-center justify-center gap-2 px-4 py-2 bg-brand-accent/20 text-brand-accent border border-brand-accent/50 font-semibold rounded-md shadow-md hover:bg-brand-accent/50 hover:text-amber-300 transition disabled:opacity-50 disabled:cursor-not-allowed">
                            <FilePlusIcon className="w-5 h-5" /> {t('myRackx.requestTransfer')}
                        </button>
                    )}
                </div>

                <div className="overflow-y-auto flex-grow p-4 sm:p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2 bg-slate-900/50 p-4 rounded-lg border border-slate-700">
                             <div className="flex justify-between items-center mb-4">
                                <h3 className="text-xl font-semibold text-slate-200">{t('myRackx.inventoryTitle')}</h3>
                                <input
                                    type="search"
                                    placeholder={t('myRackx.searchPlaceholder')}
                                    value={searchTerm}
                                    onChange={e => setSearchTerm(e.target.value)}
                                    className="w-full md:w-64 bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition"
                                />
                            </div>
                            <div className="overflow-auto max-h-[60vh]">
                                <table className="w-full text-sm">
                                    <thead className="sticky top-0 bg-slate-800 text-xs text-slate-400 uppercase">
                                        <tr>
                                            <th className="px-4 py-2 text-start">{t('myRackx.tableHeaderItem')}</th>
                                            <th className="px-4 py-2 text-center">{t('myRackx.tableHeaderStock')}</th>
                                            <th className="px-4 py-2 text-center">{t('myRackx.tableHeaderConsume')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredInventory.length > 0 ? (
                                            filteredInventory.map(item => (
                                                <tr key={item.id} className="border-b border-slate-700/50">
                                                    <td className="px-4 py-3"><p className="font-medium text-slate-200">{item.catalogItem.name}</p><p className="text-xs text-slate-500 font-mono">{item.catalogItem.sku}</p></td>
                                                    <td className="px-4 py-3 text-center text-lg font-bold text-slate-300">{item.quantity}</td>
                                                    <td className="px-4 py-3 text-center">
                                                        <input type="number" min="0" max={item.quantity} value={consumptionItems.get(item.item_id)?.quantity || ''} onChange={e => updateConsumptionItem(item, e.target.value === '' ? 0 : parseInt(e.target.value, 10))} className="w-20 bg-slate-700 border border-slate-600 rounded-md p-1.5 text-center text-slate-100 focus:ring-2 focus:ring-sky-500 disabled:bg-slate-800 disabled:cursor-not-allowed" placeholder="0" disabled={!canPerformActions}/>
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td colSpan={3} className="text-center py-8 text-slate-400">
                                                    {searchTerm ? t('myRackx.noSearchResults', { term: searchTerm }) : t('myRackx.noInventory')}
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div className="space-y-6">
                            {currentUser.role !== 'Admin' && <div className="grid grid-cols-1 gap-6">{renderStats()}</div>}
                            
                            <div className="bg-slate-900/50 p-4 rounded-lg border border-slate-700">
                                <h3 className="text-xl font-semibold text-slate-200 mb-4">{t('myRackx.logConsumptionTitle')}</h3>
                                {consumptionItems.size > 0 && canPerformActions ? (
                                    <>
                                        <ul className="space-y-2 max-h-40 overflow-y-auto pe-2">{Array.from(consumptionItems.entries()).map(([id, {quantity, name}]) => (<li key={id} className="flex justify-between items-center text-sm"><span className="text-slate-300">{name}</span><span className="font-mono text-slate-400">x{quantity}</span></li>))}</ul>
                                        <div className="mt-4 pt-4 border-t border-slate-700/50">
                                            <p className="text-lg font-bold text-slate-200">{t('myRackx.logConsumptionTotal')} <span className="text-green-400">€{consumptionTotalCost.toFixed(2)}</span></p>
                                            <button onClick={handleConsumptionSubmitClick} className="w-full mt-3 px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition flex items-center justify-center gap-2">
                                                <CheckCircleIcon className="w-5 h-5" />
                                                {t('myRackx.logConsumptionSubmit')}
                                            </button>
                                        </div>
                                    </>
                                ) : (<p className="text-slate-400 text-sm">{canPerformActions ? t('myRackx.logConsumptionPlaceholder') : t('myRackx.logConsumptionDisabled')}</p>)}
                            </div>

                            <div className="bg-slate-900/50 rounded-lg border border-slate-700">
                                <div className="p-4"><h3 className="text-xl font-semibold text-slate-200">{t('myRackx.requestsTitle')}</h3></div>
                                <RequestsPage appState={appState} onApproveRequest={handleApproveRequest} onRejectRequest={handleRejectRequest} isEmbedded={true} filterByRackXId={displayWorkshopId} />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
         <SmartReplenishmentModal isOpen={isSmartReplenishModalOpen} onClose={() => setIsSmartReplenishModalOpen(false)} rackx={selectedWorkshop} onSmartReplenish={handleCreateRequestBatch} />
         <LogConsumptionModal
            isOpen={isLogConsumptionModalOpen}
            onClose={() => setIsLogConsumptionModalOpen(false)}
            onSubmit={handleLogConsumption}
         />
        </>
    );
};

export default MyRackxPage;