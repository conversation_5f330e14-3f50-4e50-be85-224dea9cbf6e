import React, { useMemo, useState, useEffect } from 'react';
import { LayoutDashboardIcon, BarChartIcon, TrendingUpIcon, EuroIcon, AlertTriangleIcon, WrenchIcon, ClipboardListIcon, UserIcon } from './icons';
import StatCard from './StatCard';
import ActivityFeed from './ActivityFeed';
import PendingApprovalsWidget from './PendingApprovalsWidget';
import { AppState, Page, Request, CatalogItem, Workshop, TopConsumedItem, Program, SubProgram } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { useTranslation } from '../i18n/I18nProvider';
import ProgramDashboard from './ProgramDashboard';
import { PROGRAM_TAB_COLORS, STAT_CARD_THEMES } from '../constants';
import { getProgramColor } from '../utils';

export const MONOTONE_BLUE_GRADIENTS = [
    { from: '#22d3ee', to: '#06b6d4' }, // cyan 400-500
    { from: '#38bdf8', to: '#0ea5e9' }, // sky 400-500
    { from: '#60a5fa', to: '#3b82f6' }, // blue 400-500
    { from: '#7dd3fc', to: '#38bdf8' }, // sky 300-400
    { from: '#0e7490', to: '#0369a1' }, // cyan 700 - sky 700
];


// Reusable Donut Chart Component
export const DonutChart: React.FC<{ data: { label: string; value: number; gradient: { from: string, to: string } }[]; valuePrefix?: string, totalLabel: string, noDataLabel: string }> = ({ data, valuePrefix = '', totalLabel, noDataLabel }) => {
    const total = data.reduce((acc, d) => acc + d.value, 0);
    if (total === 0) return <div className="flex items-center justify-center h-full text-slate-500">{noDataLabel}</div>;
    let accumulated = 0;
    
    const formatValue = (val: number) => {
        return val.toLocaleString(undefined, {
            minimumFractionDigits: valuePrefix ? 2 : 0,
            maximumFractionDigits: valuePrefix ? 2 : 0
        });
    };

    const isSingle = data.length === 1;

    return (
        <div className="flex items-center gap-5">
            <div className="relative w-[108px] h-[108px]">
                <svg width="108" height="108" viewBox="0 0 36 36" className="block transform -rotate-90">
                    <defs>
                        {data.map(({ gradient }, index) => (
                            <linearGradient key={index} id={`donut-gradient-${index}`} x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" stopColor={gradient.from} />
                                <stop offset="100%" stopColor={gradient.to} />
                            </linearGradient>
                        ))}
                    </defs>
                    {data.map(({ value }, index) => {
                        const percentage = (value / total) * 100;
                        const dashArray = `${percentage} ${100 - percentage}`;
                        const dashOffset = 25 - accumulated;
                        accumulated += percentage;
                        return (
                            <circle
                                key={index}
                                cx="18" cy="18" r="15.915"
                                fill="transparent" stroke={`url(#donut-gradient-${index})`} strokeWidth="4"
                                strokeDasharray={dashArray} strokeDashoffset={dashOffset}
                                style={{transition: 'stroke-dasharray 0.5s ease-in-out'}}
                            />
                        );
                    })}
                </svg>
                 <div className="absolute inset-0 flex items-center justify-center">
                     <div className="text-center">
                        <span className="font-extrabold text-lg text-brand-accent">{valuePrefix}{formatValue(total)}</span>
                        <span className="block text-xs text-slate-400">{totalLabel}</span>
                     </div>
                 </div>
            </div>
            
            <div className="text-sm">
                {isSingle ? (
                     <div className="flex items-center gap-2">
                        <span className="w-2.5 h-2.5 rounded-full" style={{ background: `linear-gradient(to bottom, ${data[0].gradient.from}, ${data[0].gradient.to})` }}></span>
                        <span className="text-slate-300">{data[0].label}:</span>
                        <span className="font-semibold text-slate-100">{valuePrefix}{formatValue(data[0].value)}</span>
                    </div>
                ) : (
                    <ul className="space-y-2">
                        {data.map(({ label, value, gradient }) => (
                            <li key={label} className="flex items-center gap-2">
                                <span className="w-2.5 h-2.5 rounded-full" style={{ background: `linear-gradient(to bottom, ${gradient.from}, ${gradient.to})` }}></span>
                                <span className="text-slate-400 truncate max-w-[100px]" title={label}>{label}:</span>
                                <span className="font-semibold text-slate-200">{valuePrefix}{formatValue(value)}</span>
                            </li>
                        ))}
                    </ul>
                )}
            </div>
        </div>
    );
};

// Reusable Bar Chart Component
export const HorizontalBarChart: React.FC<{ data: { label: string; value: number }[], gradient: string, unit?: string, noDataLabel: string }> = ({ data, gradient, unit="", noDataLabel }) => {
    const maxValue = Math.max(...data.map(d => d.value), 0);
    if (maxValue === 0) return <div className="flex items-center justify-center h-full text-slate-500">{noDataLabel}</div>;

    return (
        <div className="space-y-3">
            {data.map(({ label, value }) => (
                <div key={label} className="grid grid-cols-3 gap-2 items-center text-sm">
                    <p className="col-span-1 truncate text-slate-400" title={label}>{label}</p>
                    <div className="col-span-2 flex items-center gap-3">
                        <div className="w-full bg-slate-700/50 rounded-full h-3">
                            <div
                                className={`rounded-full h-3 bg-gradient-to-r ${gradient} transition-all duration-500`}
                                style={{ width: `${(value / maxValue) * 100}%` }}
                            ></div>
                        </div>
                        <p className="font-semibold text-slate-200 w-12 text-right">{value}{unit}</p>
                    </div>
                </div>
            ))}
        </div>
    );
}

// Reusable Line Chart Component for Spend Trend
export const LineChart: React.FC<{ data: { label: string; value: number }[], noDataLabel: string }> = ({ data, noDataLabel }) => {
    const width = 600;
    const height = 250;
    const padding = { top: 20, right: 20, bottom: 30, left: 50 };
    const chartColor = "#0ea5e9"; // theme('colors.brand-primary')

    const maxValue = Math.max(...data.map(d => d.value), 0);
    if (maxValue === 0) {
        return <div className="flex items-center justify-center h-[250px] text-slate-500">{noDataLabel}</div>;
    }

    const xScale = (index: number) => padding.left + (index / (data.length - 1)) * (width - padding.left - padding.right);
    const yScale = (value: number) => height - padding.bottom - (value / maxValue) * (height - padding.top - padding.bottom);

    const pathData = data.map((d, i) => `${i === 0 ? 'M' : 'L'} ${xScale(i)} ${yScale(d.value)}`).join(' ');
    const areaPathData = `M ${xScale(0)} ${height - padding.bottom} ${pathData.substring(1)} L ${xScale(data.length - 1)} ${height - padding.bottom} Z`;

    const yAxisLabels = Array.from({ length: 4 }, (_, i) => {
        const value = (maxValue / 3) * i;
        return { value: Math.round(value), y: yScale(value) };
    });

    const xAxisLabels = data.map((d, i) => {
        if (i % Math.ceil(data.length / 7) === 0 || i === data.length - 1) { // Show about 7-8 labels
            return { label: d.label, x: xScale(i) };
        }
        return null;
    }).filter(Boolean);

    return (
        <div className="w-full overflow-x-auto">
            <svg width="100%" height={height} viewBox={`0 0 ${width} ${height}`} preserveAspectRatio="xMidYMid meet">
                <defs>
                    <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor={chartColor} stopOpacity="0.3" />
                        <stop offset="100%" stopColor={chartColor} stopOpacity="0" />
                    </linearGradient>
                </defs>
                {/* Y-axis grid lines and labels */}
                {yAxisLabels.map(label => (
                    <g key={label.value}>
                        <line x1={padding.left} y1={label.y} x2={width - padding.right} y2={label.y} className="stroke-slate-700" strokeDasharray="3,3" />
                        <text x={padding.left - 8} y={label.y + 4} textAnchor="end" className="fill-slate-400" fontSize="10">€{label.value}</text>
                    </g>
                ))}
                 {/* X-axis labels */}
                {xAxisLabels.map(label => (
                    label && <text key={label.label} x={label.x} y={height - padding.bottom + 15} textAnchor="middle" className="fill-slate-400" fontSize="10">{label.label}</text>
                ))}

                <path d={areaPathData} fill="url(#areaGradient)" />
                <path d={pathData} fill="none" stroke={chartColor} strokeWidth="2" />
                {data.map((d, i) => (
                    <circle key={i} cx={xScale(i)} cy={yScale(d.value)} r="3" fill={chartColor} className="stroke-slate-900" strokeWidth="1" />
                ))}
            </svg>
        </div>
    );
};

export const DashboardCard: React.FC<{children: React.ReactNode, className?: string}> = ({children, className}) => (
    <div className={`[background-image:var(--card-bg-dashboard)] backdrop-blur-md rounded-2xl border border-slate-800 shadow-card transition-all duration-300 hover:border-brand-accent/30 hover:shadow-[0_0_20px_rgba(34,211,238,0.15)] hover:-translate-y-1 ${className}`}>
        {children}
    </div>
);


const DashboardPage: React.FC<{ setActivePage: (page: Page) => void }> = ({ setActivePage }) => {
  const { appState } = useAppState();
  const { handleApproveRequest, handleRejectRequest } = useAppActions();
  const { t } = useTranslation();
  const { currentUser, requests, catalog, auditLog, users, workshops, workshopInventories, programs, subPrograms } = appState!;
  const [activeTab, setActiveTab] = useState<number | 'general'>('general');

  const requestStats = useMemo(() => ({
    pending: requests.filter(r => r.status === 'Pending').length,
    approved: requests.filter(r => r.status === 'Approved').length,
    rejected: requests.filter(r => r.status === 'Rejected').length,
  }), [requests]);

  const topConsumed = useMemo(() => {
      const consumptionMap = new Map<number, number>();
      requests.filter(req => req.type === 'CONSUMPTION' && req.status === 'Approved')
        .forEach(req => req.items.forEach(item => consumptionMap.set(item.item_id, (consumptionMap.get(item.item_id) || 0) + item.quantity)));
      const sortedItems = Array.from(consumptionMap.entries()).sort(([, qtyA], [, qtyB]) => qtyB - qtyA).slice(0, 5);
      return sortedItems.map(([itemId, quantity]) => {
          const itemDetails = catalog.find(c => c.id === itemId);
          return { id: itemId, name: itemDetails?.name || 'Unknown', sku: itemDetails?.sku || 'N/A', quantity };
      });
  }, [requests, catalog]);

  const totalInventoryValue = useMemo(() => {
      const catalogPriceMap = new Map(catalog.map(i => [i.id, i.default_unit_price]));
      return workshopInventories.reduce((acc, inv) => {
          const price = catalogPriceMap.get(inv.item_id) || 0;
          return acc + (inv.quantity * price);
      }, 0);
  }, [workshopInventories, catalog]);

  const lowStockItemsCount = useMemo(() =>
      workshopInventories.filter(i => i.quantity > 0 && i.quantity < i.low_stock_threshold).length
  , [workshopInventories]);

  const totalLocalWorkshops = useMemo(() =>
      workshops.filter(w => w.type === 'Local').length
  , [workshops]);

  const workshopHealth = useMemo(() =>
      workshops
          .filter(w => w.type === 'Local')
          .map(w => {
              const lowStockItems = workshopInventories.filter(i => i.workshop_id === w.id && i.quantity > 0 && i.quantity < i.low_stock_threshold).length;
              const teamLead = users.find(u => u.primary_workshop_id === w.id && u.role === 'TeamLeader');
              return { id: w.id, name: w.name, lowStockCount: lowStockItems, teamLeadName: teamLead?.name };
          })
          .sort((a, b) => b.lowStockCount - a.lowStockCount)
  , [workshops, workshopInventories, users]);

  const { spendTrendByProgram, spendByWorkshopByProgram } = useMemo(() => {
      const subProgramMap = new Map(subPrograms.map(sp => [sp.id, sp]));
      const workshopToProgramMap = new Map<number, number>();
      workshops.forEach(w => {
          const subProgram = subProgramMap.get(w.sub_program_id);
          if (subProgram) {
              workshopToProgramMap.set(w.id, subProgram.program_id);
          }
      });

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const approvedConsumptionsLast30Days = requests.filter(r =>
          r.type === 'CONSUMPTION' &&
          r.status === 'Approved' &&
          new Date(r.created_at) >= thirtyDaysAgo
      );

      const newSpendTrendByProgram = (() => {
          const spendByProgramByDate = new Map<number, Map<string, number>>();
          programs.forEach(p => spendByProgramByDate.set(p.id, new Map()));

          const today = new Date();
          today.setHours(0, 0, 0, 0);

          for (let i = 0; i < 30; i++) {
              const date = new Date(today);
              date.setDate(today.getDate() - i);
              const dateKey = date.toISOString().split('T')[0];
              programs.forEach(p => {
                  spendByProgramByDate.get(p.id)!.set(dateKey, 0);
              });
          }

          approvedConsumptionsLast30Days.forEach(req => {
              if (req.total_cost && req.workshop_id) {
                  const programId = workshopToProgramMap.get(req.workshop_id);
                  const reqDate = new Date(req.created_at);
                  reqDate.setHours(0, 0, 0, 0);
                  const dateKey = reqDate.toISOString().split('T')[0];

                  if (programId && spendByProgramByDate.has(programId)) {
                      const programDateMap = spendByProgramByDate.get(programId)!;
                      if (programDateMap.has(dateKey)) {
                          programDateMap.set(dateKey, programDateMap.get(dateKey)! + req.total_cost);
                      }
                  }
              }
          });

          const result = new Map<number, { label: string; value: number }[]>();
          const formatLabel = (dateString: string) => {
              const date = new Date(dateString);
              return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
          };

          spendByProgramByDate.forEach((dateMap, programId) => {
              const sortedData = Array.from(dateMap.entries())
                  .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                  .map(([date, spend]) => ({ label: formatLabel(date), value: parseFloat(spend.toFixed(2)) }));
              result.set(programId, sortedData);
          });

          return result;
      })();

      const newSpendByWorkshopByProgram = (() => {
          const programSpendMap = new Map<number, Map<number, number>>();
          approvedConsumptionsLast30Days.forEach(req => {
              if (req.total_cost && req.workshop_id) {
                  const programId = workshopToProgramMap.get(req.workshop_id);
                  if (programId) {
                      if (!programSpendMap.has(programId)) {
                          programSpendMap.set(programId, new Map());
                      }
                      const workshopSpendMap = programSpendMap.get(programId)!;
                      const currentSpend = workshopSpendMap.get(req.workshop_id) || 0;
                      workshopSpendMap.set(req.workshop_id, currentSpend + req.total_cost);
                  }
              }
          });

          const workshopNameMap = new Map(workshops.map(w => [w.id, w.name]));
          const result = new Map<number, { workshopName: string, spend: number }[]>();
          programSpendMap.forEach((workshopSpendMap, programId) => {
              const programData = Array.from(workshopSpendMap.entries()).map(([workshopId, spend]) => ({
                  workshopName: workshopNameMap.get(workshopId) || `RackX #${workshopId}`,
                  spend: parseFloat(spend.toFixed(2)),
              }))
              .filter(d => d.spend > 0)
              .sort((a,b) => b.spend - a.spend);

              result.set(programId, programData);
          });

          return result;
      })();

      return {
          spendTrendByProgram: newSpendTrendByProgram,
          spendByWorkshopByProgram: newSpendByWorkshopByProgram,
      };
  }, [requests, workshops, programs, subPrograms]);
  
  const donutData = [
      { label: t('dashboard.donutPending'), value: requestStats.pending, gradient: MONOTONE_BLUE_GRADIENTS[1] },
      { label: t('dashboard.donutApproved'), value: requestStats.approved, gradient: MONOTONE_BLUE_GRADIENTS[0] },
      { label: t('dashboard.donutRejected'), value: requestStats.rejected, gradient: MONOTONE_BLUE_GRADIENTS[2] },
  ];

  const showApprovalWidget = currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader' || currentUser.role === 'TeamLeader';

  const getHealthStatusColor = (count: number) => {
      if (count > 3) return 'bg-blue-600';
      if (count > 0) return 'bg-sky-500';
      return 'bg-cyan-400';
  }
  
  const getHealthStatusText = (count: number) => {
      if (count > 3) return t('dashboard.workshopHealthCritical', { count });
      if (count > 0) return t('dashboard.workshopHealthLowStock', { count });
      return t('dashboard.workshopHealthHealthy');
  };

  const renderGeneralView = () => {
    const theme = STAT_CARD_THEMES[0];
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          <StatCard icon={<EuroIcon />} title={t('dashboard.totalInventoryValue')} value={`€${totalInventoryValue.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`} color={theme[0].color} gradient={theme[0].gradient} />
          <StatCard icon={<AlertTriangleIcon />} title={t('dashboard.lowStockItems')} value={`${lowStockItemsCount} ${t('dashboard.itemsUnit')}`} color={theme[1].color} gradient={theme[1].gradient} />
          <StatCard icon={<ClipboardListIcon />} title={t('dashboard.pendingRequests')} value={`${requestStats.pending} ${t('dashboard.requestsUnit')}`} color={theme[2].color} gradient={theme[2].gradient}/>
          <StatCard icon={<WrenchIcon />} title={t('dashboard.localWorkshops')} value={`${totalLocalWorkshops} ${t('dashboard.rackxUnit')}`} color={theme[3].color} gradient={theme[3].gradient} />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow">
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
              {programs.map(program => {
                  const trendData = spendTrendByProgram.get(program.id);
                  const workshopSpendData = spendByWorkshopByProgram.get(program.id);
                  if (!trendData) return null;
                  return (
                      <DashboardCard key={program.id} className="flex flex-col">
                          <div className="p-8">
                              <h3 className="text-base font-semibold text-slate-200 flex items-center mb-4">
                                  <TrendingUpIcon className="w-5 h-5 mr-2 text-sky-400" />
                                  {t('dashboard.spendTrendTitle', { programName: program.name })}
                              </h3>
                              <LineChart data={trendData} noDataLabel={t('dashboard.spendTrendNoData')} />
                          </div>

                          {workshopSpendData && workshopSpendData.length > 0 && (
                              <div className="mt-auto p-8 border-t border-slate-800">
                                  <h4 className="text-md font-semibold text-slate-200 flex items-center mb-4">
                                      <WrenchIcon className="w-5 h-5 mr-2 text-sky-400" />
                                      {t('dashboard.rackxSpendTitle')}
                                  </h4>
                                  <DonutChart data={
                                      workshopSpendData.map((d, i) => ({
                                          label: d.workshopName,
                                          value: d.spend,
                                          gradient: MONOTONE_BLUE_GRADIENTS[i % MONOTONE_BLUE_GRADIENTS.length]
                                      }))
                                  } valuePrefix="€" totalLabel={t('dashboard.donutTotal')} noDataLabel={t('dashboard.noData')} />
                              </div>
                          )}
                      </DashboardCard>
                  );
              })}
              <DashboardCard className="p-8">
                  <h3 className="text-base font-semibold text-slate-200 flex items-center mb-4">
                      <BarChartIcon className="w-5 h-5 mr-2 text-sky-400" /> {t('dashboard.topConsumedTitle')}
                  </h3>
                  <HorizontalBarChart data={topConsumed.map(item => ({ label: item.name, value: item.quantity }))} gradient="from-sky-400 to-cyan-400" noDataLabel={t('dashboard.noData')} />
              </DashboardCard>
              <DashboardCard className="p-8">
                  <h3 className="text-base font-semibold text-slate-200 flex items-center mb-4">
                      <LayoutDashboardIcon className="w-5 h-5 mr-2 text-sky-400" /> {t('dashboard.workshopHealthTitle')}
                  </h3>
                  <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
                      {workshopHealth.map(w => (
                          <div key={w.id} className="flex items-center justify-between p-2.5 bg-slate-800/60 rounded-md">
                              <div>
                                  <span className="text-slate-300 font-medium">{w.name}</span>
                                  {w.teamLeadName && <p className="text-xs text-slate-500">{t('dashboard.workshopHealthLead', { name: w.teamLeadName })}</p>}
                              </div>
                              <div className="flex items-center gap-2">
                                  <span className="text-sm font-semibold text-slate-400">
                                      {getHealthStatusText(w.lowStockCount)}
                                  </span>
                                  <div className={`w-3 h-3 rounded-full ${getHealthStatusColor(w.lowStockCount)}`}></div>
                              </div>
                          </div>
                      ))}
                  </div>
              </DashboardCard>
          </div>

          <div className="lg:col-span-1 flex flex-col gap-6">
              <DashboardCard className="p-8">
                  <h3 className="text-base font-semibold text-slate-200 flex items-center mb-4">
                      <TrendingUpIcon className="w-5 h-5 mr-2 text-sky-400" /> {t('dashboard.requestStatusTitle')}
                  </h3>
                  <DonutChart data={donutData} totalLabel={t('dashboard.donutTotal')} noDataLabel={t('dashboard.noData')} />
              </DashboardCard>
              {showApprovalWidget && (
                  <PendingApprovalsWidget appState={appState!} onApprove={handleApproveRequest} onReject={handleRejectRequest} />
              )}
              <ActivityFeed auditLog={auditLog} users={users} catalog={catalog} workshops={workshops} />
          </div>
        </div>
      </div>
    );
  };

  const renderProgramView = (programId: number) => {
    const program = programs.find(p => p.id === programId);
    if (!program) return null;
    return <ProgramDashboard programName={program.name} />;
  };

  return (
    <div className="animate-fade-in flex flex-col h-full space-y-6">
      <div>
        <h1 className="text-3xl font-black tracking-tighter bg-clip-text text-transparent bg-gradient-to-br from-slate-50 to-slate-400">{t('dashboard.title')}</h1>
        <p className="text-slate-400 mt-1">{t('dashboard.welcome', { name: currentUser.name })}</p>
      </div>

      <div className="flex space-x-1 rounded-lg bg-slate-800 p-1">
        <button
          onClick={() => setActiveTab('general')}
          className={`px-4 rounded-lg py-2 text-sm font-medium leading-5 text-white ${activeTab === 'general' ? 'bg-brand-accent' : 'text-slate-300 hover:bg-slate-700'}`}
          style={activeTab === 'general' ? { backgroundColor: PROGRAM_TAB_COLORS[0] } : {}}
        >
          General View
        </button>
        {programs.map((program) => {
            const isActive = activeTab === program.id;
            const programColor = getProgramColor(program.id, programs);
            return (
                <button
                  key={program.id}
                  onClick={() => setActiveTab(program.id)}
                  className={`px-4 rounded-lg py-2 text-sm font-medium leading-5 text-white ${!isActive ? 'text-slate-300 hover:bg-slate-700' : ''}`}
                  style={isActive ? { backgroundColor: programColor } : {}}
                >
                  {program.name}
                </button>
            );
        })}
      </div>

      <div className="mt-2">
        {activeTab === 'general' && renderGeneralView()}
        {typeof activeTab === 'number' && renderProgramView(activeTab)}
      </div>
    </div>
  );
};

export default DashboardPage;