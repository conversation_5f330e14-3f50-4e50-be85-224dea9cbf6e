import React, { useState, FormEvent, useEffect, useMemo } from 'react';
import { Workshop, UserType, UserRole } from '../types';
import { XIcon, UsersIcon } from './icons';
import { useAppState } from '../context/AppContext';

interface CreateTeamModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (teamData: { workshopId: number; teamLeaderId: number; memberIds: number[] }) => void;
}

const CreateTeamModal: React.FC<CreateTeamModalProps> = ({ isOpen, onClose, onSave }) => {
    const { appState } = useAppState();
    const { workshops, users } = appState!;

    const [selectedWorkshopId, setSelectedWorkshopId] = useState<number | null>(null);
    const [selectedTeamLeaderId, setSelectedTeamLeaderId] = useState<number | null>(null);
    const [selectedMemberIds, setSelectedMemberIds] = useState<number[]>([]);
    const [error, setError] = useState<string | null>(null);

    const unassignedUsers = useMemo(() => {
        return users.filter(u => u.primary_workshop_id === 1 && u.role !== 'Admin' && u.role !== 'Manager'); // Assuming workshop 1 is the 'unassigned' pool
    }, [users]);

    const potentialTeamLeaders = useMemo(() => {
        return unassignedUsers.filter(u => u.role === 'TeamLeader');
    }, [unassignedUsers]);

    useEffect(() => {
        if (isOpen) {
            setSelectedWorkshopId(null);
            setSelectedTeamLeaderId(null);
            setSelectedMemberIds([]);
            setError(null);
        }
    }, [isOpen]);

    if (!isOpen) return null;

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (!selectedWorkshopId || !selectedTeamLeaderId) {
            setError('A workshop and a team leader must be selected.');
            return;
        }
        onSave({
            workshopId: selectedWorkshopId,
            teamLeaderId: selectedTeamLeaderId,
            memberIds: selectedMemberIds,
        });
    };

    const handleMemberToggle = (userId: number) => {
        setSelectedMemberIds(prev =>
            prev.includes(userId) ? prev.filter(id => id !== userId) : [...prev, userId]
        );
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-lg relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100 flex items-center"><UsersIcon className="w-6 h-6 mr-2" /> Create New Team</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
                        <div>
                            <label htmlFor="workshopId" className={labelClasses}>Assign to Workshop</label>
                            <select id="workshopId" value={selectedWorkshopId ?? ''} onChange={e => setSelectedWorkshopId(Number(e.target.value))} className={inputClasses}>
                                <option value="" disabled>Select a workshop...</option>
                                {workshops.filter(w => w.type === 'Local').map(w => <option key={w.id} value={w.id}>{w.name}</option>)}
                            </select>
                        </div>

                        <div>
                            <label htmlFor="teamLeaderId" className={labelClasses}>Select Team Leader</label>
                            <select id="teamLeaderId" value={selectedTeamLeaderId ?? ''} onChange={e => setSelectedTeamLeaderId(Number(e.target.value))} className={inputClasses} disabled={potentialTeamLeaders.length === 0}>
                                <option value="" disabled>Select a team leader...</option>
                                {potentialTeamLeaders.map(u => <option key={u.id} value={u.id}>{u.name}</option>)}
                            </select>
                            {potentialTeamLeaders.length === 0 && <p className="text-xs text-slate-400 mt-1">No unassigned Team Leaders available.</p>}
                        </div>

                        <div>
                            <label className={labelClasses}>Select Team Members</label>
                            <div className="max-h-48 overflow-y-auto bg-slate-900/50 p-2 rounded-md border border-slate-600">
                                {unassignedUsers.filter(u => u.id !== selectedTeamLeaderId).map(user => (
                                    <div key={user.id} className="flex items-center p-2 rounded-md hover:bg-slate-700">
                                        <input
                                            type="checkbox"
                                            id={`member-${user.id}`}
                                            checked={selectedMemberIds.includes(user.id)}
                                            onChange={() => handleMemberToggle(user.id)}
                                            className="h-4 w-4 rounded border-slate-600 text-brand-accent focus:ring-brand-accent"
                                        />
                                        <label htmlFor={`member-${user.id}`} className="ml-3 text-slate-300">{user.name} <span className="text-xs text-slate-500">({user.role})</span></label>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {error && <p className="text-status-error text-xs mt-1">{error}</p>}
                    </div>

                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">Cancel</button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Create Team</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default CreateTeamModal;
