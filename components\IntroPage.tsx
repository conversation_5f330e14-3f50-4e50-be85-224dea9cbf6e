import React from 'react';
import { useTranslation } from '../i18n/I18nProvider';
import RackxLogo from './RackxLogo';

interface IntroPageProps {
  onComplete: () => void;
}

const IntroPage: React.FC<IntroPageProps> = ({ onComplete }) => {
  const { t } = useTranslation();

  const backgroundStyles: React.CSSProperties = {
    backgroundColor: '#0f172a', // slate-900
    backgroundImage: `
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)
    `,
    backgroundSize: `20px 20px`,
  };

  return (
    <div
      className="flex flex-col items-center justify-center min-h-screen text-white"
      style={backgroundStyles}
    >
      <div className="text-center p-8">
        <div className="animate-pulse">
          <div className="flex flex-col items-center">
            <RackxLogo />
            <p className="text-xl text-slate-300 mt-2">by Sabca Morocco</p>
          </div>
        </div>
        <button
          onClick={onComplete}
          className="mt-12 bg-brand-accent text-brand-text-on-accent font-bold py-3 px-8 rounded-full text-lg shadow-lg hover:bg-brand-accent-hover transition-transform transform hover:scale-105 duration-300 focus:outline-none focus:ring-4 focus:ring-blue-500/50"
        >
          Enter
        </button>
      </div>
    </div>
  );
};

export default IntroPage;