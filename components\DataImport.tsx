import React, { useState, useCallback } from 'react';
import { useTranslation } from '../i18n/I18nProvider';
import { UploadIcon, MapIcon, ValidateIcon, ConfirmIcon, ArrowRightIcon, CheckCircleIcon, XCircleIcon } from './icons';
import { useDropzone } from 'react-dropzone';
import * as XLSX from 'xlsx';
import { StagedImportRow } from '../types';
import { useAppActions } from '../context/AppContext';

type Stage = 'Upload' | 'Map' | 'Validate' | 'Confirm';

const DataImport: React.FC = () => {
  const { t } = useTranslation();
  const { handleStageData } = useAppActions();
  const [currentStage, setCurrentStage] = useState<Stage>('Upload');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [fileHeaders, setFileHeaders] = useState<string[]>([]);
  const [allFileRows, setAllFileRows] = useState<string[][]>([]);
  const [columnMapping, setColumnMapping] = useState<{ [key: string]: string }>({});
  const [validatedRows, setValidatedRows] = useState<StagedImportRow[]>([]);

  const STAGES: { id: Stage; title: string; icon: React.ElementType }[] = [
    { id: 'Upload', title: t('dataImport.stageUpload'), icon: UploadIcon },
    { id: 'Map', title: t('dataImport.stageMap'), icon: MapIcon },
    { id: 'Validate', title: t('dataImport.stageValidate'), icon: ValidateIcon },
    { id: 'Confirm', title: t('dataImport.stageConfirm'), icon: ConfirmIcon },
  ];

  const handleFileAccepted = (file: File) => {
    setUploadedFile(file);

    const reader = new FileReader();
    reader.onload = (e) => {
        const data = e.target?.result;
        if (data) {
            try {
                const workbook = XLSX.read(data, { type: 'binary' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: "" });
                const headers = jsonData[0].map(h => h.toString());
                const rows = jsonData.slice(1).map(row => row.map(cell => cell.toString()));
                setFileHeaders(headers);
                setAllFileRows(rows);
            } catch (error) {
                console.error("Error reading file:", error);
                setUploadedFile(null);
                setFileHeaders([]);
                setAllFileRows([]);
            }
        }
    };
    reader.onerror = () => {
        console.error("Error reading file.");
        setUploadedFile(null);
        setFileHeaders([]);
        setAllFileRows([]);
    };
    reader.readAsBinaryString(file);
  };

  const handleValidation = () => {
    const newValidatedRows = allFileRows.map(row => {
        let isValid = true;
        let message = '';
        // Basic validation: ensure required fields are present
        for(const key in columnMapping) {
            const field = columnMapping[key];
            if(field) {
                const headerIndex = fileHeaders.indexOf(key);
                const value = row[headerIndex];
                if(!value || value.trim() === '') {
                    isValid = false;
                    message = `Required field ${field} is missing`;
                    break;
                }
            }
        }
        return { data: row, status: isValid ? 'ok' : 'error', message } as StagedImportRow;
    });
    setValidatedRows(newValidatedRows);
    setCurrentStage('Validate');
  };

  const handleConfirm = () => {
    if (!uploadedFile) return;
    const valid = validatedRows.filter(r => r.status === 'ok').length;
    const invalid = validatedRows.length - valid;
    const dataToStage = {
        source: 'csv' as const,
        importType: 'Add/Update Inventory' as const,
        fileName: uploadedFile.name,
        status: 'staged' as const,
        rows: validatedRows,
        summary: { total: validatedRows.length, valid, invalid },
        columnMapping: columnMapping,
        fileHeaders: fileHeaders,
    };
    handleStageData(dataToStage);
    setCurrentStage('Confirm');
  };

  const renderContent = () => {
    switch (currentStage) {
      case 'Upload':
        return <UploadStep onFileAccepted={handleFileAccepted} onNext={() => setCurrentStage('Map')} file={uploadedFile} />;
      case 'Map':
        return <MapStep headers={fileHeaders} columnMapping={columnMapping} setColumnMapping={setColumnMapping} onNext={handleValidation} />;
      case 'Validate':
        return <ValidateStep rows={validatedRows} onNext={handleConfirm} />;
      case 'Confirm':
        return <ConfirmStep />;
      default:
        return <p>Stage not implemented yet.</p>;
    }
  };

  return (
    <div className="h-full flex flex-col text-white">
      {/* Stepper */}
      <div className="mb-8">
        <ol className="flex items-center w-full">
          {STAGES.map((stage, index) => (
            <li key={stage.id} className={`flex w-full items-center ${index < STAGES.length - 1 ? 'after:content-[\'\'] after:w-full after:h-1 after:border-b after:border-slate-700 after:border-4 after:inline-block' : ''}`}>
              <span className={`flex items-center justify-center w-10 h-10 rounded-full lg:h-12 lg:w-12 shrink-0 ${currentStage === stage.id ? 'bg-brand-accent' : 'bg-slate-700'}`}>
                <stage.icon className="w-5 h-5 text-white lg:w-6 lg:h-6" />
              </span>
            </li>
          ))}
        </ol>
      </div>

      <div className="bg-slate-900/50 rounded-lg p-6 flex-grow">
        {renderContent()}
      </div>
    </div>
  );
};

interface UploadStepProps {
    onFileAccepted: (file: File) => void;
    onNext: () => void;
    file: File | null;
}

const UploadStep: React.FC<UploadStepProps> = ({ onFileAccepted, onNext, file }) => {
    const { t } = useTranslation();
    const onDrop = useCallback((acceptedFiles: File[], fileRejections: any[]) => {
        if (acceptedFiles.length > 0) {
            onFileAccepted(acceptedFiles[0]);
        }
        if (fileRejections.length > 0) {
            alert("File type not supported. Please upload a CSV or XLSX file.");
        }
    }, [onFileAccepted]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'text/csv': ['.csv'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
        },
        multiple: false,
    });

    return (
        <div>
            <h2 className="text-xl font-semibold mb-4">{t('dataImport.upload.title')}</h2>
            <div {...getRootProps()} className={`border-2 border-dashed border-slate-600 rounded-lg p-12 text-center cursor-pointer transition-colors ${isDragActive ? 'bg-slate-700' : 'hover:bg-slate-800'}`}>
                <input {...getInputProps()} />
                <UploadIcon className="mx-auto h-12 w-12 text-slate-400" />
                {file ? (
                    <p className="mt-4 text-green-400">{t('dataImport.upload.fileAccepted', { fileName: file.name })}</p>
                ) : (
                    <>
                        <p className="mt-4 text-slate-400">{t('dataImport.upload.dragAndDrop')}</p>
                        <p className="text-xs text-slate-500 mt-1">{t('dataImport.upload.supportedFormats')}</p>
                    </>
                )}
                 <button className="mt-6 bg-brand-accent hover:bg-brand-accent-dark text-white font-bold py-2 px-4 rounded">
                    {t('dataImport.upload.selectFile')}
                </button>
            </div>
            {file && (
                <div className="flex justify-end mt-6">
                    <button onClick={onNext} disabled={!file} className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded flex items-center disabled:bg-slate-500">
                        {t('dataImport.next')} <ArrowRightIcon className="ml-2 h-5 w-5" />
                    </button>
                </div>
            )}
        </div>
    )
}

interface MapStepProps {
    headers: string[];
    columnMapping: { [key: string]: string };
    setColumnMapping: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
    onNext: () => void;
}

const MapStep: React.FC<MapStepProps> = ({ headers, columnMapping, setColumnMapping, onNext }) => {
    const { t } = useTranslation();

    const systemFields = ['name', 'sku', 'quantity', 'price'];

    const handleMappingChange = (header: string, field: string) => {
        setColumnMapping(prev => ({...prev, [header]: field}));
    }

    return (
        <div>
            <h2 className="text-xl font-semibold mb-4">{t('dataImport.map.title')}</h2>
            <p className="text-slate-400 mb-6">{t('dataImport.map.description')}</p>
            <div className="space-y-4">
                {headers.map((header, index) => (
                    <div key={index} className="grid grid-cols-2 gap-4 items-center">
                        <div className="font-semibold text-slate-300">{header}</div>
                        <div>
                            <select
                                className="bg-slate-800 border border-slate-700 rounded-md p-2 w-full"
                                value={columnMapping[header] || ''}
                                onChange={(e) => handleMappingChange(header, e.target.value)}
                            >
                                <option value="">{t('dataImport.map.selectField')}</option>
                                {systemFields.map(field => (
                                    <option key={field} value={field}>{field}</option>
                                ))}
                            </select>
                        </div>
                    </div>
                ))}
            </div>
            <div className="flex justify-end mt-6">
                <button onClick={onNext} className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded flex items-center">
                    {t('dataImport.next')} <ArrowRightIcon className="ml-2 h-5 w-5" />
                </button>
            </div>
        </div>
    )
}

interface ValidateStepProps {
    rows: StagedImportRow[];
    onNext: () => void;
}

const ValidateStep: React.FC<ValidateStepProps> = ({ rows, onNext }) => {
    const { t } = useTranslation();
    const validRows = rows.filter(row => row.status === 'ok');
    const invalidRows = rows.filter(row => row.status === 'error');

    return (
        <div>
            <h2 className="text-xl font-semibold mb-4">{t('dataImport.validate.title')}</h2>
            <p className="text-slate-400 mb-6">{t('dataImport.validate.description', { total: rows.length, valid: validRows.length, invalid: invalidRows.length })}</p>

            {invalidRows.length > 0 && (
                <div className="mb-6">
                    <h3 className="font-semibold text-red-400 mb-2">{t('dataImport.validate.invalidRows')}</h3>
                    <div className="max-h-60 overflow-y-auto bg-slate-800/50 p-2 rounded">
                        {invalidRows.map((row, index) => (
                            <div key={index} className="p-2 border-b border-slate-700">
                                <p className="text-sm text-red-400">{row.message}</p>
                                <p className="text-xs text-slate-500 mt-1">{row.data.join(', ')}</p>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            <div className="flex justify-end mt-6">
                <button onClick={onNext} className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded flex items-center">
                    {t('dataImport.next')} <ArrowRightIcon className="ml-2 h-5 w-5" />
                </button>
            </div>
        </div>
    )
}

const ConfirmStep: React.FC = () => {
    const { t } = useTranslation();
    return (
        <div className="text-center">
            <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
            <h2 className="mt-4 text-2xl font-bold">{t('dataImport.confirm.title')}</h2>
            <p className="mt-2 text-slate-400">{t('dataImport.confirm.description')}</p>
        </div>
    )
}

export default DataImport;
