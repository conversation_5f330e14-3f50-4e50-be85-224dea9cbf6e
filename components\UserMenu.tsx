

import React, { useState, useRef, useEffect } from 'react';
import { UserType } from '../types';
import { UserIcon, ChevronDownIcon, LogOutIcon } from './icons';
import { useAppActions } from '../context/AppContext';

interface UserMenuProps {
  currentUser: UserType;
  users: UserType[];
  onSelectUser: (user: UserType) => void;
  onLogout: () => void;
}

const UserMenu: React.FC<UserMenuProps> = ({ currentUser, users, onSelectUser, onLogout }) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSelect = (user: UserType) => {
    onSelectUser(user);
    setIsOpen(false);
  };

  const handleLogout = () => {
    onLogout();
    setIsOpen(false);
  }

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-slate-700/50 transition-colors"
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        <img src={currentUser.avatarUrl} alt={currentUser.name} className="w-8 h-8 rounded-full bg-slate-700" />
        <div className="text-left hidden md:block">
          <p className="font-semibold text-sm text-slate-100">{currentUser.name}</p>
          <p className="text-xs text-slate-400">{currentUser.role}</p>
        </div>
        <ChevronDownIcon className={`w-5 h-5 text-slate-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-slate-800 border border-slate-700 rounded-lg shadow-xl z-20 animate-fade-in" role="menu">
          <div className="p-2 font-bold text-slate-200 border-b border-slate-700">Switch User</div>
          <ul className="py-1 max-h-72 overflow-y-auto">
            {users.map(user => (
              <li key={user.id}>
                <button
                  onClick={() => handleSelect(user)}
                  className="w-full text-left flex items-center px-3 py-2 text-sm text-slate-200 hover:bg-brand-accent hover:text-slate-900 transition-colors"
                  role="menuitem"
                >
                  <div className="flex-shrink-0 mr-3">
                    <img src={user.avatarUrl} alt={user.name} className="w-8 h-8 rounded-full bg-slate-700" />
                  </div>
                  <div>
                    <p className="font-semibold">{user.name}</p>
                    <p className="text-xs text-slate-400">{user.role}</p>
                  </div>
                </button>
              </li>
            ))}
          </ul>
           <div className="border-t border-slate-700 p-2">
            <button
                onClick={handleLogout}
                className="w-full text-left flex items-center px-3 py-2 text-sm text-slate-200 hover:bg-red-600/80 hover:text-white transition-colors rounded-md"
                role="menuitem"
            >
                <LogOutIcon className="w-5 h-5 mr-3"/>
                <p className="font-semibold">Logout</p>
            </button>
        </div>
        </div>
      )}
    </div>
  );
};

export default UserMenu;