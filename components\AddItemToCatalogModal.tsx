import React, { useState, FormEvent, useEffect } from 'react';
import { CatalogItem } from '../types';
import { XIcon } from './icons';

interface AddItemToCatalogModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (itemData: Omit<CatalogItem, 'id' | 'custom_fields'>) => void;
}

const AddItemToCatalogModal: React.FC<AddItemToCatalogModalProps> = ({ isOpen, onClose, onSave }) => {
    const initialState = {
        name: '',
        description: '',
        sku: '',
        image_url: '/img/placeholder.jpg', // Default placeholder
        category: '',
        default_unit_price: 0,
        is_restricted: false,
    };
    const [newItem, setNewItem] = useState(initialState);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    useEffect(() => {
        if (isOpen) {
            setNewItem(initialState);
            setErrors({});
        }
    }, [isOpen]);

    if (!isOpen) return null;

    const validate = () => {
        const tempErrors: { [key: string]: string } = {};
        if (!newItem.name.trim()) tempErrors.name = 'Item name is required.';
        if (!newItem.sku.trim()) tempErrors.sku = 'SKU is required.';
        if (!newItem.category.trim()) tempErrors.category = 'Category is required.';
        if (newItem.default_unit_price <= 0) tempErrors.default_unit_price = 'Price must be greater than 0.';
        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (validate()) {
            onSave(newItem);
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        if (type === 'checkbox') {
            const { checked } = e.target as HTMLInputElement;
            setNewItem(prev => ({ ...prev, [name]: checked }));
        } else {
            const processedValue = name === 'default_unit_price' ? parseFloat(value) : value;
            setNewItem(prev => ({ ...prev, [name]: processedValue }));
        }
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div 
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-lg relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Add New Item to Catalog</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>
                
                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="name" className={labelClasses}>Item Name</label>
                                <input type="text" id="name" name="name" value={newItem.name} onChange={handleChange} className={inputClasses} required />
                                {errors.name && <p className="text-red-400 text-xs mt-1">{errors.name}</p>}
                            </div>
                            <div>
                                <label htmlFor="sku" className={labelClasses}>SKU</label>
                                <input type="text" id="sku" name="sku" value={newItem.sku} onChange={handleChange} className={inputClasses} required />
                                {errors.sku && <p className="text-red-400 text-xs mt-1">{errors.sku}</p>}
                            </div>
                        </div>
                        <div>
                            <label htmlFor="description" className={labelClasses}>Description</label>
                            <textarea id="description" name="description" value={newItem.description} onChange={handleChange} className={`${inputClasses} min-h-[80px]`} />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="category" className={labelClasses}>Category</label>
                                <input type="text" id="category" name="category" value={newItem.category} onChange={handleChange} className={inputClasses} required />
                                {errors.category && <p className="text-red-400 text-xs mt-1">{errors.category}</p>}
                            </div>
                            <div>
                                <label htmlFor="default_unit_price" className={labelClasses}>Unit Price (€)</label>
                                <input type="number" step="0.01" min="0" id="default_unit_price" name="default_unit_price" value={newItem.default_unit_price} onChange={handleChange} className={inputClasses} required />
                                {errors.default_unit_price && <p className="text-red-400 text-xs mt-1">{errors.default_unit_price}</p>}
                            </div>
                        </div>
                        <div className="flex items-center">
                           <input type="checkbox" id="is_restricted" name="is_restricted" checked={newItem.is_restricted} onChange={handleChange} className="h-4 w-4 rounded border-gray-300 text-brand-accent focus:ring-brand-accent" />
                           <label htmlFor="is_restricted" className="ml-2 block text-sm text-slate-300">This is a restricted item</label>
                        </div>
                    </div>
                    
                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-600 text-white font-semibold rounded-md shadow-md hover:bg-slate-500 transition">Cancel</button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Save Item</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddItemToCatalogModal;