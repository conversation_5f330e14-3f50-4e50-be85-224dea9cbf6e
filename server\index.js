import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import sql from 'mssql';
import { createClient } from '@supabase/supabase-js';

const app = express();
app.use(express.json());

// CORS: allow your Vite dev origin
const allowed = (process.env.CORS_ORIGIN || '').split(',').map(s => s.trim()).filter(Boolean);
app.use(cors({
  origin: allowed.length ? allowed : true,
  credentials: true
}));

let pool;
let poolConnect;

if (process.env.DB_HOST) {
    const dbConfig = {
        server: process.env.DB_HOST,
        port: Number(process.env.DB_PORT || 1433),
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        options: {
            encrypt: process.env.DB_ENCRYPT === 'true',
            trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
        },
        pool: { max: 10, min: 0, idleTimeoutMillis: 30000 }
    };
    pool = new sql.ConnectionPool(dbConfig);
    poolConnect = pool.connect();
}

// Supabase client setup
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;
console.log(`Supabase URL: ${supabaseUrl}`);
console.log(`Supabase Key: ${supabaseKey}`);
if (!supabaseUrl || !supabaseKey) {
  console.warn('Supabase environment variables (SUPABASE_URL, SUPABASE_SERVICE_KEY) are not set. Migration endpoint will not work.');
}
const supabase = supabaseUrl && supabaseKey ? createClient(supabaseUrl, supabaseKey) : null;

// health check
app.get('/api/health', async (_req, res) => {
    const health = {
        ok: true,
        checks: {}
    };

    if (poolConnect) {
        try {
            await poolConnect;
            health.checks.sqlServer = { ok: true };
        } catch (e) {
            health.ok = false;
            health.checks.sqlServer = { ok: false, error: e.message };
        }
    } else {
        health.checks.sqlServer = { ok: true, message: 'Not configured' };
    }

    if (supabase) {
        try {
            // A simple query to test the connection and auth
            const { error } = await supabase.from('programs').select('id').limit(1);
            if (error) throw error;
            health.checks.supabase = { ok: true };
        } catch (e) {
            health.ok = false;
            health.checks.supabase = { ok: false, error: e.message };
        }
    } else {
        health.checks.supabase = { ok: true, message: 'Not configured' };
    }

    if (!health.ok) {
        return res.status(500).json(health);
    }

    res.json(health);
});

// sample: read items
app.get('/api/items', async (_req, res) => {
    if (!poolConnect) return res.status(503).json({ error: 'SQL Server not configured' });
  try {
    await poolConnect;
    const result = await pool.request()
      .query('SELECT TOP (50) Id, Name FROM dbo.Items ORDER BY Id DESC');
    res.json(result.recordset);
  } catch (e) {
    console.error(e);
    res.status(500).json({ error: 'DB error' });
  }
});

// sample: create item (uses parameters to avoid SQL injection)
app.post('/api/items', async (req, res) => {
    if (!poolConnect) return res.status(503).json({ error: 'SQL Server not configured' });
  const { name } = req.body;
  if (!name || !name.trim()) return res.status(400).json({ error: 'Name is required' });

  try {
    await poolConnect;
    const result = await pool.request()
      .input('name', sql.NVarChar(100), name.trim())
      .query('INSERT INTO dbo.Items (Name) VALUES (@name); SELECT SCOPE_IDENTITY() AS Id;');
    res.status(201).json({ id: result.recordset[0].Id, name: name.trim() });
  } catch (e) {
    console.error(e);
    res.status(500).json({ error: 'DB error' });
  }
});


// --- Data Sources API ---

// GET all data sources (summary view)
app.get('/api/datasources', async (_req, res) => {
    if (!poolConnect) return res.status(503).json({ error: 'SQL Server not configured' });
    try {
        await poolConnect;
        const result = await pool.request().query('SELECT Id, Name, Type, CreatedAt, UpdatedAt FROM dbo.DataSources ORDER BY Name');
        res.json(result.recordset);
    } catch (e) {
        console.error('Failed to get data sources:', e);
        res.status(500).json({ error: 'Failed to retrieve data sources', details: e.message });
    }
});

// GET a single data source by ID (full details)
app.get('/api/datasources/:id', async (req, res) => {
    if (!poolConnect) return res.status(503).json({ error: 'SQL Server not configured' });
    try {
        await poolConnect;
        const result = await pool.request()
            .input('id', sql.Int, req.params.id)
            .query('SELECT * FROM dbo.DataSources WHERE Id = @id');

        if (result.recordset.length === 0) {
            return res.status(404).json({ error: 'Data source not found' });
        }

        const dataSource = result.recordset[0];
        // Config is stored as a JSON string, parse it for the client
        try {
            dataSource.Config = JSON.parse(dataSource.Config);
        } catch (parseError) {
            console.error(`Failed to parse config for data source ${req.params.id}:`, parseError);
            return res.status(500).json({ error: 'Failed to parse data source configuration.' });
        }

        res.json(dataSource);
    } catch (e) {
        console.error(`Failed to get data source ${req.params.id}:`, e);
        res.status(500).json({ error: 'Failed to retrieve data source', details: e.message });
    }
});

// POST a new data source
app.post('/api/datasources', async (req, res) => {
    if (!poolConnect) return res.status(503).json({ error: 'SQL Server not configured' });
    const { name, type, config } = req.body;
    if (!name || !type || !config) {
        return res.status(400).json({ error: 'Name, type, and config are required fields.' });
    }

    try {
        await poolConnect;
        const result = await pool.request()
            .input('name', sql.NVarChar(100), name)
            .input('type', sql.NVarChar(50), type)
            .input('config', sql.NVarChar(sql.MAX), JSON.stringify(config))
            .query('INSERT INTO dbo.DataSources (Name, Type, Config) OUTPUT INSERTED.Id, INSERTED.CreatedAt, INSERTED.UpdatedAt VALUES (@name, @type, @config)');

        const newRecord = result.recordset[0];
        res.status(201).json({ id: newRecord.Id, name, type, config, createdAt: newRecord.CreatedAt, updatedAt: newRecord.UpdatedAt });
    } catch (e) {
        console.error('Failed to create data source:', e);
        if (e.message && e.message.includes('UNIQUE KEY constraint')) {
            return res.status(409).json({ error: `A data source with the name "${name}" already exists.` });
        }
        res.status(500).json({ error: 'Failed to create data source', details: e.message });
    }
});

// PUT (update) an existing data source
app.put('/api/datasources/:id', async (req, res) => {
    if (!poolConnect) return res.status(503).json({ error: 'SQL Server not configured' });
    const { name, type, config } = req.body;
    if (!name || !type || !config) {
        return res.status(400).json({ error: 'Name, type, and config are required fields.' });
    }

    try {
        await poolConnect;
        // The trigger will handle UpdatedAt, but we can also set it here
        const result = await pool.request()
            .input('id', sql.Int, req.params.id)
            .input('name', sql.NVarChar(100), name)
            .input('type', sql.NVarChar(50), type)
            .input('config', sql.NVarChar(sql.MAX), JSON.stringify(config))
            .query('UPDATE dbo.DataSources SET Name = @name, Type = @type, Config = @config, UpdatedAt = GETUTCDATE() WHERE Id = @id');

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({ error: 'Data source not found or no changes made.' });
        }

        res.status(200).json({ message: 'Data source updated successfully.' });
    } catch (e) {
        console.error(`Failed to update data source ${req.params.id}:`, e);
        if (e.message && e.message.includes('UNIQUE KEY constraint')) {
            return res.status(409).json({ error: `A data source with the name "${name}" already exists.` });
        }
        res.status(500).json({ error: 'Failed to update data source', details: e.message });
    }
});

// DELETE a data source
app.delete('/api/datasources/:id', async (req, res) => {
    if (!poolConnect) return res.status(503).json({ error: 'SQL Server not configured' });
    try {
        await poolConnect;
        const result = await pool.request()
            .input('id', sql.Int, req.params.id)
            .query('DELETE FROM dbo.DataSources WHERE Id = @id');

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({ error: 'Data source not found.' });
        }

        res.status(204).send();
    } catch (e) {
        console.error(`Failed to delete data source ${req.params.id}:`, e);
        res.status(500).json({ error: 'Failed to delete data source', details: e.message });
    }
});

// POST to test a connection configuration
app.post('/api/datasources/test', async (req, res) => {
    const { type, config } = req.body;

    if (type === 'SQL_SERVER') {
        if (!config || !config.host || !config.user || !config.password || !config.database) {
            return res.status(400).json({ success: false, message: 'For SQL Server, config must include host, user, password, and database.' });
        }
        let testPool;
        try {
            const testDbConfig = {
                server: config.host,
                port: Number(config.port || 1433),
                user: config.user,
                password: config.password,
                database: config.database,
                options: {
                    encrypt: config.encrypt === true || String(config.encrypt).toLowerCase() === 'true',
                    trustServerCertificate: config.trustServerCertificate === true || String(config.trustServerCertificate).toLowerCase() === 'true',
                },
                pool: { max: 1, min: 0, idleTimeoutMillis: 5000 },
                connectionTimeout: 15000,
                requestTimeout: 15000
            };

            testPool = new sql.ConnectionPool(testDbConfig);
            await testPool.connect();
            res.json({ success: true, message: 'Connection successful!' });
        } catch (err) {
            console.error('Test connection failed:', err.message);
            res.status(400).json({ success: false, message: `Connection failed: ${err.message}` });
        } finally {
            if (testPool) {
                await testPool.close();
            }
        }
    } else if (type === 'SUPABASE') {
        if (!config || !config.projectUrl || !config.serviceRoleKey) {
            return res.status(400).json({ success: false, message: 'For Supabase, config must include projectUrl and serviceRoleKey.' });
        }

        try {
            const testSupabase = createClient(config.projectUrl, config.serviceRoleKey);
            // A simple query to test the connection and auth.
            // We try to fetch the list of schemas, which is a low-impact way to verify credentials.
            const { error } = await testSupabase.rpc('schema_list');

            if (error && error.message.includes("failed to fetch")) {
                 // This can happen if the URL is wrong or there's a network issue.
                 throw new Error(`Network error or invalid Supabase URL. ${error.message}`);
            }

            // If the service_role key is invalid, Supabase returns a specific error.
            if (error && (error.message.includes('Invalid API key') || error.message.includes('invalid JWT'))) {
                throw new Error(`Authentication failed. Check your Service Role Key. Details: ${error.message}`);
            }

            // Any other error is also a failure.
            if (error) {
                throw error;
            }

            res.json({ success: true, message: 'Supabase connection successful!' });
        } catch (err) {
            console.error('Supabase test connection failed:', err);
            res.status(400).json({ success: false, message: `Supabase connection failed: ${err.message}` });
        }
    } else {
        return res.status(501).json({ success: false, message: `Connection testing for type '${type}' is not implemented yet.` });
    }
});

app.post('/api/migrate-data', async (req, res) => {
    if (!supabase) {
        return res.status(500).json({ error: 'Supabase client is not initialized. Check server environment variables.' });
    }

    const appState = req.body;
    if (!appState) {
        return res.status(400).json({ error: 'Request body with application state is required.' });
    }

    // De-structure the AppState
    const {
        programs,
        subPrograms,
        catalog,
        catalogCustomFields,
        workshops,
        workshopInventories,
        users,
        requests,
        auditLog,
        consumed,
        breakSchedules,
    } = appState;

    try {
        console.log('Starting data migration to Supabase...');

        // The order is important due to foreign key constraints.
        const tablesToDeleteFrom = [
            'audit_log',
            'requests',
            'workshop_inventories',
            'break_schedules',
            'users',
            'workshops',
            'sub_programs',
            'programs',
            'catalog_items',
            'catalog_custom_fields',
            'consumed_items',
        ];
        console.log('Truncating tables...');
        for (const table of tablesToDeleteFrom) {
            const { error } = await supabase.from(table).delete().neq('id', -1); // delete all rows
            if (error) throw new Error(`Failed to truncate ${table}: ${error.message}`);
        }
        console.log('Tables truncated successfully.');


        // Insert data in order of dependencies
        console.log('Inserting data...');

        if (catalogCustomFields && catalogCustomFields.length > 0) {
            const { error } = await supabase.from('catalog_custom_fields').insert(catalogCustomFields);
            if (error) throw new Error(`Failed to insert catalog_custom_fields: ${error.message}`);
        }

        if (consumed && consumed.length > 0) {
            const { error } = await supabase.from('consumed_items').insert(consumed);
            if (error) throw new Error(`Failed to insert consumed_items: ${error.message}`);
        }

        if (programs && programs.length > 0) {
            const { error } = await supabase.from('programs').insert(programs);
            if (error) throw new Error(`Failed to insert programs: ${error.message}`);
        }

        if (subPrograms && subPrograms.length > 0) {
            const { error } = await supabase.from('sub_programs').insert(subPrograms);
            if (error) throw new Error(`Failed to insert sub_programs: ${error.message}`);
        }

        if (catalog && catalog.length > 0) {
            const catalogToInsert = catalog.map(({ id, name, description, sku, image_url, category, default_unit_price, is_restricted, custom_fields }) => ({
                id, name, description, sku, image_url, category, default_unit_price, is_restricted, custom_fields
            }));
            const { error } = await supabase.from('catalog_items').insert(catalogToInsert);
            if (error) throw new Error(`Failed to insert catalog_items: ${error.message}`);
        }

        if (workshops && workshops.length > 0) {
            const { error } = await supabase.from('workshops').insert(workshops);
            if (error) throw new Error(`Failed to insert workshops: ${error.message}`);
        }

        if (users && users.length > 0) {
            const usersToInsert = users.map(({ id, name, employeeId, role, password, primary_workshop_id, avatarUrl, reelImageUrl }) => ({
                id, name, employee_id: employeeId, role, password, primary_workshop_id, avatar_url: avatarUrl, reel_image_url: reelImageUrl
            }));
            const { error } = await supabase.from('users').insert(usersToInsert);
            if (error) throw new Error(`Failed to insert users: ${error.message}`);
        }

        if (breakSchedules) {
            const schedulesToInsert = Object.entries(breakSchedules).map(([workshop_id, schedule]) => ({
                workshop_id: parseInt(workshop_id),
                schedule,
            }));
            if (schedulesToInsert.length > 0) {
                const { error } = await supabase.from('break_schedules').insert(schedulesToInsert);
                if (error) throw new Error(`Failed to insert break_schedules: ${error.message}`);
            }
        }

        if (workshopInventories && workshopInventories.length > 0) {
            const { error } = await supabase.from('workshop_inventories').insert(workshopInventories);
            if (error) throw new Error(`Failed to insert workshop_inventories: ${error.message}`);
        }

        if (requests && requests.length > 0) {
            const { error } = await supabase.from('requests').insert(requests);
            if (error) throw new Error(`Failed to insert requests: ${error.message}`);
        }

        if (auditLog && auditLog.length > 0) {
            const { error } = await supabase.from('audit_log').insert(auditLog);
            if (error) throw new Error(`Failed to insert audit_log: ${error.message}`);
        }

        console.log('Data migration to Supabase completed successfully.');
        res.status(200).json({ message: 'Data migration successful!' });

    } catch (error) {
        console.error('Data migration failed:', error);
        res.status(500).json({ error: 'Data migration failed', details: error.message });
    }
});


const createGetEndpoint = (tableName, dataTransformer = (data) => data) => {
    return async (req, res) => {
        if (!supabase) {
            return res.status(500).json({ error: 'Supabase client is not initialized.' });
        }

        const { sortKey, sortDirection = 'asc', filters = {} } = req.query;

        try {
            let query = supabase.from(tableName).select('*');

            // A more robust solution would be to specify the search columns per table
            if (filters.searchTerm) {
                const term = `%${filters.searchTerm}%`;
                if (tableName === 'users') {
                    query = query.or(`name.ilike.${term},employee_id.ilike.${term}`);
                } else if (tableName === 'catalog_items') {
                     query = query.or(`name.ilike.${term},sku.ilike.${term}`);
                } else if ('name' in (data[0] || {})) {
                   query = query.ilike('name', term);
                }
            }

            if (sortKey) {
                query = query.order(sortKey, { ascending: sortDirection === 'asc' });
            }

            const { data, error } = await query;

            if (error) throw error;

            res.json(dataTransformer(data));
        } catch (e) {
            console.error(`Failed to get ${tableName}:`, e);
            res.status(500).json({ error: `Failed to retrieve ${tableName}`, details: e.message });
        }
    };
}

// --- Application API Endpoints ---

const userTransformer = (data) => data.map(user => ({
    id: user.id,
    name: user.name,
    employeeId: user.employee_id,
    role: user.role,
    password: user.password,
    primary_workshop_id: user.primary_workshop_id,
    avatarUrl: user.avatar_url,
    reelImageUrl: user.reel_image_url
}));

app.get('/api/users', createGetEndpoint('users', userTransformer));
app.get('/api/workshops', createGetEndpoint('workshops'));
app.get('/api/catalog', createGetEndpoint('catalog_items'));
app.get('/api/programs', createGetEndpoint('programs'));

app.post('/api/programs', async (req, res) => {
    if (!supabase) {
        return res.status(500).json({ error: 'Supabase client is not initialized.' });
    }

    const { id, name } = req.body;
    if (id === undefined || !name) {
        return res.status(400).json({ error: 'Program must have an id and a name.' });
    }

    try {
        const { data, error } = await supabase
            .from('programs')
            .insert([{ id, name }])
            .select();

        if (error) {
            // Check for specific errors, like unique constraint violation
            if (error.code === '23505') { // unique_violation
                return res.status(409).json({ error: `A program with id ${id} already exists.` });
            }
            throw error;
        }

        if (!data || data.length === 0) {
            // This case should ideally not happen if insert is successful without error
            return res.status(500).json({ error: 'Failed to create program, no data returned.' });
        }

        res.status(201).json(data[0]);
    } catch (e) {
        console.error(`Failed to create program:`, e);
        res.status(500).json({ error: 'Failed to create program', details: e.message });
    }
});
app.get('/api/sub-programs', createGetEndpoint('sub_programs'));
app.get('/api/requests', createGetEndpoint('requests'));
app.get('/api/consumed', createGetEndpoint('consumed_items'));
app.get('/api/break-schedules', createGetEndpoint('break_schedules'));
app.get('/api/workshop-inventories', createGetEndpoint('workshop_inventories'));

// A bit more complex, so we'll keep it separate for now
app.get('/api/audit-log', async (req, res) => {
     if (!supabase) {
        return res.status(500).json({ error: 'Supabase client is not initialized.' });
    }
    // ... more complex logic for audit log ...
    res.json([]);
});


// graceful shutdown
process.on('SIGINT', async () => {
  try { await pool.close(); } catch {}
  process.exit(0);
});

const port = Number(process.env.PORT || 3001);
app.listen(port, () => {
  console.log(`API running on http://localhost:${port}`);
});
