<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RackX</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&family=Inter:wght@400;500;600;700;800;900&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      :root {
        --color-brand-accent: #17648B;
        --color-brand-accent-stop: #06b6d4;
        --color-slate-gradient-start: #475569;
        --color-slate-gradient-end: #94a3b8;

        /* Tinted Glass Backgrounds */
        --card-bg-dashboard: linear-gradient(145deg, rgba(34, 211, 238, 0.1), rgba(15, 23, 42, 0.1));
        --card-bg-panel: linear-gradient(145deg, rgba(51, 65, 85, 0.25), rgba(30, 41, 59, 0.35));
        --card-bg-data: linear-gradient(145deg, rgba(30, 41, 59, 0.3), rgba(15, 23, 42, 0.2));
        --card-bg-reports: linear-gradient(145deg, rgba(30, 41, 59, 0.3), rgba(15, 23, 42, 0.2));
        --card-bg-modal: linear-gradient(145deg, rgba(51, 65, 85, 0.5), rgba(30, 41, 59, 0.6));
      }
      body {
        background-image: 
          linear-gradient(rgba(15, 23, 42, 0.97), rgba(15, 23, 42, 0.97)),
          radial-gradient(circle at top left, rgba(34, 211, 238, 0.15), transparent 30%),
          radial-gradient(circle at bottom right, rgba(139, 92, 246, 0.1), transparent 40%),
          linear-gradient(to right, #0f172a 1px, transparent 1px),
          linear-gradient(to bottom, #0f172a 1px, transparent 1px);
        background-size: 100%, 100%, 100%, 40px 40px, 40px 40px;
        background-attachment: fixed;
      }
    </style>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'Cairo', 'sans-serif'],
              logo: ['Space Grotesk', 'sans-serif'],
            },
            colors: {
              'brand-primary': '#0ea5e9', // sky-500
              'brand-secondary': '#38bdf8', // sky-400
              'brand-accent': '#17648B', // sabca blue
              'brand-accent-hover': '#115070', // darker sabca blue
              'brand-text-on-accent': '#ffffff', // white
              'sidebar-gradient-start': '#115070',
              'sidebar-gradient-middle': '#0d3d56',
              'sidebar-gradient-end': '#0f172a',
              'status-success': '#22c55e', // green-500
              'status-success-bg': 'rgba(34, 197, 94, 0.1)',
              'status-success-border': 'rgba(34, 197, 94, 0.2)',
              'status-warning': '#f59e0b', // amber-500
              'status-warning-bg': 'rgba(245, 158, 11, 0.1)',
              'status-error': '#ef4444', // red-500
              'status-error-bg': 'rgba(239, 68, 68, 0.1)',
              'status-error-border': 'rgba(239, 68, 68, 0.2)',
              'status-info': '#0ea5e9', // sky-500
            },
            boxShadow: {
                'card': '0 4px 6px -1px rgb(0 0 0 / 0.07), 0 2px 4px -2px rgb(0 0 0 / 0.07)',
                'accent': '0 4px 14px 0 rgba(34, 211, 238, 0.2)',
            },
            animation: {
              'fade-in': 'fadeIn 0.5s ease-in-out',
              'pulse-subtle': 'pulseSubtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0', transform: 'translateY(10px)' },
                '100%': { opacity: '1', transform: 'translateY(0)' },
              },
              pulseSubtle: {
                '0%, 100%': { opacity: '1' },
                '50%': { opacity: '.75' },
              }
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^18.2.0/",
    "react/": "https://esm.sh/react@^18.2.0/",
    "react": "https://esm.sh/react@^18.2.0",
    "date-fns": "https://esm.sh/date-fns@^3.6.0",
    "@google/genai": "https://esm.sh/@google/genai",
    "xlsx": "https://esm.sh/xlsx",
    "react-router-dom": "https://esm.sh/react-router-dom@^6.22.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-slate-950 text-slate-200 font-sans">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>