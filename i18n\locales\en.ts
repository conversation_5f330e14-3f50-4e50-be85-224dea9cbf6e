export default {
    "app.loading": "Loading RackX...",
    "intro.welcome": "Welcome to RackX",
    "intro.description": "The intelligent logistics platform for aerospace manufacturing. Streamline your inventory, manage workshops, and accelerate production.",
    "intro.getStarted": "Get Started",
    "login.title": "RackX",
    "login.adminSection": "System Administration",
    "login.programSection": "Select Program Access",
    "login.subPrograms": "{{count}} Sub-Programs",
    "login.rackx": "{{count}} RackX",
    "login.userLead": "Lead: {{name}}",
    "login.userCount": "{{count}} Users",
    "login.noUsers": "No users assigned to this RackX.",
    "login.passwordPrompt": "Password",
    "login.passwordError": "Incorrect password. Please try again.",
    "login.login": "Login",
    "login.defaultPasswordNotice": "Default password for all users is: 1111",
    "header.print": "Print Page",
    "header.breakScheduler": "Team Break Scheduler",
    "header.logout": "Logout",
    "header.collapseSidebar": "Collapse sidebar",
    "header.expandSidebar": "Expand sidebar",
    "header.currentWeek": "Current Week Number",
    "header.notifications": "View notifications",
    "header.language": "Language",
    "notifications.title": "Notifications",
    "notifications.markAllRead": "Mark all as read",
    "notifications.noNotifications": "You have no new notifications.",
    "notifications.requestStatusChanged": "{{actorName}} {{action}} {{requestSummary}}.",
    "notifications.requestStatusApproved": "approved",
    "notifications.requestStatusRejected": "rejected",
    "notifications.requestSummaryDefault": "your request",
    "notifications.requestSummaryTransfer": "your request for {{quantity}}x {{itemName}}",
    "notifications.requestSummaryConsumption": "your consumption log for {{count}} item(s)",
    "notifications.profileUpdated": "{{actorName}} updated your profile.",
    "notifications.userAssigned": "{{actorName}} assigned {{assignedUserName}} to your RackX.",
    "sidebar.dashboard": "Dashboard",
    "sidebar.myRackX": "My RackX",
    "sidebar.inventoryCatalog": "Inventory Catalog",
    "sidebar.browseRackX": "Browse RackX",
    "sidebar.reports": "Reports",
    "sidebar.documentation": "Documentation",
    "sidebar.settings": "Settings",
    "sidebar.adminPanel": "Admin Panel",
    "costTracker.title": "Session Cost",
    "costTracker.consumedItems": "Consumed Items",
    "costTracker.noItems": "Use an item from the inventory to see it here.",
    "dashboard.title": "Dashboard",
    "dashboard.welcome": "Welcome, {{name}}. Here's the pulse of your operations.",
    "dashboard.totalInventoryValue": "Total Inventory Value",
    "dashboard.lowStockItems": "Low Stock Items",
    "dashboard.pendingRequests": "Pending Requests",
    "dashboard.localWorkshops": "Local Workshops",
    "dashboard.itemsUnit": "Items",
    "dashboard.requestsUnit": "Requests",
    "dashboard.rackxUnit": "RackX",
    "dashboard.spendTrendTitle": "{{programName}} Spend Trend (30d)",
    "dashboard.spendTrendNoData": "No spending data for the last 30 days.",
    "dashboard.rackxSpendTitle": "RackX Spend Breakdown (30d)",
    "dashboard.topConsumedTitle": "Top 5 Consumed Items",
    "dashboard.workshopHealthTitle": "Workshop Health",
    "dashboard.workshopHealthLead": "Lead: {{name}}",
    "dashboard.workshopHealthLowStock": "{{count}} low",
    "dashboard.workshopHealthHealthy": "Healthy",
    "dashboard.workshopHealthCritical": "{{count}} critical",
    "dashboard.requestStatusTitle": "Request Status",
    "dashboard.donutTotal": "Total",
    "dashboard.donutPending": "Pending",
    "dashboard.donutApproved": "Approved",
    "dashboard.donutRejected": "Rejected",
    "dashboard.noData": "No data",
    "activity.title": "Activity Feed",
    "activity.noActivity": "No system activity yet.",
    "time.years": "years",
    "time.months": "months",
    "time.days": "days",
    "time.hours": "hours",
    "time.minutes": "minutes",
    "time.justNow": "just now",
    "time.ago": "ago",
    "approvals.title": "Pending Approvals",
    "approvals.noApprovals": "No pending items need your approval.",
    "approvals.reject": "Reject request {{id}}",
    "approvals.approve": "Approve request {{id}}",
    "approvals.requestTypeConsumption": "Consumption Log ({{count}} items)",
    "approvals.requestTypeUnknown": "Unknown Request",
    "approvals.requestBy": "By {{name}}",
    "reports.title": "Reports & Statistics",
    "reports.description": "Key performance indicators and system analytics.",
    "reports.topConsumedTitle": "Top Consumed Items",
    "reports.noConsumptionData": "No consumption data available yet.",
    "reports.mostActiveTitle": "Most Active Users",
    "reports.noUserData": "No user activity recorded yet.",
    "reports.actionsUnit": "actions",
    "reports.stockTrendsTitle": "Stock Trends",
    "reports.stockTrendsDesc": "Historical stock trend analysis is under development and will be available in a future update.",
    "myRackx.noWorkshop": "No Workshop Assigned",
    "myRackx.noWorkshopDesc": "You are not currently assigned to a Workshop. Please contact an administrator.",
    "myRackx.pageTitle": "{{name}}",
    "myRackx.managedBy": "Managed by {{name}}.",
    "myRackx.noLead": "No assigned Team Leader.",
    "myRackx.yourWorkspace": "Your primary operational workspace.",
    "myRackx.viewingWorkspace": "Viewing another team's workspace.",
    "myRackx.smartReplenish": "Smart Replenishment",
    "myRackx.requestTransfer": "Request New Item Transfer",
    "myRackx.inventoryTitle": "Inventory",
    "myRackx.searchPlaceholder": "Search by ID, name, or SKU...",
    "myRackx.tableHeaderItem": "Item",
    "myRackx.tableHeaderStock": "Stock",
    "myRackx.tableHeaderConsume": "Consume Qty",
    "myRackx.noInventory": "This workshop has no inventory.",
    "myRackx.noSearchResults": "No items match \"{{term}}\".",
    "myRackx.logConsumptionTitle": "Log Consumption",
    "myRackx.logConsumptionPlaceholder": "Enter a quantity in the inventory list to log item consumption.",
    "myRackx.logConsumptionDisabled": "Consumption can only be logged from your own RackX.",
    "myRackx.logConsumptionTotal": "Total:",
    "myRackx.logConsumptionSubmit": "Submit for Approval",
    "myRackx.requestsTitle": "Workshop Requests",
    "myRackx.pendingTeamRequests": "Pending Team Requests",
    "myRackx.myPendingRequests": "My Pending Requests",
    "smartReplenish.modalTitle": "Smart Replenishment for {{name}}",
    "smartReplenish.title": "Ready for Smart Analysis",
    "smartReplenish.description": "Use AI to analyze this Workshop's inventory and automatically suggest items that need replenishment from the central warehouse.",
    "smartReplenish.button": "Analyze & Suggest Items",
    "smartReplenish.loadingTitle": "Analyzing Inventory...",
    "smartReplenish.loadingDesc": "The AI is analyzing stock levels and recent usage to generate smart suggestions.",
    "smartReplenish.errorTitle": "Analysis Failed",
    "smartReplenish.tryAgain": "Try Again",
    "smartReplenish.noReplenishTitle": "No Replenishment Needed",
    "smartReplenish.noReplenishDesc": "The AI has determined that the inventory for {{name}} is well-stocked for now.",
    "smartReplenish.tableHeaderItem": "Item",
    "smartReplenish.tableHeaderReason": "Reason",
    "smartReplenish.tableHeaderQty": "Suggested Qty",
    "smartReplenish.submitButton": "Create Transfer Requests",
    "logConsumption.modalTitle": "Justify Consumption",
    "logConsumption.description": "Please provide the reason for consuming these items.",
    "logConsumption.purpose": "Purpose",
    "logConsumption.orderId": "Work Order #",
    "logConsumption.fabOrderId": "Fabrication Order #",
    "logConsumption.duration": "Time for Fabrication (hours)",
    "logConsumption.notes": "Additional Notes (Optional)",
    "logConsumption.notesPlaceholder": "Add any other relevant details...",
    "logConsumption.submit": "Submit Justification",
    "logConsumption.purpose_Fabrication": "Fabrication",
    "logConsumption.purpose_Assembly": "Assembly",
    "logConsumption.purpose_Repair": "Repair",
    "logConsumption.purpose_Maintenance": "Maintenance",
    "logConsumption.purpose_General_Use": "General Use",
    "cancel": "Cancel",
    "close": "Close"
}