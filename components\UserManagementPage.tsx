

import React, { useState, useMemo } from 'react';
import { UserType, Workshop } from '../types';
import { UsersIcon, PlusIcon, EditIcon, Trash2Icon, ChevronsUpDownIcon } from './icons';
import { EditUserModal } from './EditUserModal';
import { useAppState, useAppActions } from '../context/AppContext';

interface UserManagementPageProps {
    onOpenAddUserModal: () => void;
    onDeleteUser: (userId: number) => void;
}

const UserManagementPage: React.FC<UserManagementPageProps> = ({ onOpenAddUserModal, onDeleteUser }) => {
    const { appState } = useAppState();
    const { handleUpdateUser } = useAppActions();
    const { users, workshops, currentUser } = appState!;
    
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortConfig, setSortConfig] = useState<{ key: keyof UserType | 'workshop', direction: 'asc' | 'desc' } | null>({ key: 'name', direction: 'asc' });

    const getRackXName = (id: number) => workshops.find(w => w.id === id)?.name || 'N/A';

    const visibleUsers = useMemo(() => {
        let filteredUsers: UserType[];
        if (currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') {
            filteredUsers = users;
        } else if (currentUser.role === 'TeamLeader') {
            // TeamLeaders can see their own team members plus any unassigned users (in central warehouse)
            filteredUsers = users.filter(user => 
                user.primary_workshop_id === currentUser.primary_workshop_id || 
                user.primary_workshop_id === 1 // RackX Central ID
            );
        } else {
            filteredUsers = [];
        }

        if (searchTerm) {
            return filteredUsers.filter(u => u.name.toLowerCase().includes(searchTerm.toLowerCase()));
        }
        return filteredUsers;

    }, [users, currentUser, searchTerm]);
    
    const sortedUsers = useMemo(() => {
        let sortableUsers = [...visibleUsers];
        if (sortConfig !== null) {
            sortableUsers.sort((a, b) => {
                let aValue: any, bValue: any;
                if (sortConfig.key === 'workshop') {
                    aValue = getRackXName(a.primary_workshop_id);
                    bValue = getRackXName(b.primary_workshop_id);
                } else {
                    aValue = a[sortConfig.key as keyof UserType];
                    bValue = b[sortConfig.key as keyof UserType];
                }
                
                if (aValue < bValue) {
                    return sortConfig.direction === 'asc' ? -1 : 1;
                }
                if (aValue > bValue) {
                    return sortConfig.direction === 'asc' ? 1 : -1;
                }
                return 0;
            });
        }
        return sortableUsers;
    }, [visibleUsers, sortConfig, workshops]);

    const requestSort = (key: keyof UserType | 'workshop') => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
    };

    const handleOpenEditModal = (user: UserType) => {
        setSelectedUser(user);
        setIsEditModalOpen(true);
    };

    const handleCloseEditModal = () => {
        setSelectedUser(null);
        setIsEditModalOpen(false);
    };

    const handleSaveEditedUser = (user: UserType) => {
        handleUpdateUser(user);
        handleCloseEditModal();
    };

    const canEditUser = (userToEdit: UserType): boolean => {
        if (currentUser.role === 'Admin' || currentUser.role === 'Manager') {
            return true; // Admins and Managers can edit anyone.
        }
        if (currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') {
            // Supervisors and Line Leaders can edit anyone except Admins and Managers.
            return userToEdit.role !== 'Admin' && userToEdit.role !== 'Manager';
        }
        if (currentUser.role === 'TeamLeader') {
            // TeamLeaders can edit users in their scope, but not Admins, Managers, or themselves.
            return userToEdit.id !== currentUser.id && userToEdit.role !== 'Admin' && userToEdit.role !== 'Manager';
        }
        return false;
    }

    const canDeleteUser = (userToDelete: UserType): boolean => {
        if (currentUser.role === 'Admin') {
            // Admins can delete anyone except themselves.
            return userToDelete.id !== currentUser.id;
        }
        return false;
    }

    const handleDeleteClick = (user: UserType) => {
        if (canDeleteUser(user) && window.confirm(`Are you sure you want to delete ${user.name}? This action cannot be undone.`)) {
            onDeleteUser(user.id);
        }
    }

    return (
        <>
            <div role="tabpanel" className="animate-fade-in">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
                    <div className="flex items-center">
                        <UsersIcon className="w-6 h-6 mr-3 text-brand-accent" />
                        <h3 className="text-xl font-bold text-slate-200">
                            User Management
                        </h3>
                    </div>
                    <div className="flex items-center space-x-3 w-full md:w-auto">
                        <input
                            type="search"
                            placeholder="Search by name..."
                            value={searchTerm}
                            onChange={e => setSearchTerm(e.target.value)}
                            className="w-full md:w-48 bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition"
                        />
                        {(currentUser.role === 'Admin' || currentUser.role === 'Manager') && (
                            <button
                                onClick={onOpenAddUserModal}
                                className="flex-shrink-0 flex items-center px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition"
                            >
                                <PlusIcon className="w-5 h-5 mr-2" />
                                Add User
                            </button>
                        )}
                    </div>
                </div>

                <p className="text-slate-400 mt-2 mb-6">
                    {(currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader')
                        ? "View, create, and manage all registered users and their assignments."
                        : "Manage your team by assigning available users from RackX Central to your RackX, or unassigning current members."
                    }
                </p>
                <div className="overflow-x-auto bg-slate-900/50 rounded-lg border border-slate-700">
                    <table className="w-full text-sm text-left text-slate-300">
                        <thead className="text-xs text-slate-400 uppercase bg-slate-800">
                            <tr>
                                <th scope="col" className="px-6 py-3 font-semibold tracking-wider">
                                    <button onClick={() => requestSort('id')} className="flex items-center gap-1.5 w-full text-left">
                                        ID <ChevronsUpDownIcon className="w-4 h-4" />
                                    </button>
                                </th>
                                <th scope="col" className="px-6 py-3 font-semibold tracking-wider">
                                    <button onClick={() => requestSort('name')} className="flex items-center gap-1.5 w-full text-left">
                                        Name <ChevronsUpDownIcon className="w-4 h-4" />
                                    </button>
                                </th>
                                <th scope="col" className="px-6 py-3 font-semibold tracking-wider">
                                    <button onClick={() => requestSort('role')} className="flex items-center gap-1.5 w-full text-left">
                                        Role <ChevronsUpDownIcon className="w-4 h-4" />
                                    </button>
                                </th>
                                <th scope="col" className="px-6 py-3 font-semibold tracking-wider">
                                    <button onClick={() => requestSort('workshop')} className="flex items-center gap-1.5 w-full text-left">
                                        Assigned RackX <ChevronsUpDownIcon className="w-4 h-4" />
                                    </button>
                                </th>
                                <th scope="col" className="px-6 py-3 font-semibold tracking-wider"><span className="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            {sortedUsers.map((user, index) => {
                                const canEdit = canEditUser(user);
                                const canDelete = canDeleteUser(user);
                                return (
                                <tr key={user.id} className={`border-b border-slate-700 last:border-b-0 hover:bg-slate-700/50 transition-colors duration-200 ${index % 2 === 0 ? 'bg-slate-800/30' : 'bg-slate-900/20'}`}>
                                    <td className="px-6 py-4 font-mono text-slate-500">{user.id}</td>
                                    <td className="px-6 py-4">
                                        <div className="flex items-center gap-3">
                                            <img src={user.avatarUrl} alt={user.name} className="flex-shrink-0 h-10 w-10 rounded-full bg-slate-700" />
                                            <div>
                                                <div className="font-medium text-slate-100 whitespace-nowrap">{user.name}</div>
                                                <div className="text-xs text-slate-400 font-mono">{user.employeeId}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4">{user.role}</td>
                                    <td className="px-6 py-4">{getRackXName(user.primary_workshop_id)}</td>
                                    <td className="px-6 py-4 text-right">
                                        <button 
                                            onClick={() => handleOpenEditModal(user)} 
                                            className="p-2 text-brand-accent hover:text-amber-300 disabled:text-slate-500 disabled:cursor-not-allowed transition-colors"
                                            disabled={!canEdit}
                                            title={canEdit ? `Edit user ${user.name}`: "You cannot edit this user"}
                                        >
                                            <EditIcon className="w-4 h-4" />
                                            <span className="sr-only">Edit User {user.name}</span>
                                        </button>
                                        <button
                                            onClick={() => handleDeleteClick(user)}
                                            className="p-2 text-status-error hover:text-red-400 disabled:text-slate-500 disabled:cursor-not-allowed transition-colors"
                                            disabled={!canDelete}
                                            title={canDelete ? `Delete user ${user.name}` : "You cannot delete this user"}
                                        >
                                            <Trash2Icon className="w-4 h-4" />
                                            <span className="sr-only">Delete User {user.name}</span>
                                        </button>
                                    </td>
                                </tr>
                            )})}
                        </tbody>
                    </table>
                    {sortedUsers.length === 0 && (
                        <p className="text-center py-8 text-slate-400">
                            {searchTerm ? `No users found for "${searchTerm}".` : 'No users to display.'}
                        </p>
                    )}
                </div>
            </div>
            <EditUserModal 
                isOpen={isEditModalOpen}
                onClose={handleCloseEditModal}
                onSave={handleSaveEditedUser}
                user={selectedUser}
                workshops={workshops}
                currentUser={currentUser}
            />
        </>
    );
};

export default UserManagementPage;