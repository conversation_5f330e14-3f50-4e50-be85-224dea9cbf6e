import React from 'react';
import DataImport from './DataImport';
import { XIcon } from './icons';
import { useTranslation } from '../i18n/I18nProvider';

interface DataImportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DataImportModal: React.FC<DataImportModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-slate-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <header className="p-4 border-b border-slate-700 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">{t('dataImport.title')}</h2>
          <button onClick={onClose} className="p-2 rounded-full hover:bg-slate-700">
            <XIcon className="w-6 h-6 text-slate-400" />
          </button>
        </header>
        <main className="p-6 overflow-y-auto">
          <DataImport />
        </main>
      </div>
    </div>
  );
};

export default DataImportModal;
