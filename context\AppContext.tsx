
import React, { createContext, useState, useCallback, useMemo, useEffect, useContext } from 'react';
import { AppState, UserType, Request, AuditLog, WorkshopInventoryItem, CatalogItem, Workshop, RequestItemDetail, StagedImport, UserRole, StagedImportRow, CatalogCustomField, BreakSchedule, ConsumedItem, SubProgram, Program } from '../types';
import { api } from '../services/api';

interface IAppContext {
    appState: AppState | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    isRequestModalOpen: boolean;
    isSidebarOpen: boolean;
    isBreakSchedulerModalOpen: boolean;
    isIdle: boolean;
    totalCost: number;
    selectedWorkshopId: number | null;
}

interface IAppActionsContext {
    setIsIdle: (isIdle: boolean) => void;
    handleSetCurrentUser: (user: UserType) => Promise<void>;
    handleLogin: (user: UserType) => Promise<void>;
    handleLogout: () => void;
    handleReset: () => Promise<void>;
    handleRestore: (state: AppState) => Promise<void>;
    handleSelectWorkshop: (workshopId: number | null) => void;
    handleCreateRequest: (requestData: Omit<Request, 'id' | 'status' | 'created_at' | 'created_by_user_id'>) => Promise<void>;
    handleCreateRequestBatch: (requestsData: Omit<Request, 'id' | 'status' | 'created_at' | 'created_by_user_id'>[]) => Promise<void>;
    handleLogConsumptionBatch: (workshopId: number, items: RequestItemDetail[], totalCost: number, reason?: string) => Promise<void>;
    handleRejectRequest: (requestId: number) => Promise<void>;
    handleApproveRequest: (requestId: number) => Promise<void>;
    handleStageData: (stagedImportData: Omit<StagedImport, 'id' | 'user_id' | 'created_at'>) => Promise<void>;
    handleUpdateStagedImport: (importId: number, updatedRows: StagedImportRow[]) => Promise<void>;
    handleProcessStagedImport: (importId: number) => Promise<void>;
    handleDiscardStagedImport: (importId: number) => Promise<void>;
    handleUpdateUser: (user: UserType) => Promise<void>;
    handleUpdateCatalog: (updatedItems: CatalogItem[]) => Promise<void>;
    handleUpdateCustomFields: (newFields: CatalogCustomField[]) => Promise<void>;
    handleCreateUser: (userData: Omit<UserType, 'id' | 'avatarUrl' | 'reelImageUrl'>) => Promise<void>;
    handleCreateWorkshop: (workshopData: Omit<Workshop, 'id'>) => Promise<void>;
    handleAddItemToCatalog: (itemData: Omit<CatalogItem, 'id' | 'custom_fields'>) => Promise<void>;
    handleCreateSubProgram: (subProgramData: Omit<SubProgram, 'id'>) => Promise<void>;
    handleCreateProgram: (programData: Omit<Program, 'id'>) => Promise<void>;
    handleCreateTeam: (teamData: { workshopId: number; teamLeaderId: number; memberIds: number[] }) => Promise<void>;
    handleDeleteUser: (userId: number) => Promise<void>;
    handleUpdateWorkshop: (workshop: Workshop) => Promise<void>;
    handleDeleteWorkshop: (workshopId: number) => Promise<void>;
    handleUpdateProgram: (program: Program) => Promise<void>;
    handleDeleteProgram: (programId: number) => Promise<void>;
    handleUpdateSubProgram: (subProgram: SubProgram) => Promise<void>;
    handleDeleteSubProgram: (subProgramId: number) => Promise<void>;
    handleUpdateBreakSchedule: (workshopId: number, schedule: BreakSchedule) => Promise<void>;
    openRequestModal: () => void;
    closeRequestModal: () => void;
    toggleSidebar: () => void;
    openBreakSchedulerModal: () => void;
    closeBreakSchedulerModal: () => void;
}

const AppContext = createContext<IAppContext | undefined>(undefined);
const AppActionsContext = createContext<IAppActionsContext | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [appState, setAppState] = useState<AppState | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [isBreakSchedulerModalOpen, setIsBreakSchedulerModalOpen] = useState(false);
    const [selectedWorkshopId, setSelectedWorkshopId] = useState<number | null>(null);
    const [isIdle, setIsIdle] = useState(false);

    useEffect(() => {
        const loadData = async () => {
            setIsLoading(true);
            const data = await api.getAppData();
            setAppState(data);
            setIsLoading(false);
        };
        loadData();
    }, []);

    const toggleSidebar = () => setIsSidebarOpen(prev => !prev);
    const openRequestModal = () => setIsRequestModalOpen(true);
    const closeRequestModal = () => setIsRequestModalOpen(false);
    const openBreakSchedulerModal = () => setIsBreakSchedulerModalOpen(true);
    const closeBreakSchedulerModal = () => setIsBreakSchedulerModalOpen(false);
    const handleSelectWorkshop = (workshopId: number | null) => setSelectedWorkshopId(workshopId);

    const updateState = useCallback(async (newState: AppState) => {
        setAppState(newState);
        await api.saveAppData(newState);
    }, []);
    
    const logAction = useCallback((action: string, details: object, state: AppState): AuditLog[] => {
        const newLogEntry: AuditLog = {
            id: Date.now(),
            action,
            user_id: state.currentUser.id,
            details,
            created_at: new Date().toISOString(),
        };
        return [newLogEntry, ...state.auditLog].slice(0, 100);
    }, []);

    const handleSetCurrentUser = useCallback(async (user: UserType) => {
        if (!appState) return;
        await updateState({ ...appState, currentUser: user });
    }, [appState, updateState]);
    
    const totalCost = useMemo(() => {
        if (!appState) return 0;
        return appState.consumed.reduce((total: number, item: ConsumedItem) => total + item.quantity * item.cost, 0);
    }, [appState?.consumed]);
    
    const handleLogin = useCallback(async (user: UserType) => {
        await handleSetCurrentUser(user);
        setIsAuthenticated(true);
    }, [handleSetCurrentUser]);

    const handleLogout = useCallback(() => {
        setIsAuthenticated(false);
        setSelectedWorkshopId(null);
    }, []);

    const handleReset = useCallback(async () => {
        if (!window.confirm("Are you sure you want to reset all application data? This action cannot be undone.")) return;
        setIsLoading(true);
        const initialData = await api.resetAppData();
        setAppState(initialData);
        setIsAuthenticated(false);
        setIsLoading(false);
    }, []);

    const handleRestore = useCallback(async (state: AppState) => {
        setAppState(state);
    }, []);

    const handleCreateRequest = useCallback(async (requestData: Omit<Request, 'id' | 'status' | 'created_at' | 'created_by_user_id'>) => {
        if (!appState) return;
        const maxId = appState.requests.length > 0 ? Math.max(...appState.requests.map(r => r.id)) : 0;
        const newRequest: Request = { 
            ...requestData, 
            id: maxId + 1, 
            status: 'Pending',
            created_by_user_id: appState.currentUser.id,
            created_at: new Date().toISOString(),
        };
        const auditLog = logAction('TRANSFER_REQUEST_CREATE', { requestId: newRequest.id, from: requestData.from_workshop_id, to: requestData.to_workshop_id }, appState);
        await updateState({ ...appState, requests: [...appState.requests, newRequest], auditLog });
        closeRequestModal();
    }, [appState, logAction, updateState]);

    const handleCreateRequestBatch = useCallback(async (requestsData: Omit<Request, 'id' | 'status' | 'created_at' | 'created_by_user_id'>[]) => {
        if (!appState) return;
        let currentMaxId = appState.requests.length > 0 ? Math.max(...appState.requests.map(r => r.id)) : 0;
        const newRequests: Request[] = [];
        const newAuditLogEntries: AuditLog[] = [];
        requestsData.forEach((requestData) => {
            currentMaxId++;
            const newRequest: Request = {
                ...requestData, id: currentMaxId, status: 'Pending' as const,
                created_by_user_id: appState.currentUser.id, created_at: new Date().toISOString(),
            };
            newRequests.push(newRequest);
            const newLogEntry: AuditLog = {
                id: Date.now() + currentMaxId, action: 'TRANSFER_REQUEST_CREATE', user_id: appState.currentUser.id,
                details: { requestId: newRequest.id, from: requestData.from_workshop_id, to: requestData.to_workshop_id },
                created_at: new Date().toISOString(),
            };
            newAuditLogEntries.push(newLogEntry);
        });
        const finalState: AppState = {
            ...appState, requests: [...appState.requests, ...newRequests],
            auditLog: [...newAuditLogEntries.reverse(), ...appState.auditLog].slice(0, 100),
        };
        await updateState(finalState);
    }, [appState, updateState]);

    const handleLogConsumptionBatch = useCallback(async (workshopId: number, items: RequestItemDetail[], totalCost: number, reason?: string) => {
        if (!appState) return;
        const maxId = appState.requests.length > 0 ? Math.max(...appState.requests.map(r => r.id)) : 0;
        const newRequest: Request = {
            id: maxId + 1, type: 'CONSUMPTION', status: 'Pending', created_by_user_id: appState.currentUser.id,
            created_at: new Date().toISOString(), workshop_id: workshopId, items, total_cost: totalCost,
            reason: reason,
        };
        const auditLog = logAction('CONSUMPTION_LOG_CREATE', { requestId: newRequest.id, workshopId, itemCount: items.length }, appState);
        await updateState({ ...appState, requests: [...appState.requests, newRequest], auditLog });
    }, [appState, logAction, updateState]);
  
    const handleRejectRequest = useCallback(async (requestId: number) => {
        if (!appState) return;
        const request = appState.requests.find((r: Request) => r.id === requestId);
        if (!request) return;
        const action = request.type === 'TRANSFER' ? 'TRANSFER_REQUEST_REJECT' : 'CONSUMPTION_LOG_REJECT';
        const updatedRequests = appState.requests.map((r: Request) => r.id === requestId ? { ...r, status: 'Rejected' as const } : r);
        const auditLog = logAction(action, { requestId }, appState);
        await updateState({ ...appState, requests: updatedRequests, auditLog });
    }, [appState, logAction, updateState]);

    const handleApproveRequest = useCallback(async (requestId: number) => {
        if (!appState) return;
        const request = appState.requests.find((r: Request) => r.id === requestId);
        if (!request) return;
        let newState = { ...appState };
        if (request.type === 'TRANSFER' && request.from_workshop_id && request.to_workshop_id) {
            let inventoriesToUpdate = [...newState.workshopInventories];
            let nextInventoryId = newState.workshopInventories.length > 0 ? Math.max(...newState.workshopInventories.map(i => i.id)) + 1 : 1;

            for (const itemToTransfer of request.items) {
                const fromWorkshopInventory = inventoriesToUpdate.find((wi: WorkshopInventoryItem) => wi.workshop_id === request.from_workshop_id && wi.item_id === itemToTransfer.item_id);
                if (!fromWorkshopInventory || fromWorkshopInventory.quantity < itemToTransfer.quantity) {
                    const itemName = newState.catalog.find((c: CatalogItem) => c.id === itemToTransfer.item_id)?.name || `Item ID ${itemToTransfer.item_id}`;
                    alert(`Inventory is insufficient for "${itemName}". Request rejected.`);
                    handleRejectRequest(requestId);
                    return;
                }
            }

            for (const itemToTransfer of request.items) {
                const fromInventoryIndex = inventoriesToUpdate.findIndex((wi: WorkshopInventoryItem) => wi.workshop_id === request.from_workshop_id && wi.item_id === itemToTransfer.item_id);
                inventoriesToUpdate[fromInventoryIndex] = { ...inventoriesToUpdate[fromInventoryIndex], quantity: inventoriesToUpdate[fromInventoryIndex].quantity - itemToTransfer.quantity };

                const toInventoryIndex = inventoriesToUpdate.findIndex((wi: WorkshopInventoryItem) => wi.workshop_id === request.to_workshop_id && wi.item_id === itemToTransfer.item_id);
                if (toInventoryIndex > -1) {
                    inventoriesToUpdate[toInventoryIndex] = { ...inventoriesToUpdate[toInventoryIndex], quantity: inventoriesToUpdate[toInventoryIndex].quantity + itemToTransfer.quantity };
                } else {
                    inventoriesToUpdate.push({
                        id: nextInventoryId++,
                        workshop_id: request.to_workshop_id,
                        item_id: itemToTransfer.item_id,
                        quantity: itemToTransfer.quantity,
                        low_stock_threshold: 10
                    });
                }
            }
            newState.workshopInventories = inventoriesToUpdate;
            newState.auditLog = logAction('TRANSFER_REQUEST_APPROVE', { requestId, itemCount: request.items.length }, newState);
        } else if (request.type === 'CONSUMPTION' && request.workshop_id && request.items) {
            let updatedInventories = [...newState.workshopInventories];
            let newConsumed = [...newState.consumed];
            for (const consumedItem of request.items) {
                const inventoryIndex = updatedInventories.findIndex((wi: WorkshopInventoryItem) => wi.workshop_id === request.workshop_id && wi.item_id === consumedItem.item_id);
                if (inventoryIndex === -1 || updatedInventories[inventoryIndex].quantity < consumedItem.quantity) {
                     alert(`Not enough stock for ${newState.catalog.find((c: CatalogItem) => c.id === consumedItem.item_id)?.name}. Request rejected.`);
                     handleRejectRequest(requestId);
                     return;
                }
                updatedInventories[inventoryIndex] = { ...updatedInventories[inventoryIndex], quantity: updatedInventories[inventoryIndex].quantity - consumedItem.quantity };
                const catalogItem = newState.catalog.find((ci: CatalogItem) => ci.id === consumedItem.item_id);
                if (catalogItem) {
                     const existingConsumedItemIndex = newConsumed.findIndex((item: ConsumedItem) => item.id === consumedItem.item_id);
                     if (existingConsumedItemIndex > -1) {
                        newConsumed[existingConsumedItemIndex].quantity += consumedItem.quantity;
                     } else {
                        newConsumed.push({ id: catalogItem.id, name: catalogItem.name, quantity: consumedItem.quantity, cost: catalogItem.default_unit_price });
                     }
                }
            }
            newState.workshopInventories = updatedInventories;
            newState.consumed = newConsumed;
            newState.auditLog = logAction('CONSUMPTION_LOG_APPROVE', { requestId, workshopId: request.workshop_id, itemCount: request.items.length }, newState);
        }
        const updatedRequests = newState.requests.map((r: Request) => r.id === requestId ? { ...r, status: 'Approved' as const } : r);
        newState.requests = updatedRequests;
        await updateState(newState);
    }, [appState, handleRejectRequest, logAction, updateState]);

    const handleStageData = useCallback(async (stagedImportData: Omit<StagedImport, 'id' | 'user_id' | 'created_at'>) => {
        if (!appState) return;
        const newId = Date.now();
        const newStagedImport: StagedImport = { ...stagedImportData, id: newId, user_id: appState.currentUser.id, created_at: new Date().toISOString() };
        const auditLog = logAction('DATA_STAGED', { importId: newId, fileName: newStagedImport.fileName, type: newStagedImport.importType }, appState);
        await updateState({ ...appState, stagedImports: [newStagedImport, ...appState.stagedImports], auditLog });
    }, [appState, logAction, updateState]);

    const handleUpdateStagedImport = useCallback(async (importId: number, updatedRows: StagedImportRow[]) => {
        if (!appState) return;
        const stagedImportIndex = appState.stagedImports.findIndex(imp => imp.id === importId);
        if (stagedImportIndex === -1) return;
        const originalImport = appState.stagedImports[stagedImportIndex];
        const catalogSkuMap = new Map(appState.catalog.map(i => [i.sku.toLowerCase(), i]));
        const workshopIdSet = new Set(appState.workshops.map(w => w.id));
        const getMappedValue = (row: StagedImportRow, fieldName: string): string => {
            if (!originalImport.columnMapping || !originalImport.columnMapping[fieldName]) return "";
            const headerName = originalImport.columnMapping[fieldName];
            const headerIndex = originalImport.fileHeaders.indexOf(headerName);
            return headerIndex !== -1 ? (row.data[headerIndex] || "") : "";
        };
        const revalidatedRows = updatedRows.map(row => {
            let isValid = true; let message = '';
            if (originalImport.importType === 'Inventory') {
                const name = getMappedValue(row, 'Item Name'); const sku = getMappedValue(row, 'SKU'); const quantity = getMappedValue(row, 'Initial Quantity');
                if (!name?.trim() || !sku?.trim() || !quantity?.trim() || isNaN(parseInt(quantity, 10))) { isValid = false; message = 'Invalid data. Ensure required fields have values and quantity is a number.'; }
            } else if (originalImport.importType === 'Users') {
                const name = getMappedValue(row, 'Full Name'); const employeeId = getMappedValue(row, 'Employee ID'); const role = getMappedValue(row, 'Role');
                if (!name?.trim() || !employeeId?.trim() || !role?.trim()) { isValid = false; message = 'Invalid data. Ensure all fields are present.'; }
            } else if (originalImport.importType === 'Workshop Stock') {
                const wsId = getMappedValue(row, 'Workshop ID'); const sku = getMappedValue(row, 'Item SKU'); const quantity = getMappedValue(row, 'Quantity');
                if (!wsId?.trim() || !sku?.trim() || !quantity?.trim()) { isValid = false; message = 'Required field is missing.';
                } else if (isNaN(parseInt(quantity, 10))) { isValid = false; message = `Quantity must be a valid number.`;
                } else if (!workshopIdSet.has(parseInt(wsId, 10))) { isValid = false; message = `Workshop ID "${wsId}" does not exist.`;
                } else if (!catalogSkuMap.has(sku.toLowerCase())) { isValid = false; message = `Item SKU "${sku}" does not exist in the catalog.`; }
            }
            return { ...row, status: isValid ? 'ok' : 'error', message: isValid ? undefined : message } as StagedImportRow;
        });
        const valid = revalidatedRows.filter(r => r.status === 'ok').length; const invalid = revalidatedRows.length - valid;
        const updatedImport: StagedImport = { ...originalImport, rows: revalidatedRows, summary: { total: revalidatedRows.length, valid, invalid } };
        const updatedStagedImports = [...appState.stagedImports]; updatedStagedImports[stagedImportIndex] = updatedImport;
        const auditLog = logAction('STAGED_DATA_EDITED', { importId, fileName: originalImport.fileName }, appState);
        await updateState({ ...appState, stagedImports: updatedStagedImports, auditLog });
    }, [appState, logAction, updateState]);

    const handleProcessStagedImport = useCallback(async (importId: number) => {
        if (!appState) return;
        const stagedImport = appState.stagedImports.find(imp => imp.id === importId);
        if (!stagedImport || stagedImport.status !== 'staged') return;
        let finalState: AppState = { ...appState };
        finalState.stagedImports = finalState.stagedImports.map(imp => imp.id === importId ? { ...imp, status: 'processing' as const } : imp);
        await updateState(finalState); await new Promise(res => setTimeout(res, 500)); 
        const validRows = stagedImport.rows.filter(r => r.status === 'ok');
        const getMappedValue = (row: StagedImportRow, fieldName: string): string => {
            if (!stagedImport.columnMapping?.[fieldName]) return "";
            const headerName = stagedImport.columnMapping[fieldName];
            const headerIndex = stagedImport.fileHeaders.indexOf(headerName);
            if (headerIndex === -1) return "";
            const value = row.data[headerIndex];
            return value ? String(value) : "";
        };
        if (stagedImport.importType === 'Inventory') {
            const importedSkuSet = new Set<string>(); validRows.forEach(row => { const sku = getMappedValue(row, 'SKU')?.toLowerCase(); if (sku) importedSkuSet.add(sku); });
            const itemIdsToDelete = new Set<number>(); appState.catalog.forEach(item => { if (!importedSkuSet.has(item.sku.toLowerCase())) { itemIdsToDelete.add(item.id); } });
            let newCatalog: CatalogItem[] = [...finalState.catalog]; let newWorkshopInventories: WorkshopInventoryItem[] = [...finalState.workshopInventories];
            let nextCatalogId = finalState.catalog.length > 0 ? Math.max(...finalState.catalog.map((i: CatalogItem) => i.id)) + 1 : 1;
            let nextInventoryId = finalState.workshopInventories.length > 0 ? Math.max(...finalState.workshopInventories.map((i: WorkshopInventoryItem) => i.id)) + 1 : 1;
            for (const row of validRows) {
                const sku = getMappedValue(row, 'SKU'); if (!sku) continue;
                const quantityStr = getMappedValue(row, 'Initial Quantity'); const quantity = quantityStr ? parseInt(quantityStr, 10) : 0;
                const existingCatalogItemIndex = newCatalog.findIndex(item => item.sku.toLowerCase() === sku.toLowerCase());
                if (existingCatalogItemIndex > -1) {
                    const itemToUpdate = newCatalog[existingCatalogItemIndex];
                    const newName = getMappedValue(row, 'Item Name');
                    const newDescription = getMappedValue(row, 'Description');
                    const newCategory = getMappedValue(row, 'Category');
                    const newPriceStr = getMappedValue(row, 'Unit Price');
                    if (newName) itemToUpdate.name = newName;
                    if (newDescription) itemToUpdate.description = newDescription;
                    if (newCategory) itemToUpdate.category = newCategory;
                    if (newPriceStr) { const price = parseFloat(newPriceStr); if (!isNaN(price)) itemToUpdate.default_unit_price = price; }
                    const inventoryItemIndex = newWorkshopInventories.findIndex(inv => inv.item_id === itemToUpdate.id && inv.workshop_id === 1);
                    if (inventoryItemIndex > -1) { newWorkshopInventories[inventoryItemIndex].quantity = quantity;
                    } else { newWorkshopInventories.push({ id: nextInventoryId++, workshop_id: 1, item_id: itemToUpdate.id, quantity: quantity, low_stock_threshold: 10 }); }
                } else {
                    const name = getMappedValue(row, 'Item Name'); if (!name) continue; 
                    const description = getMappedValue(row, 'Description') || `Imported via ${stagedImport.source}`; const category = getMappedValue(row, 'Category') || 'Imported';
                    const priceStr = getMappedValue(row, 'Unit Price'); const price = priceStr ? parseFloat(priceStr) : 1.00;
                    const newCatalogItem: CatalogItem = {
                        id: nextCatalogId, name, sku, description, image_url: '/img/placeholder.jpg', category,
                        default_unit_price: isNaN(price) ? 1.00 : price, is_restricted: false, custom_fields: {},
                    };
                    newCatalog.push(newCatalogItem); newWorkshopInventories.push({ id: nextInventoryId++, workshop_id: 1, item_id: newCatalogItem.id, quantity: quantity, low_stock_threshold: 10 });
                    nextCatalogId++;
                }
            }
            finalState.catalog = newCatalog.filter((item: CatalogItem) => !itemIdsToDelete.has(item.id)); finalState.workshopInventories = newWorkshopInventories.filter((inv: WorkshopInventoryItem) => !itemIdsToDelete.has(inv.item_id));
        } else if (stagedImport.importType === 'Users') {
            const importedIdSet = new Set<string>(); validRows.forEach(row => { const employeeId = getMappedValue(row, 'Employee ID')?.toLowerCase(); if (employeeId) importedIdSet.add(employeeId); });
            let userIdsToDelete = new Set<number>();
            if (finalState.users && Array.isArray(finalState.users)) {
                userIdsToDelete = new Set<number>(finalState.users.filter(user => !importedIdSet.has(user.employeeId.toLowerCase())).map(user => user.id));
            }
            let newUsers: UserType[] = [...finalState.users];
            let nextUserId = finalState.users.length > 0 ? Math.max(...finalState.users.map(u => u.id)) + 1 : 1;
            for (const row of validRows) {
                const name = getMappedValue(row, 'Full Name'); const employeeId = getMappedValue(row, 'Employee ID'); const role = getMappedValue(row, 'Role'); const workshopIdStr = getMappedValue(row, 'Primary Workshop ID');
                if (!name || !employeeId || !role) continue;
                const existingUserIndex = newUsers.findIndex(u => u.employeeId.toLowerCase() === employeeId.toLowerCase());
                if (existingUserIndex > -1) {
                     newUsers[existingUserIndex].name = name; newUsers[existingUserIndex].role = role as UserRole;
                     if (workshopIdStr) { newUsers[existingUserIndex].primary_workshop_id = parseInt(workshopIdStr, 10) || 1; }
                } else {
                    newUsers.push({ id: nextUserId++, name, employeeId, role: role as UserRole, password: '1111', primary_workshop_id: workshopIdStr ? parseInt(workshopIdStr, 10) : 1, avatarUrl: `https://i.pravatar.cc/150?u=${employeeId.trim()}`, reelImageUrl: `https://picsum.photos/seed/${employeeId.trim()}/600/200` });
                }
            }
            finalState.users = newUsers.filter((user: UserType) => !userIdsToDelete.has(user.id));
        } else if (stagedImport.importType === 'Workshop Stock') {
            let newWorkshopInventories: WorkshopInventoryItem[] = [...finalState.workshopInventories]; const catalogSkuMap = new Map(finalState.catalog.map((i: CatalogItem) => [i.sku.toLowerCase(), i]));
            let nextInventoryId = finalState.workshopInventories.length > 0 ? Math.max(...finalState.workshopInventories.map((i: WorkshopInventoryItem) => i.id)) + 1 : 1;
            for (const row of validRows) {
                const workshopIdStr = getMappedValue(row, 'Workshop ID'); const sku = getMappedValue(row, 'Item SKU'); const quantityStr = getMappedValue(row, 'Quantity'); const lowStockThresholdStr = getMappedValue(row, 'Low Stock Threshold');
                if (!workshopIdStr || !sku || !quantityStr) continue;
                const workshopId = parseInt(workshopIdStr, 10); const quantity = parseInt(quantityStr, 10); const lowStockThreshold = lowStockThresholdStr ? parseInt(lowStockThresholdStr, 10) : 10;
                const catalogItem = catalogSkuMap.get(sku.toLowerCase()); if (!catalogItem) continue;
                const inventoryItemIndex = newWorkshopInventories.findIndex(inv => inv.workshop_id === workshopId && inv.item_id === catalogItem.id);
                if (inventoryItemIndex > -1) {
                    newWorkshopInventories[inventoryItemIndex] = { ...newWorkshopInventories[inventoryItemIndex], quantity: quantity, low_stock_threshold: isNaN(lowStockThreshold) ? newWorkshopInventories[inventoryItemIndex].low_stock_threshold : lowStockThreshold };
                } else {
                    newWorkshopInventories.push({ id: nextInventoryId++, workshop_id: workshopId, item_id: catalogItem.id, quantity: quantity, low_stock_threshold: isNaN(lowStockThreshold) ? 10 : lowStockThreshold });
                }
            }
            finalState.workshopInventories = newWorkshopInventories;
        } else if (stagedImport.importType === 'Add/Update Inventory') {
            let newCatalog: CatalogItem[] = [...finalState.catalog];
            let newWorkshopInventories: WorkshopInventoryItem[] = [...finalState.workshopInventories];
            let nextCatalogId = finalState.catalog.length > 0 ? Math.max(...finalState.catalog.map((i: CatalogItem) => i.id)) + 1 : 1;
            let nextInventoryId = finalState.workshopInventories.length > 0 ? Math.max(...finalState.workshopInventories.map((i: WorkshopInventoryItem) => i.id)) + 1 : 1;

            for (const row of validRows) {
                const sku = getMappedValue(row, 'SKU');
                if (!sku) continue;

                const existingCatalogItemIndex = newCatalog.findIndex(item => item.sku.toLowerCase() === sku.toLowerCase());

                if (existingCatalogItemIndex > -1) {
                    // Update existing item
                    const itemToUpdate = newCatalog[existingCatalogItemIndex];
                    const newName = getMappedValue(row, 'Item Name');
                    const newDescription = getMappedValue(row, 'Description');
                    const newCategory = getMappedValue(row, 'Category');
                    const newPriceStr = getMappedValue(row, 'Unit Price');
                    const quantityStr = getMappedValue(row, 'Initial Quantity');

                    if (newName) itemToUpdate.name = newName;
                    if (newDescription) itemToUpdate.description = newDescription;
                    if (newCategory) itemToUpdate.category = newCategory;
                    if (newPriceStr) {
                        const price = parseFloat(newPriceStr);
                        if (!isNaN(price)) itemToUpdate.default_unit_price = price;
                    }

                    if (quantityStr) {
                        const quantity = parseInt(quantityStr, 10);
                        if (!isNaN(quantity)) {
                            const inventoryItemIndex = newWorkshopInventories.findIndex(inv => inv.item_id === itemToUpdate.id && inv.workshop_id === 1);
                            if (inventoryItemIndex > -1) {
                                newWorkshopInventories[inventoryItemIndex].quantity += quantity;
                            } else {
                                newWorkshopInventories.push({ id: nextInventoryId++, workshop_id: 1, item_id: itemToUpdate.id, quantity: quantity, low_stock_threshold: 10 });
                            }
                        }
                    }
                } else {
                    // Add new item
                    const name = getMappedValue(row, 'Item Name');
                    if (!name) continue;

                    const description = getMappedValue(row, 'Description') || `Imported via ${stagedImport.source}`;
                    const category = getMappedValue(row, 'Category') || 'Imported';
                    const priceStr = getMappedValue(row, 'Unit Price');
                    const price = priceStr ? parseFloat(priceStr) : 1.00;
                    const quantityStr = getMappedValue(row, 'Initial Quantity');
                    const quantity = quantityStr ? parseInt(quantityStr, 10) : 0;

                    const newCatalogItem: CatalogItem = {
                        id: nextCatalogId,
                        name,
                        sku,
                        description,
                        image_url: '/img/placeholder.jpg',
                        category,
                        default_unit_price: isNaN(price) ? 1.00 : price,
                        is_restricted: false,
                        custom_fields: {},
                    };
                    newCatalog.push(newCatalogItem);
                    if (!isNaN(quantity) && quantity > 0) {
                        newWorkshopInventories.push({ id: nextInventoryId++, workshop_id: 1, item_id: newCatalogItem.id, quantity: quantity, low_stock_threshold: 10 });
                    }
                    nextCatalogId++;
                }
            }
            finalState.catalog = newCatalog;
            finalState.workshopInventories = newWorkshopInventories;
        }
        finalState.auditLog = logAction('DATA_PROCESSED', { importId, fileName: stagedImport.fileName, type: stagedImport.importType, rows: stagedImport.summary.valid }, finalState);
        finalState.stagedImports = finalState.stagedImports.map(imp => imp.id === importId ? { ...imp, status: 'processed' as const } : imp);
        await updateState(finalState);
    }, [appState, logAction, updateState]);

    const handleDiscardStagedImport = useCallback(async (importId: number) => {
        if (!appState) return;
        const stagedImport = appState.stagedImports.find(imp => imp.id === importId); if (!stagedImport) return;
        const auditLog = logAction('STAGED_DATA_DISCARDED', { importId, fileName: stagedImport.fileName }, appState);
        const updatedImports = appState.stagedImports.map(imp => imp.id === importId ? { ...imp, status: 'discarded' as const } : imp);
        await updateState({ ...appState, stagedImports: updatedImports, auditLog });
    }, [appState, logAction, updateState]);
  
    const handleUpdateUser = useCallback(async (user: UserType) => {
        if (!appState) return;
        const updatedUsers = appState.users.map((u: UserType) => u.id === user.id ? user : u);
        const auditLog = logAction('USER_UPDATED', { userId: user.id, name: user.name }, appState);
        await updateState({ ...appState, users: updatedUsers, auditLog });
    }, [appState, logAction, updateState]);

    const handleUpdateCatalog = useCallback(async (updatedItems: CatalogItem[]) => {
        if (!appState) return;

        const originalItemIds = new Set(appState.catalog.map(i => i.id));
        const updatedItemIds = new Set(updatedItems.map(i => i.id));
        
        const deletedItemIds = new Set([...originalItemIds].filter(id => !updatedItemIds.has(id)));
        
        let updatedWorkshopInventories = appState.workshopInventories;
        if (deletedItemIds.size > 0) {
            updatedWorkshopInventories = appState.workshopInventories.filter(wi => !deletedItemIds.has(wi.item_id));
        }

        const auditLog = logAction('CATALOG_UPDATED', { 
            itemCount: updatedItems.length,
            deletedCount: deletedItemIds.size 
        }, appState);
        
        await updateState({ 
            ...appState, 
            catalog: updatedItems, 
            workshopInventories: updatedWorkshopInventories,
            auditLog 
        });
    }, [appState, logAction, updateState]);
  
    const handleUpdateCustomFields = useCallback(async (newFields: CatalogCustomField[]) => {
        if (!appState) return;
        const oldFieldIds = new Set(appState.catalogCustomFields.map(f => f.id));
        const newFieldIds = new Set(newFields.map(f => f.id));
        const deletedFieldIds = [...oldFieldIds].filter(id => !newFieldIds.has(id));
        let updatedCatalog = appState.catalog;
        if (deletedFieldIds.length > 0) {
            updatedCatalog = appState.catalog.map((item: CatalogItem) => {
                const newCustomFields = { ...item.custom_fields };
                deletedFieldIds.forEach(fieldId => {
                    delete (newCustomFields as any)[fieldId];
                });
                return { ...item, custom_fields: newCustomFields };
            });
        }
        const auditLog = logAction('CATALOG_FIELDS_UPDATED', { newFieldCount: newFields.length }, appState);
        await updateState({ ...appState, catalog: updatedCatalog, catalogCustomFields: newFields, auditLog });
    }, [appState, logAction, updateState]);

    const handleCreateUser = useCallback(async (userData: Omit<UserType, 'id' | 'avatarUrl' | 'reelImageUrl'>) => {
        if (!appState) return;
        const maxId = appState.users.length > 0 ? Math.max(...appState.users.map(u => u.id)) : 0;
        const newId = maxId + 1;
        const employeeId = userData.employeeId.trim();
        const newUser: UserType = { 
            ...userData, 
            id: newId,
            avatarUrl: `https://i.pravatar.cc/150?u=${employeeId}`,
            reelImageUrl: `https://picsum.photos/seed/${employeeId}/600/200`,
        };
        const auditLog = logAction('USER_CREATED', { userId: newUser.id, name: newUser.name }, appState);
        await updateState({ ...appState, users: [...appState.users, newUser], auditLog });
    }, [appState, logAction, updateState]);
    
    const handleCreateWorkshop = useCallback(async (workshopData: Omit<Workshop, 'id'>) => {
        if (!appState) return;
        const maxId = appState.workshops.length > 0 ? Math.max(...appState.workshops.map(w => w.id)) : 0;
        const newWorkshop = { ...workshopData, id: maxId + 1 };
        const auditLog = logAction('WORKSHOP_CREATED', { workshopId: newWorkshop.id, name: newWorkshop.name }, appState);
        await updateState({ ...appState, workshops: [...appState.workshops, newWorkshop], auditLog });
    }, [appState, logAction, updateState]);

    const handleAddItemToCatalog = useCallback(async (itemData: Omit<CatalogItem, 'id' | 'custom_fields'>) => {
      if (!appState) return;
      const maxId = appState.catalog.length > 0 ? Math.max(...appState.catalog.map(i => i.id)) : 0;
      const newId = maxId + 1;
      const custom_fields: Record<string, any> = {};
      appState.catalogCustomFields.forEach((field: CatalogCustomField) => {
        switch (field.type) {
            case 'number': custom_fields[field.id] = 0; break;
            case 'boolean': custom_fields[field.id] = false; break;
            case 'date': custom_fields[field.id] = ''; break;
            default: custom_fields[field.id] = '';
        }
      });
      const newItem = { ...itemData, id: newId, custom_fields };
      const auditLog = logAction('CATALOG_ITEM_CREATED', { itemId: newId, name: newItem.name }, appState);
      const maxInvId = appState.workshopInventories.length > 0 ? Math.max(...appState.workshopInventories.map(wi => wi.id)) : 0;
      const newInventoryItem = { id: maxInvId + 1, workshop_id: 1, item_id: newId, quantity: 0, low_stock_threshold: 10 };
      await updateState({ ...appState, catalog: [...appState.catalog, newItem], workshopInventories: [...appState.workshopInventories, newInventoryItem], auditLog });
    }, [appState, logAction, updateState]);

    const handleCreateSubProgram = useCallback(async (subProgramData: Omit<SubProgram, 'id'>) => {
        if (!appState) return;
        const maxId = appState.subPrograms.length > 0 ? Math.max(...appState.subPrograms.map(sp => sp.id)) : 0;
        const newSubProgram = { ...subProgramData, id: maxId + 1 };
        const auditLog = logAction('SUBPROGRAM_CREATED', { subProgramId: newSubProgram.id, name: newSubProgram.name }, appState);
        await updateState({ ...appState, subPrograms: [...appState.subPrograms, newSubProgram], auditLog });
    }, [appState, logAction, updateState]);

    const handleCreateProgram = useCallback(async (programData: Omit<Program, 'id'>) => {
        if (!appState) return;
        const maxId = appState.programs.length > 0 ? Math.max(...appState.programs.map(p => p.id)) : 0;
        const newProgram = { ...programData, id: maxId + 1 };
        const auditLog = logAction('PROGRAM_CREATED', { programId: newProgram.id, name: newProgram.name }, appState);
        await updateState({ ...appState, programs: [...appState.programs, newProgram], auditLog });
    }, [appState, logAction, updateState]);

    const handleCreateTeam = useCallback(async (teamData: { workshopId: number; teamLeaderId: number; memberIds: number[] }) => {
        if (!appState) return;
        const { workshopId, teamLeaderId, memberIds } = teamData;
        const userIdsToUpdate = [teamLeaderId, ...memberIds];
        const updatedUsers = appState.users.map(user => {
            if (userIdsToUpdate.includes(user.id)) {
                return { ...user, primary_workshop_id: workshopId };
            }
            return user;
        });
        const auditLog = logAction('TEAM_CREATED', { workshopId, teamLeaderId, memberCount: memberIds.length }, appState);
        await updateState({ ...appState, users: updatedUsers, auditLog });
    }, [appState, logAction, updateState]);

    const handleDeleteUser = useCallback(async (userId: number) => {
        if (!appState) return;
        const updatedUsers = appState.users.filter(user => user.id !== userId);
        const auditLog = logAction('USER_DELETED', { userId }, appState);
        await updateState({ ...appState, users: updatedUsers, auditLog });
    }, [appState, logAction, updateState]);

    const handleUpdateWorkshop = useCallback(async (workshop: Workshop) => {
        if (!appState) return;
        const updatedWorkshops = appState.workshops.map(w => w.id === workshop.id ? workshop : w);
        const auditLog = logAction('WORKSHOP_UPDATED', { workshopId: workshop.id, name: workshop.name }, appState);
        await updateState({ ...appState, workshops: updatedWorkshops, auditLog });
    }, [appState, logAction, updateState]);

    const handleDeleteWorkshop = useCallback(async (workshopId: number) => {
        if (!appState) return;
        const updatedWorkshops = appState.workshops.filter(w => w.id !== workshopId);
        // Also remove associated inventory and user assignments
        const updatedInventories = appState.workshopInventories.filter(wi => wi.workshop_id !== workshopId);
        const updatedUsers = appState.users.map(u => u.primary_workshop_id === workshopId ? { ...u, primary_workshop_id: 1 } : u); // Re-assign users to central
        const auditLog = logAction('WORKSHOP_DELETED', { workshopId }, appState);
        await updateState({ ...appState, workshops: updatedWorkshops, workshopInventories: updatedInventories, users: updatedUsers, auditLog });
    }, [appState, logAction, updateState]);

    const handleUpdateProgram = useCallback(async (program: Program) => {
        if (!appState) return;
        const updatedPrograms = appState.programs.map(p => p.id === program.id ? program : p);
        const auditLog = logAction('PROGRAM_UPDATED', { programId: program.id, name: program.name }, appState);
        await updateState({ ...appState, programs: updatedPrograms, auditLog });
    }, [appState, logAction, updateState]);

    const handleDeleteProgram = useCallback(async (programId: number) => {
        if (!appState) return;
        const updatedPrograms = appState.programs.filter(p => p.id !== programId);
        const subProgramIdsToDelete = appState.subPrograms.filter(sp => sp.program_id === programId).map(sp => sp.id);
        const updatedSubPrograms = appState.subPrograms.filter(sp => sp.program_id !== programId);
        const workshopIdsToDelete = appState.workshops.filter(w => subProgramIdsToDelete.includes(w.sub_program_id)).map(w => w.id);
        const updatedWorkshops = appState.workshops.filter(w => !workshopIdsToDelete.includes(w.id));
        const updatedInventories = appState.workshopInventories.filter(wi => !workshopIdsToDelete.includes(wi.workshop_id));
        const updatedUsers = appState.users.map(u => workshopIdsToDelete.includes(u.primary_workshop_id) ? { ...u, primary_workshop_id: 1 } : u);
        const auditLog = logAction('PROGRAM_DELETED', { programId }, appState);
        await updateState({ ...appState, programs: updatedPrograms, subPrograms: updatedSubPrograms, workshops: updatedWorkshops, workshopInventories: updatedInventories, users: updatedUsers, auditLog });
    }, [appState, logAction, updateState]);

    const handleUpdateSubProgram = useCallback(async (subProgram: SubProgram) => {
        if (!appState) return;
        const updatedSubPrograms = appState.subPrograms.map(sp => sp.id === subProgram.id ? subProgram : sp);
        const auditLog = logAction('SUBPROGRAM_UPDATED', { subProgramId: subProgram.id, name: subProgram.name }, appState);
        await updateState({ ...appState, subPrograms: updatedSubPrograms, auditLog });
    }, [appState, logAction, updateState]);

    const handleDeleteSubProgram = useCallback(async (subProgramId: number) => {
        if (!appState) return;
        const updatedSubPrograms = appState.subPrograms.filter(sp => sp.id !== subProgramId);
        // This is a simpler delete, we just need to re-assign workshops to a default sub-program if they were assigned to the deleted one.
        // For now, we'll just nullify it, but a better implementation would be to have a default "Unassigned" sub-program.
        const updatedWorkshops = appState.workshops.map(w => w.sub_program_id === subProgramId ? { ...w, sub_program_id: -1 } : w);
        const auditLog = logAction('SUBPROGRAM_DELETED', { subProgramId }, appState);
        await updateState({ ...appState, subPrograms: updatedSubPrograms, workshops: updatedWorkshops, auditLog });
    }, [appState, logAction, updateState]);
    
    const handleUpdateBreakSchedule = useCallback(async (workshopId: number, schedule: BreakSchedule) => {
        if (!appState) return;
        const newBreakSchedules = { ...appState.breakSchedules, [workshopId]: schedule };
        const workshopName = appState.workshops.find((w: Workshop) => w.id === workshopId)?.name || 'Unknown Workshop';
        const auditLog = logAction('BREAK_SCHEDULE_UPDATE', { workshopId, workshopName }, appState);
        await updateState({ ...appState, breakSchedules: newBreakSchedules, auditLog });
    }, [appState, logAction, updateState]);


    const value = useMemo(() => ({
        appState, isLoading, isAuthenticated, isRequestModalOpen, isSidebarOpen, isBreakSchedulerModalOpen, totalCost, selectedWorkshopId, isIdle
    }), [appState, isLoading, isAuthenticated, isRequestModalOpen, isSidebarOpen, isBreakSchedulerModalOpen, totalCost, selectedWorkshopId, isIdle]);

    const actionsValue = useMemo(() => ({
        setIsIdle, handleSetCurrentUser, handleLogin, handleLogout, handleReset, handleRestore, handleSelectWorkshop, handleCreateRequest, handleCreateRequestBatch,
        handleLogConsumptionBatch, handleRejectRequest, handleApproveRequest, handleStageData,
        handleUpdateStagedImport, handleProcessStagedImport, handleDiscardStagedImport,
        handleUpdateUser, handleUpdateCatalog, handleUpdateCustomFields, handleCreateUser,
        handleCreateWorkshop, handleAddItemToCatalog, handleUpdateBreakSchedule, handleCreateSubProgram, handleCreateProgram, handleCreateTeam, handleDeleteUser, handleUpdateWorkshop, handleDeleteWorkshop, handleUpdateProgram, handleDeleteProgram, handleUpdateSubProgram, handleDeleteSubProgram,
        openRequestModal, closeRequestModal, 
        toggleSidebar, openBreakSchedulerModal, closeBreakSchedulerModal,
    }), [
        setIsIdle, handleSetCurrentUser, handleLogin, handleLogout, handleReset, handleRestore, handleSelectWorkshop, handleCreateRequest, handleCreateRequestBatch,
        handleLogConsumptionBatch, handleRejectRequest, handleApproveRequest, handleStageData,
        handleUpdateStagedImport, handleProcessStagedImport, handleDiscardStagedImport,
        handleUpdateUser, handleUpdateCatalog, handleUpdateCustomFields, handleCreateUser,
        handleCreateWorkshop, handleAddItemToCatalog, handleUpdateBreakSchedule, handleCreateSubProgram, handleCreateProgram, handleCreateTeam, handleDeleteUser, handleUpdateWorkshop, handleDeleteWorkshop, handleUpdateProgram, handleDeleteProgram, handleUpdateSubProgram, handleDeleteSubProgram,
    ]);

    return (
        <AppContext.Provider value={value}>
            <AppActionsContext.Provider value={actionsValue}>
                {children}
            </AppActionsContext.Provider>
        </AppContext.Provider>
    );
};

export const useAppState = (): IAppContext => {
    const context = useContext(AppContext);
    if (context === undefined) {
        throw new Error('useAppState must be used within an AppProvider');
    }
    return context;
};

export const useAppActions = (): IAppActionsContext => {
    const context = useContext(AppActionsContext);
    if (context === undefined) {
        throw new Error('useAppActions must be used within an AppProvider');
    }
    return context;
};