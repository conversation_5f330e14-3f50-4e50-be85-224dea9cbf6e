import React, { useMemo } from 'react';
import { AuditLog, UserType, CatalogItem, Workshop } from '../types';
import { BellIcon, UserIcon } from './icons';
import { useTranslation } from '../i18n/I18nProvider';

interface ActivityFeedProps {
  auditLog: AuditLog[];
  users: UserType[];
  catalog: CatalogItem[];
  workshops: Workshop[];
}

const ActivityFeed: React.FC<ActivityFeedProps> = ({ auditLog, users, catalog, workshops }) => {
  const { t } = useTranslation();

  const userMap = useMemo(() => new Map(users.map(u => [u.id, u])), [users]);
  const workshopMap = useMemo(() => new Map(workshops.map(w => [w.id, w])), [workshops]);

  const formatDistanceToNow = (date: Date, options: { addSuffix?: boolean } = {}) => {
      const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
      let interval = seconds / 31536000;
      if (interval > 1) return `${Math.floor(interval)} ${t('time.years')}${options.addSuffix ? ` ${t('time.ago')}` : ''}`;
      interval = seconds / 2592000;
      if (interval > 1) return `${Math.floor(interval)} ${t('time.months')}${options.addSuffix ? ` ${t('time.ago')}` : ''}`;
      interval = seconds / 86400;
      if (interval > 1) return `${Math.floor(interval)} ${t('time.days')}${options.addSuffix ? ` ${t('time.ago')}` : ''}`;
      interval = seconds / 3600;
      if (interval > 1) return `${Math.floor(interval)} ${t('time.hours')}${options.addSuffix ? ` ${t('time.ago')}` : ''}`;
      interval = seconds / 60;
      if (interval > 1) return `${Math.floor(interval)} ${t('time.minutes')}${options.addSuffix ? ` ${t('time.ago')}` : ''}`;
      return t('time.justNow');
  }

  const renderLogMessage = (log: AuditLog) => {
    const user = userMap.get(log.user_id);
    const details = log.details as any;
    let message = 'An unknown action occurred.';

    switch(log.action) {
      case 'TRANSFER_REQUEST_CREATE': {
        const toWorkshop = workshopMap.get(details.to);
        message = `created a transfer request for ${toWorkshop?.name || 'a RackX'}.`;
        break;
      }
      case 'TRANSFER_REQUEST_APPROVE': {
        message = `approved transfer request #${details.requestId}.`;
        break;
      }
      case 'TRANSFER_REQUEST_REJECT': {
        message = `rejected transfer request #${details.requestId}.`;
        break;
      }
      case 'CONSUMPTION_LOG_CREATE': {
        const fromWorkshop = workshopMap.get(details.workshopId);
        message = `logged a consumption of ${details.itemCount} item(s) from ${fromWorkshop?.name || 'a RackX'} for approval.`;
        break;
      }
      case 'CONSUMPTION_LOG_APPROVE': {
        const fromWorkshop = workshopMap.get(details.workshopId);
        message = `approved a consumption log from ${fromWorkshop?.name || 'a RackX'}.`;
        break;
      }
      case 'CONSUMPTION_LOG_REJECT': {
        message = `rejected consumption log #${details.requestId}.`;
        break;
      }
      case 'USER_CREATED': {
        message = `created a new user: ${details.name}.`;
        break;
      }
      case 'USER_UPDATED': {
        message = `updated the profile for user: ${details.name}.`;
        break;
      }
      case 'WORKSHOP_CREATED': {
        message = `created a new RackX: ${details.name}.`;
        break;
      }
      case 'CATALOG_ITEM_CREATED': {
        message = `added a new item to the catalog: ${details.name}.`;
        break;
      }
      default: {
        message = `performed action: ${log.action}.`;
        break;
      }
    }
    
    return (
        <>
            <span className="font-bold text-slate-200">{user?.name || 'A user'}</span> {message}
        </>
    )
  }

  return (
    <div className="bg-[var(--card-bg-dashboard)] backdrop-blur-md rounded-lg shadow-lg border border-slate-700">
      <div className="p-4 sm:p-6 flex items-center border-b border-slate-700">
        <BellIcon className="w-6 h-6 me-3 text-brand-accent" />
        <h2 className="text-xl font-bold text-slate-100">{t('activity.title')}</h2>
      </div>
      <div className="p-4 sm:p-6 max-h-96 overflow-y-auto">
        {auditLog.length === 0 ? (
          <p className="text-slate-400 text-center py-8">{t('activity.noActivity')}</p>
        ) : (
          <ul className="space-y-4">
            {auditLog.map(log => {
                const user = userMap.get(log.user_id);
                return (
                  <li key={log.id} className="flex items-start space-x-3 rtl:space-x-reverse">
                    <div className="flex-shrink-0">
                      {user ? (
                          <img src={user.avatarUrl} alt={user.name} className="w-8 h-8 rounded-full bg-slate-700" />
                      ) : (
                          <div className="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center">
                              <UserIcon className="w-4 h-4 text-slate-400" />
                          </div>
                      )}
                    </div>
                    <div>
                      <p className="text-sm text-slate-300">
                        {renderLogMessage(log)}
                      </p>
                      <p className="text-xs text-slate-500 mt-0.5">
                        {formatDistanceToNow(new Date(log.created_at), { addSuffix: true })}
                      </p>
                    </div>
                  </li>
                )
            })}
          </ul>
        )}
      </div>
    </div>
  );
};

export default ActivityFeed;