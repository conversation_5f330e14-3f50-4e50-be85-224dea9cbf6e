import React, { createContext, useState, useEffect, useCallback, useContext } from 'react';
import en from './locales/en';
import fr from './locales/fr';
import ar from './locales/ar';

type Language = 'en' | 'fr' | 'ar';
type Translations = typeof en;

const translationsData: Record<Language, Translations> = {
    en,
    fr,
    ar,
};

interface I18nContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: keyof Translations, options?: { [key: string]: string | number }) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export const I18nProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [language, setLanguageState] = useState<Language>(() => (localStorage.getItem('language') as Language) || 'en');
    const [loadedTranslations, setLoadedTranslations] = useState<Translations>(translationsData[language]);

    useEffect(() => {
        setLoadedTranslations(translationsData[language]);
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        localStorage.setItem('language', language);
    }, [language]);
    
    const setLanguage = (lang: Language) => {
        setLanguageState(lang);
    };

    const t = useCallback((key: keyof Translations, options?: { [key: string]: string | number }): string => {
        let translation = loadedTranslations[key] || en[key] || key;
        if (options && typeof translation === 'string') {
            Object.keys(options).forEach(optionKey => {
                translation = translation.replace(`{{${optionKey}}}`, String(options[optionKey]));
            });
        }
        return translation;
    }, [loadedTranslations]);

    return (
        <I18nContext.Provider value={{ language, setLanguage, t }}>
            {children}
        </I18nContext.Provider>
    );
};

export const useTranslation = (): I18nContextType => {
    const context = useContext(I18nContext);
    if (!context) throw new Error('useTranslation must be used within an I18nProvider');
    return context;
};