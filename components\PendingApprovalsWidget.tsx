import React, { useMemo } from 'react';
import { Request, AppState } from '../types';
import { ClipboardListIcon, CheckCircleIcon, XCircleIcon } from './icons';
import { useTranslation } from '../i18n/I18nProvider';

interface PendingApprovalsWidgetProps {
    appState: AppState;
    onApprove: (id: number) => void;
    onReject: (id: number) => void;
}

const PendingApprovalsWidget: React.FC<PendingApprovalsWidgetProps> = ({ appState, onApprove, onReject }) => {
    const { requests, currentUser, users, catalog } = appState;
    const { t } = useTranslation();

    const userMap = useMemo(() => new Map(users.map(u => [u.id, u])), [users]);
    const catalogMap = useMemo(() => new Map(catalog.map(c => [c.id, c])), [catalog]);

    const canApprove = (request: Request) => {
        if (currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') {
            return request.created_by_user_id !== currentUser.id;
        }
        if (currentUser.role === 'TeamLeader') {
            const isMyTeamRequest = (request.type === 'TRANSFER' && request.to_workshop_id === currentUser.primary_workshop_id) ||
                                  (request.type === 'CONSUMPTION' && request.workshop_id === currentUser.primary_workshop_id);
            return isMyTeamRequest && request.created_by_user_id !== currentUser.id;
        }
        return false;
    };

    const pendingApprovals = useMemo(() => {
        return requests
            .filter(r => r.status === 'Pending' && canApprove(r))
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
            .slice(0, 5); // Show top 5
    }, [requests, currentUser]);

    const getRequestTitle = (req: Request): string => {
        if (req.type === 'TRANSFER' && req.items.length > 0) {
            const item = catalogMap.get(req.items[0].item_id);
            return `${req.items[0].quantity}x ${item?.name || 'Item'}`;
        }
        if (req.type === 'CONSUMPTION') {
            return t('approvals.requestTypeConsumption', { count: req.items.length });
        }
        return t('approvals.requestTypeUnknown');
    };

    return (
        <div className="bg-[var(--card-bg-dashboard)] backdrop-blur-md rounded-lg shadow-lg border border-slate-700 h-full">
            <div className="p-4 flex items-center border-b border-slate-700">
                <ClipboardListIcon className="w-6 h-6 me-3 text-brand-accent" />
                <h2 className="text-xl font-bold text-slate-100">{t('approvals.title')}</h2>
            </div>
            <div className="p-4">
                {pendingApprovals.length === 0 ? (
                    <div className="text-center py-8 text-slate-400">
                        <CheckCircleIcon className="mx-auto h-10 w-10 text-green-500" />
                        <p className="mt-2 text-sm">{t('approvals.noApprovals')}</p>
                    </div>
                ) : (
                    <ul className="space-y-3">
                        {pendingApprovals.map(req => {
                            const requester = userMap.get(req.created_by_user_id);
                            return (
                                <li key={req.id} className="bg-slate-900/50 p-3 rounded-lg border border-slate-700/50">
                                    <div className="flex justify-between">
                                        <div>
                                            <p className="text-sm font-semibold text-slate-200">{getRequestTitle(req)}</p>
                                            <p className="text-xs text-slate-400">{t('approvals.requestBy', { name: requester?.name || 'Unknown' })}</p>
                                        </div>
                                        <span className={`px-2 py-0.5 text-xs font-semibold rounded-full self-start ${req.type === 'TRANSFER' ? 'bg-blue-900/50 text-blue-300' : 'bg-purple-900/50 text-purple-300'}`}>{req.type}</span>
                                    </div>
                                    <div className="flex justify-end space-x-2 mt-2">
                                        <button 
                                            onClick={() => onReject(req.id)}
                                            className="p-1.5 text-slate-400 hover:bg-slate-500/20 rounded-full transition-colors"
                                            aria-label={t('approvals.reject', { id: req.id })}
                                        >
                                            <XCircleIcon className="w-5 h-5"/>
                                        </button>
                                        <button 
                                            onClick={() => onApprove(req.id)}
                                            className="p-1.5 text-brand-accent hover:bg-cyan-500/10 rounded-full transition-colors"
                                            aria-label={t('approvals.approve', { id: req.id })}
                                        >
                                            <CheckCircleIcon className="w-5 h-5"/>
                                        </button>
                                    </div>
                                </li>
                            )
                        })}
                    </ul>
                )}
            </div>
        </div>
    );
};

export default PendingApprovalsWidget;