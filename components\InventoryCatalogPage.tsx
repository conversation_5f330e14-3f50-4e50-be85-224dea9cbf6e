

import React, { useState, useMemo, useEffect } from 'react';
import { CatalogItem, CatalogCustomField } from '../types';
import { BoxIcon, SaveIcon, PlusIcon, ChevronsUpDownIcon, Trash2Icon, XCircleIcon } from './icons';
import { useAppState, useAppActions } from '../context/AppContext';
import AddItemToCatalogModal from './AddItemToCatalogModal';

const ITEMS_PER_PAGE = 15;

const InventoryCatalogPage: React.FC = () => {
  const { appState } = useAppState();
  const { handleUpdateCatalog, handleAddItemToCatalog } = useAppActions();
  const { catalog, catalogCustomFields } = appState!;

  const [editedItems, setEditedItems] = useState<CatalogItem[]>([]);
  const [isDirty, setIsDirty] = useState(false);
  const [isAddItemModalOpen, setIsAddItemModalOpen] = useState(false);
  
  // Filtering, Sorting, Pagination State
  const [filters, setFilters] = useState<{ search: string; category: string; restricted: string }>({ search: '', category: 'all', restricted: 'all' });
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>({ key: 'name', direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    // Deep copy to avoid direct state mutation and reset on global state change
    setEditedItems(JSON.parse(JSON.stringify(catalog)));
    setIsDirty(false);
  }, [catalog]);

  const uniqueCategories = useMemo(() => {
    const categories = new Set(catalog.map(item => item.category));
    return ['all', ...Array.from(categories)];
  }, [catalog]);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Reset to first page on filter change
  };

  const requestSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };
  
  const filteredAndSortedItems = useMemo(() => {
    let sortableItems = [...editedItems];
    
    // Filter
    sortableItems = sortableItems.filter(item => {
        const searchLower = filters.search.toLowerCase();
        const nameMatch = item.name.toLowerCase().includes(searchLower);
        const skuMatch = item.sku.toLowerCase().includes(searchLower);
        
        const categoryMatch = filters.category === 'all' || item.category === filters.category;
        
        let restrictedMatch = true;
        if (filters.restricted !== 'all') {
            restrictedMatch = String(item.is_restricted) === filters.restricted;
        }
        
        return (nameMatch || skuMatch) && categoryMatch && restrictedMatch;
    });

    // Sort
    if (sortConfig !== null) {
      sortableItems.sort((a, b) => {
        const aVal = sortConfig.key in a ? a[sortConfig.key as keyof CatalogItem] : a.custom_fields[sortConfig.key];
        const bVal = sortConfig.key in b ? b[sortConfig.key as keyof CatalogItem] : b.custom_fields[sortConfig.key];
        
        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return sortableItems;
  }, [editedItems, filters, sortConfig]);

  const paginatedItems = useMemo(() => {
    const start = (currentPage - 1) * ITEMS_PER_PAGE;
    return filteredAndSortedItems.slice(start, start + ITEMS_PER_PAGE);
  }, [filteredAndSortedItems, currentPage]);
  
  const totalPages = Math.ceil(filteredAndSortedItems.length / ITEMS_PER_PAGE);

  const handleInputChange = (itemId: number, field: keyof CatalogItem | string, value: any, fieldType: 'text' | 'number' | 'boolean' = 'text') => {
    setEditedItems(currentItems => currentItems.map(item => {
        if (item.id === itemId) {
            const updatedItem = { ...item };
            let processedValue = value;
            if (fieldType === 'number') processedValue = parseFloat(value) || 0;
            if (fieldType === 'boolean') processedValue = value;

            if (field in updatedItem) {
                (updatedItem as any)[field] = processedValue;
            } else {
                updatedItem.custom_fields = { ...updatedItem.custom_fields, [field]: processedValue };
            }
            return updatedItem;
        }
        return item;
    }));
    setIsDirty(true);
  };
  
  const handleSave = () => {
    handleUpdateCatalog(editedItems);
    setIsDirty(false);
  };

  const handleDiscard = () => {
    setEditedItems(JSON.parse(JSON.stringify(catalog)));
    setIsDirty(false);
  };
  
  const handleDeleteItem = (itemId: number) => {
    if (window.confirm('Are you sure you want to delete this item? This will also remove it from all workshop inventories. This action is saved only when you click "Save Changes".')) {
        setEditedItems(currentItems => currentItems.filter(item => item.id !== itemId));
        setIsDirty(true);
    }
  };
  
  const handleSaveNewItem = (itemData: Omit<CatalogItem, 'id' | 'custom_fields'>) => {
    handleAddItemToCatalog(itemData);
    setIsAddItemModalOpen(false);
  };
  
  const SortableHeader: React.FC<{ sortKey: string, children: React.ReactNode, className?: string}> = ({ sortKey, children, className }) => (
    <th scope="col" className={`px-4 py-3 font-semibold tracking-wider ${className}`}>
        <button onClick={() => requestSort(sortKey)} className="flex items-center gap-1.5 w-full text-left transition-colors hover:text-white">
            {children}
            <ChevronsUpDownIcon className={`w-4 h-4 text-slate-500 ${sortConfig?.key === sortKey ? 'text-white' : ''}`} />
        </button>
    </th>
  );
  
  const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md py-2 px-3 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
  
  const renderCell = (item: CatalogItem, field: keyof CatalogItem | CatalogCustomField) => {
      const fieldId = typeof field === 'string' ? field : field.id;
      const fieldType = typeof field === 'string' ? 'text' : field.type;
      
      let value;
      if (typeof field === 'string' && field in item) {
          value = item[field as keyof CatalogItem];
      } else {
          value = item.custom_fields[fieldId] ?? '';
      }

      if (fieldType === 'boolean') {
        return (
          <input
            type="checkbox"
            checked={!!value}
            onChange={(e) => handleInputChange(item.id, fieldId, e.target.checked, 'boolean')}
            className="h-4 w-4 rounded border-slate-500 bg-slate-700 text-brand-accent focus:ring-brand-accent mx-auto block"
          />
        )
      }

      return (
        <input 
            type={fieldType === 'number' ? 'number' : 'text'}
            value={value}
            onChange={(e) => handleInputChange(item.id, fieldId, e.target.value, fieldType as any)}
            className="w-full bg-transparent p-2 -m-2 rounded-md focus:bg-slate-700 focus:ring-1 focus:ring-brand-accent outline-none transition-all duration-200"
        />
      );
  }

  return (
    <>
    <div className="flex flex-col h-full">
      <div className="[background-image:var(--card-bg-data)] backdrop-blur-md rounded-lg shadow-lg border border-slate-800 overflow-hidden animate-fade-in flex-grow flex flex-col">
        <div className="p-4 sm:p-6 flex flex-col md:flex-row items-start md:items-center justify-between border-b border-slate-700 gap-4">
          <div className="flex items-center">
              <BoxIcon className="w-8 h-8 mr-3 text-brand-accent" />
              <div>
                <h2 className="text-3xl font-bold text-slate-100">Item Catalog Engine</h2>
                <p className="text-slate-400">View, search, filter, and manage all items.</p>
              </div>
          </div>
          <button
              onClick={() => setIsAddItemModalOpen(true)}
              className="flex items-center px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              Add New Item
            </button>
        </div>
        
        {/* Filter Bar */}
        <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-4 border-b border-slate-700">
            <input type="search" name="search" placeholder="Search by name or SKU..." value={filters.search} onChange={handleFilterChange} className={inputClasses} />
            <select name="category" value={filters.category} onChange={handleFilterChange} className={inputClasses}>
                {uniqueCategories.map(cat => <option key={cat} value={cat}>{cat === 'all' ? 'All Categories' : cat}</option>)}
            </select>
            <select name="restricted" value={filters.restricted} onChange={handleFilterChange} className={inputClasses}>
                <option value="all">All Restricted Status</option>
                <option value="true">Restricted Only</option>
                <option value="false">Not Restricted</option>
            </select>
        </div>

        <div className="overflow-auto flex-grow">
          <table className="w-full text-sm text-left text-slate-300">
            <thead className="text-xs text-slate-400 uppercase bg-slate-800 sticky top-0 z-10">
              <tr>
                <SortableHeader sortKey="name">Name</SortableHeader>
                <SortableHeader sortKey="sku">SKU</SortableHeader>
                <SortableHeader sortKey="category">Category</SortableHeader>
                <SortableHeader sortKey="default_unit_price" className="text-right">Price</SortableHeader>
                <SortableHeader sortKey="is_restricted" className="text-center">Restricted</SortableHeader>
                {catalogCustomFields.map(field => (
                    <SortableHeader key={field.id} sortKey={field.id}>{field.name}</SortableHeader>
                ))}
                <th scope="col" className="px-4 py-3 font-semibold tracking-wider text-right">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-slate-800/50">
              {paginatedItems.map((item, index) => (
                  <tr key={item.id} className={`border-b border-slate-700 last:border-b-0 ${index % 2 === 0 ? 'bg-slate-800/50' : 'bg-slate-900/30'} hover:bg-slate-700/40 transition-colors`}>
                    <td className="px-4 py-2 w-1/5">{renderCell(item, 'name')}</td>
                    <td className="px-4 py-2 w-1/6">{renderCell(item, 'sku')}</td>
                    <td className="px-4 py-2 w-1/6">{renderCell(item, 'category')}</td>
                    <td className="px-4 py-2 text-right w-[100px]">{renderCell(item, 'default_unit_price')}</td>
                    <td className="px-4 py-2 text-center w-[100px]">{renderCell(item, 'is_restricted')}</td>
                    {catalogCustomFields.map(field => (
                         <td key={field.id} className="px-4 py-2">{renderCell(item, field)}</td>
                    ))}
                    <td className="px-4 py-2 text-right">
                        <button onClick={() => handleDeleteItem(item.id)} className="p-2 text-red-500 hover:text-red-400 hover:bg-red-500/20 rounded-full transition-colors" aria-label={`Delete ${item.name}`}>
                            <Trash2Icon className="w-4 h-4"/>
                        </button>
                    </td>
                  </tr>
              ))}
            </tbody>
          </table>
          {filteredAndSortedItems.length === 0 && (
            <p className="text-slate-400 text-center py-8">
              No items match your criteria.
            </p>
          )}
        </div>
         {/* Pagination */}
        <div className="flex justify-between items-center p-4 border-t border-slate-700 flex-shrink-0">
          <p className="text-sm text-slate-400">Page {currentPage} of {totalPages} ({filteredAndSortedItems.length} items total)</p>
          <div className="flex gap-2">
            <button onClick={() => setCurrentPage(p => p - 1)} disabled={currentPage === 1} className="px-3 py-1 bg-slate-600 text-white font-semibold rounded-md disabled:opacity-50 transition hover:bg-slate-500">Previous</button>
            <button onClick={() => setCurrentPage(p => p + 1)} disabled={currentPage >= totalPages} className="px-3 py-1 bg-slate-600 text-white font-semibold rounded-md disabled:opacity-50 transition hover:bg-slate-500">Next</button>
          </div>
        </div>
      </div>
       {isDirty && (
            <div className="sticky bottom-0 mt-4 p-4 bg-slate-700 rounded-lg shadow-lg border border-slate-600 flex justify-between items-center animate-fade-in z-20">
                <p className="text-slate-200 font-semibold">You have unsaved changes.</p>
                <div className="flex gap-4">
                    <button onClick={handleDiscard} className="px-4 py-2 bg-slate-500 text-white font-semibold rounded-md shadow-md hover:bg-slate-400 transition flex items-center gap-2">
                        <XCircleIcon className="w-5 h-5"/> Discard
                    </button>
                    <button onClick={handleSave} className="flex items-center px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                        <SaveIcon className="w-5 h-5 mr-2" />
                        Save Changes
                    </button>
                </div>
            </div>
        )}
    </div>
    <AddItemToCatalogModal 
        isOpen={isAddItemModalOpen}
        onClose={() => setIsAddItemModalOpen(false)}
        onSave={handleSaveNewItem}
    />
    </>
  );
};

export default InventoryCatalogPage;