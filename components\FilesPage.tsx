import React, { useMemo, useState } from 'react';
import { StagedImport, StagedImportRow } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { FileIcon, BoxIcon, UsersIcon, WrenchIcon, CheckCircleIcon, XCircleIcon, EditIcon } from './icons';
import EditStagedDataModal from './EditStagedDataModal';

interface FilesPageProps {
  onProcessStagedImport: (importId: number) => Promise<void>;
}

const FilesPage: React.FC<FilesPageProps> = ({ onProcessStagedImport }) => {
  const { appState } = useAppState();
  const { handleDiscardStagedImport, handleUpdateStagedImport } = useAppActions();
  
  const { stagedImports, users } = appState!;
  
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [dataToEdit, setDataToEdit] = useState<StagedImport | null>(null);

  const userMap = useMemo(() => new Map(users.map(u => [u.id, u.name])), [users]);

  const sortedImports = useMemo(() => {
    return [...stagedImports].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }, [stagedImports]);
  
  const handleEditImport = (importId: number) => {
    const importToEdit = stagedImports.find(imp => imp.id === importId);
    if (importToEdit) {
      setDataToEdit(importToEdit);
      setIsEditModalOpen(true);
    }
  };
  
  const handleUpdateAndClose = async (importId: number, updatedRows: StagedImportRow[]) => {
      await handleUpdateStagedImport(importId, updatedRows);
      setIsEditModalOpen(false);
      setDataToEdit(null);
  };

  const getIconForType = (type: StagedImport['importType']) => {
    switch(type) {
      case 'Inventory': return <BoxIcon className="w-8 h-8 text-green-400" />;
      case 'Users': return <UsersIcon className="w-8 h-8 text-sky-400" />;
      case 'Workshop Stock': return <WrenchIcon className="w-8 h-8 text-amber-400" />;
      default: return <FileIcon className="w-8 h-8 text-slate-400" />;
    }
  };

  return (
    <>
      <div role="tabpanel" className="animate-fade-in">
        <div className="flex items-center mb-6">
          <FileIcon className="w-6 h-6 mr-3 text-brand-accent" />
          <h3 className="text-xl font-bold text-slate-200">File Cabinet</h3>
        </div>
        <p className="text-slate-400 mt-2 mb-6">
          A history of all data imports. You can process, edit, or discard staged files from here.
        </p>
        
        {sortedImports.length === 0 ? (
          <p className="text-center py-16 text-slate-400">No import history yet. Use the Data Tools tab to get started.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {sortedImports.map(imp => {
                const isStaged = imp.status === 'staged';
                return (
                <div key={imp.id} className="bg-slate-900/50 p-4 rounded-lg border border-slate-700 flex flex-col justify-between">
                    <div>
                        <div className="flex items-start justify-between">
                            {getIconForType(imp.importType)}
                            <span className={`px-2 py-0.5 text-xs font-semibold rounded-full ${
                                imp.status === 'processed' ? 'bg-green-900/50 text-green-300' :
                                imp.status === 'discarded' ? 'bg-slate-600 text-slate-300' :
                                'bg-yellow-900/50 text-yellow-300'
                            }`}>{imp.status}</span>
                        </div>
                        <p className="font-semibold text-slate-200 mt-3 truncate" title={imp.fileName || 'Staged Data'}>{imp.fileName || 'Staged Data'}</p>
                        <p className="text-sm text-slate-400">{imp.importType}</p>
                        <p className="text-xs text-slate-500 mt-2">By {userMap.get(imp.user_id) || 'Unknown'} on {new Date(imp.created_at).toLocaleDateString()}</p>
                    </div>
                    <div className="mt-4 pt-4 border-t border-slate-700/50 text-center">
                        <p className="text-sm"><span className="font-bold text-green-400">{imp.summary.valid}</span> valid / <span className="font-bold text-red-400">{imp.summary.invalid}</span> invalid</p>
                        {isStaged && (
                            <>
                                <div className="flex justify-center items-center gap-2 mt-3">
                                    <button onClick={() => onProcessStagedImport(imp.id)} disabled={imp.summary.valid === 0} className="flex-1 px-3 py-1.5 text-xs font-semibold text-white bg-green-600 hover:bg-green-500 rounded-md transition disabled:bg-slate-500 disabled:cursor-not-allowed">Process</button>
                                    <button onClick={() => handleEditImport(imp.id)} className="p-1.5 text-brand-accent hover:bg-amber-500/20 rounded-md transition" aria-label="Edit Staged Data">
                                        <EditIcon className="w-4 h-4"/>
                                    </button>
                                    <button onClick={() => handleDiscardStagedImport(imp.id)} className="p-1.5 text-red-400 hover:bg-red-500/20 rounded-md transition" aria-label="Discard Staged Data">
                                        <XCircleIcon className="w-4 h-4"/>
                                    </button>
                                </div>
                                {imp.summary.valid === 0 && imp.summary.total > 0 && (
                                    <p className="text-xs text-amber-400 mt-2">Cannot process a file with no valid rows.</p>
                                )}
                            </>
                        )}
                    </div>
                </div>
            )})}
          </div>
        )}
      </div>

      <EditStagedDataModal 
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        stagedImport={dataToEdit}
        onUpdateStagedImport={handleUpdateAndClose}
      />
    </>
  );
};

export default FilesPage;