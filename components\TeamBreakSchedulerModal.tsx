import React, { useState, useEffect } from 'react';
import { BreakSchedule } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { XIcon, SaveIcon, CalendarClockIcon } from './icons';

interface TeamBreakSchedulerModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const breakSlots: Array<{ key: keyof BreakSchedule, label: string }> = [
    { key: 'morning', label: 'Morning Break' },
    { key: 'lunch', label: 'Lunch' },
    { key: 'afternoon', label: 'Afternoon Break' }
];

export const TeamBreakSchedulerModal: React.FC<TeamBreakSchedulerModalProps> = ({ isOpen, onClose }) => {
    const { appState } = useAppState();
    const { handleUpdateBreakSchedule } = useAppActions();
    
    // Guard against rendering if state is not ready, which can happen during startup or after a data restore.
    if (!isOpen || !appState) {
        return null;
    }

    const [selectedWorkshopId, setSelectedWorkshopId] = useState<number>(0);
    const [currentSchedule, setCurrentSchedule] = useState<BreakSchedule>({});

    useEffect(() => {
        if (isOpen && appState?.currentUser) {
            setSelectedWorkshopId(appState.currentUser.primary_workshop_id);
        }
    }, [isOpen, appState?.currentUser?.primary_workshop_id]);

    useEffect(() => {
        if (selectedWorkshopId && appState?.breakSchedules) {
            setCurrentSchedule(appState.breakSchedules[selectedWorkshopId] || {});
        }
    }, [selectedWorkshopId, appState?.breakSchedules]);


    const { currentUser, workshops } = appState;

    const handleTimeChange = (slotKey: keyof BreakSchedule, type: 'startTime' | 'endTime', value: string) => {
        setCurrentSchedule(prev => ({
            ...prev,
            [slotKey]: {
                ...(prev[slotKey] || { startTime: '', endTime: '' }),
                [type]: value
            }
        }));
    };

    const handleSave = async () => {
        await handleUpdateBreakSchedule(selectedWorkshopId, currentSchedule);
        onClose();
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            onClick={onClose}
        >
            <div 
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-lg relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100 flex items-center">
                        <CalendarClockIcon className="w-6 h-6 mr-3 text-brand-accent"/>
                        Team Break Scheduler
                    </h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>
                
                <div className="p-6 space-y-6">
                    <div>
                        <label htmlFor="workshop-select" className={labelClasses}>Select Workshop</label>
                        <select
                            id="workshop-select"
                            value={selectedWorkshopId}
                            onChange={(e) => setSelectedWorkshopId(Number(e.target.value))}
                            className={inputClasses}
                            disabled={currentUser.role !== 'Admin'}
                        >
                            {workshops.filter(w => w.type === 'Local').map(w => (
                                <option key={w.id} value={w.id}>{w.name}</option>
                            ))}
                        </select>
                         {currentUser.role !== 'Admin' && <p className="text-xs text-slate-400 mt-1">You can only manage the schedule for your assigned workshop.</p>}
                    </div>
                    
                    <div className="space-y-4">
                        {breakSlots.map(({ key, label }) => (
                            <div key={key} className="p-4 bg-slate-900/40 rounded-lg border border-slate-700/50">
                                <h4 className="font-semibold text-slate-200 mb-3">{label}</h4>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label htmlFor={`${key}-start`} className={labelClasses}>Start Time</label>
                                        <input
                                            type="time"
                                            id={`${key}-start`}
                                            value={currentSchedule[key]?.startTime || ''}
                                            onChange={(e) => handleTimeChange(key, 'startTime', e.target.value)}
                                            className={inputClasses}
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor={`${key}-end`} className={labelClasses}>End Time</label>
                                        <input
                                            type="time"
                                            id={`${key}-end`}
                                            value={currentSchedule[key]?.endTime || ''}
                                            onChange={(e) => handleTimeChange(key, 'endTime', e.target.value)}
                                            className={inputClasses}
                                        />
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
                
                <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                    <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-600 text-white font-semibold rounded-md shadow-md hover:bg-slate-500 transition">
                        Cancel
                    </button>
                    <button 
                        type="button" 
                        onClick={handleSave} 
                        className="flex items-center gap-2 px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition"
                    >
                        <SaveIcon className="w-5 h-5" />
                        Save Schedule
                    </button>
                </div>
            </div>
        </div>
    );
};