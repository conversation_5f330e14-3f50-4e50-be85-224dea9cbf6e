import React, { useState, FormEvent, useEffect } from 'react';
import { SubProgram } from '../types';
import { XIcon } from './icons';
import { useAppState } from '../context/AppContext';

interface CreateDepartmentModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (subProgramData: Omit<SubProgram, 'id'>) => void;
    parentProgramId?: number | null;
}

const CreateDepartmentModal: React.FC<CreateDepartmentModalProps> = ({ isOpen, onClose, onSave, parentProgramId }) => {
    const { appState } = useAppState();
    const { programs } = appState!;

    const getInitialState = () => ({
        name: '',
        program_id: parentProgramId ?? (programs.length > 0 ? programs[0].id : -1),
    });

    const [newDepartment, setNewDepartment] = useState(getInitialState());
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (isOpen) {
            setNewDepartment(getInitialState());
            setError(null);
        }
    }, [isOpen, programs, parentProgramId]);

    if (!isOpen) return null;

    const validate = () => {
        if (!newDepartment.name.trim()) {
            setError('Department name is required.');
            return false;
        }
        if (newDepartment.program_id === -1) {
            setError('A program must be selected.');
            return false;
        }
        setError(null);
        return true;
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (validate()) {
            onSave({
                name: newDepartment.name.trim(),
                program_id: newDepartment.program_id,
            });
            onClose();
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const processedValue = name === 'program_id' ? parseInt(value, 10) : value;
        setNewDepartment(prev => ({ ...prev, [name]: processedValue }));
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-md relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Create New Department</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4">
                        <div>
                            <label htmlFor="name" className={labelClasses}>Department Name</label>
                            <input type="text" id="name" name="name" value={newDepartment.name} onChange={handleChange} className={inputClasses} required />
                        </div>

                        <div>
                            <label htmlFor="program_id" className={labelClasses}>Parent Program</label>
                            <select id="program_id" name="program_id" value={newDepartment.program_id} onChange={handleChange} className={inputClasses} disabled={parentProgramId != null}>
                                {programs.length > 0 ? (
                                    programs.map(p => <option key={p.id} value={p.id}>{p.name}</option>)
                                ) : (
                                    <option>No programs available</option>
                                )}
                            </select>
                        </div>
                        {error && <p className="text-status-error text-xs mt-1">{error}</p>}
                    </div>

                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">Cancel</button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Create Department</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default CreateDepartmentModal;
