import React, { useState, useCallback } from 'react';
import { SettingsIcon, UsersIcon, SaveIcon, WrenchIcon, BoxIcon, DatabaseIcon, BellIcon, FileIcon, XIcon, CheckCircleIcon, InfoIcon, LayoutDashboardIcon, ArchiveIcon } from './icons';
import { AppState, CatalogItem, StagedImport, UserType, Workshop, Page, SubProgram, Program } from '../types';
import UserManagementPage from './UserManagementPage';
import BackupRestorePage from './BackupRestorePage';
import OperationsManagementPage from './OperationsManagementPage';
import CatalogManagementPage from './CatalogManagementPage';
import AddUserModal from './AddUserModal';
import CreateRackXModal from './CreateRackXModal';
import DataToolsPage from './DataToolsPage';
import ActivityLogTablePage from './ActivityLogTablePage';
import FilesPage from './FilesPage';
import { useAppActions, useAppState } from '../context/AppContext';
import AboutPage from './AboutPage';
import AdminEmptyState from './AdminEmptyState';
import CreateDepartmentModal from './CreateDepartmentModal';
import CreateProgramModal from './CreateProgramModal';
import AddItemToCatalogModal from './AddItemToCatalogModal';
import CreateTeamModal from './CreateTeamModal';
import DashboardSettings from './DashboardSettings';

interface AdminConsolePageProps {
  setActivePage: (page: Page) => void;
}

type AdminTab = 'users' | 'operations' | 'catalog' | 'data-tools' | 'files' | 'activity' | 'backup' | 'about' | 'dashboard';

const AdminConsolePage: React.FC<AdminConsolePageProps> = ({ setActivePage }) => {
  const { appState } = useAppState();
  const actions = useAppActions();
  
  const [activeTab, setActiveTab] = useState<AdminTab>('users');
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isCreateWorkshopModalOpen, setIsCreateWorkshopModalOpen] = useState(false);
  const [isCreateSubProgramModalOpen, setIsCreateSubProgramModalOpen] = useState(false);
  const [isCreateProgramModalOpen, setIsCreateProgramModalOpen] = useState(false);
  const [isAddItemToCatalogModalOpen, setIsAddItemToCatalogModalOpen] = useState(false);
  const [isCreateTeamModalOpen, setIsCreateTeamModalOpen] = useState(false);
  const [feedback, setFeedback] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  
  const handleAddNewUser = async (userData: Omit<UserType, 'id' | 'avatarUrl' | 'reelImageUrl'>) => {
    await actions.handleCreateUser(userData);
    setIsAddUserModalOpen(false);
  };

  const handleCreateWorkshop = async (workshopData: Omit<Workshop, 'id'>) => {
    await actions.handleCreateWorkshop(workshopData);
    setIsCreateWorkshopModalOpen(false);
  };

  const handleCreateSubProgram = async (subProgramData: Omit<SubProgram, 'id'>) => {
    await actions.handleCreateSubProgram(subProgramData);
    setIsCreateSubProgramModalOpen(false);
  };

  const handleCreateProgram = async (programData: Omit<Program, 'id'>) => {
    await actions.handleCreateProgram(programData);
    setIsCreateProgramModalOpen(false);
  };

  const handleAddItemToCatalog = async (itemData: Omit<CatalogItem, 'id' | 'custom_fields'>) => {
    await actions.handleAddItemToCatalog(itemData);
    setIsAddItemToCatalogModalOpen(false);
  };

  const handleCreateTeam = async (teamData: { workshopId: number; teamLeaderId: number; memberIds: number[] }) => {
    await actions.handleCreateTeam(teamData);
    setIsCreateTeamModalOpen(false);
  };

  const handleDeleteUser = async (userId: number) => {
    await actions.handleDeleteUser(userId);
  };

  const handleProcessImportWithFeedback = useCallback(async (importId: number) => {
    setFeedback(null);
    const importInfo = appState!.stagedImports.find(imp => imp.id === importId);
    if (!importInfo) {
      setFeedback({ message: 'Could not find the import job to process.', type: 'error' });
      return;
    }
    
    await actions.handleProcessStagedImport(importId);
    
    setFeedback({
      message: `Successfully processed file "${importInfo.fileName}". ${importInfo.summary.valid} rows were imported.`,
      type: 'success'
    });
    
    setTimeout(() => setFeedback(null), 6000);
  }, [appState, actions.handleProcessStagedImport]);

  const TabButton: React.FC<{ tabId: AdminTab; label: string, icon: React.ReactNode }> = ({ tabId, label, icon }) => {
    const isActive = activeTab === tabId;
    return (
      <button
        onClick={() => setActiveTab(tabId)}
        className={`flex items-center px-3 py-2 text-sm font-semibold rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 focus:ring-brand-accent ${
          isActive
            ? 'bg-brand-accent text-brand-text-on-accent'
            : 'text-slate-300 hover:bg-slate-700'
        }`}
        role="tab" aria-selected={isActive}
      >
        {icon}
        <span className="ml-2 hidden sm:inline">{label}</span>
      </button>
    );
  };

  const renderContent = () => {
    if (!appState) return null;
    
    switch (activeTab) {
      case 'users':
        return <UserManagementPage 
                  onOpenAddUserModal={() => setIsAddUserModalOpen(true)}
                  onDeleteUser={handleDeleteUser}
                />;
      case 'operations':
        return <OperationsManagementPage />;
      case 'catalog':
        return <CatalogManagementPage onOpenAddItemModal={() => setIsAddItemToCatalogModalOpen(true)} />;
      case 'data-tools':
        return <DataToolsPage />
      case 'activity':
        return <ActivityLogTablePage />;
      case 'files':
        return <FilesPage 
                  onProcessStagedImport={handleProcessImportWithFeedback}
               />;
      case 'backup':
        return <BackupRestorePage />;
      case 'about':
        return <AboutPage />;
      case 'dashboard':
        return <DashboardSettings />;
      default:
        return null;
    }
  };

  const isAdminStateEmpty = !appState || (appState.programs.length === 0 && appState.workshops.length === 0 && appState.users.length <= 1);

  return (
    <>
    <div className="flex flex-col h-full">
        <div className="[background-image:var(--card-bg-panel)] backdrop-blur-md rounded-lg shadow-lg border border-slate-800 overflow-hidden animate-fade-in flex flex-col flex-grow">
            <div className="p-4 sm:p-6 border-b border-slate-700/50 flex-shrink-0">
                <div className="flex items-center">
                    <SettingsIcon className="w-8 h-8 mr-3 text-brand-accent" />
                    <h2 className="text-3xl font-bold text-slate-100">Admin Panel</h2>
                </div>
            </div>
        
            {feedback && (
              <div className={`p-4 mx-4 mt-4 rounded-lg border ${feedback.type === 'success' ? 'bg-status-success-bg text-status-success border-status-success-border' : 'bg-status-error-bg text-status-error border-status-error-border'} flex items-center justify-between animate-fade-in flex-shrink-0`} role="alert">
                  <div className="flex items-center">
                      <CheckCircleIcon className="w-5 h-5 mr-3" />
                      <p>{feedback.message}</p>
                  </div>
                  <button onClick={() => setFeedback(null)} className="p-1 rounded-full hover:bg-white/10 transition-colors" aria-label="Dismiss">
                    <XIcon className="w-5 h-5"/>
                  </button>
              </div>
            )}

            {isAdminStateEmpty ? (
                <AdminEmptyState
                    onAddDepartment={() => setActiveTab('operations')}
                    onAddParent={() => setIsCreateProgramModalOpen(true)}
                    onAddRack={() => setActiveTab('operations')}
                    onAddUser={() => setIsAddUserModalOpen(true)}
                    onAddInventory={() => setIsAddItemToCatalogModalOpen(true)}
                    onAddTeam={() => setIsCreateTeamModalOpen(true)}
                />
            ) : (
                <>
                    <nav className="flex items-center space-x-1 sm:space-x-2 border-b border-slate-700/50 p-4 overflow-x-auto flex-shrink-0" role="tablist" aria-orientation="horizontal">
                        <TabButton tabId="users" label="Users" icon={<UsersIcon className="w-5 h-5"/>} />
                        <TabButton tabId="operations" label="Operations" icon={<ArchiveIcon className="w-5 h-5"/>} />
                        <TabButton tabId="catalog" label="Catalog" icon={<BoxIcon className="w-5 h-5"/>} />
                        <TabButton tabId="dashboard" label="Dashboard" icon={<LayoutDashboardIcon className="w-5 h-5"/>} />
                        <TabButton tabId="data-tools" label="Data Tools" icon={<DatabaseIcon className="w-5 h-5"/>} />
                        <TabButton tabId="files" label="Files" icon={<FileIcon className="w-5 h-5"/>} />
                        <TabButton tabId="activity" label="Activity Log" icon={<BellIcon className="w-5 h-5"/>} />
                        <TabButton tabId="backup" label="Backup" icon={<SaveIcon className="w-5 h-5"/>} />
                        <TabButton tabId="about" label="About" icon={<InfoIcon className="w-5 h-5"/>} />
                    </nav>
                    <div className="overflow-y-auto flex-grow p-4 md:p-6">
                        {renderContent()}
                    </div>
                </>
            )}
        </div>
    </div>
    <AddUserModal
        isOpen={isAddUserModalOpen}
        onClose={() => setIsAddUserModalOpen(false)}
        onSave={handleAddNewUser}
        workshops={appState!.workshops}
        currentUser={appState!.currentUser}
    />
    <CreateRackXModal
        isOpen={isCreateWorkshopModalOpen}
        onClose={() => setIsCreateWorkshopModalOpen(false)}
        onSave={handleCreateWorkshop}
    />
    <CreateDepartmentModal
        isOpen={isCreateSubProgramModalOpen}
        onClose={() => setIsCreateSubProgramModalOpen(false)}
        onSave={handleCreateSubProgram}
    />
    <CreateProgramModal
        isOpen={isCreateProgramModalOpen}
        onClose={() => setIsCreateProgramModalOpen(false)}
        onSave={handleCreateProgram}
    />
    <AddItemToCatalogModal
        isOpen={isAddItemToCatalogModalOpen}
        onClose={() => setIsAddItemToCatalogModalOpen(false)}
        onSave={handleAddItemToCatalog}
    />
    <CreateTeamModal
        isOpen={isCreateTeamModalOpen}
        onClose={() => setIsCreateTeamModalOpen(false)}
        onSave={handleCreateTeam}
    />
    </>
  );
};

export default AdminConsolePage;