import React, { useState, useRef, useEffect } from 'react';
import { BellIcon, CheckCircleIcon } from './icons';
import { AppState } from '../types';
import { useNotifications, UINotification } from '../hooks/useNotifications';
import { useTranslation } from '../i18n/I18nProvider';

interface NotificationsProps {
    appState: AppState;
}

const Notifications: React.FC<NotificationsProps> = ({ appState }) => {
    const [isOpen, setIsOpen] = useState(false);
    const { notifications, unreadCount, markAllAsRead } = useNotifications(appState);
    const { t } = useTranslation();
    const menuRef = useRef<HTMLDivElement>(null);

    const renderNotificationMessage = (notification: UINotification): React.ReactNode => {
        const { type, payload } = notification;
        switch (type) {
            case 'REQUEST_STATUS_CHANGED':
                const actionText = payload.action === 'approved' ? t('notifications.requestStatusApproved') : t('notifications.requestStatusRejected');
                const requestSummary = t(payload.requestSummaryKey as any, payload.summaryOptions);
                return t('notifications.requestStatusChanged', { actorName: payload.actorName, action: actionText, requestSummary });
            case 'USER_PROFILE_UPDATED':
                return t('notifications.profileUpdated', { actorName: payload.actorName });
            case 'USER_ASSIGNED':
                 return t('notifications.userAssigned', { actorName: payload.actorName, assignedUserName: payload.assignedUserName });
            default:
                return `An unknown action occurred.`;
        }
    }

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const toggleOpen = () => setIsOpen(prev => !prev);
    
    return (
        <div className="relative" ref={menuRef}>
            <button
                onClick={toggleOpen}
                className="relative p-2 rounded-full text-slate-400 hover:bg-slate-800 hover:text-white transition-colors"
                aria-label={t('header.notifications')}
            >
                <BellIcon className="w-6 h-6" />
                {unreadCount > 0 && (
                    <span className="absolute top-1 end-1 flex h-4 w-4">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-4 w-4 bg-amber-500 text-white text-xs items-center justify-center font-bold">
                            {unreadCount > 9 ? '9+' : unreadCount}
                        </span>
                    </span>
                )}
            </button>

            {isOpen && (
                <div className="absolute end-0 mt-2 w-80 sm:w-96 bg-slate-800 border border-slate-700 rounded-lg shadow-xl z-20 animate-fade-in" role="menu">
                    <div className="flex justify-between items-center p-3 border-b border-slate-700">
                        <h3 className="font-bold text-slate-100">{t('notifications.title')}</h3>
                        {notifications.length > 0 && unreadCount > 0 && (
                             <button
                                onClick={markAllAsRead}
                                className="text-xs font-semibold text-brand-accent hover:text-indigo-400"
                            >
                                {t('notifications.markAllRead')}
                            </button>
                        )}
                    </div>
                     <ul className="py-1 max-h-96 overflow-y-auto">
                        {notifications.length === 0 ? (
                            <li className="px-3 py-4 text-center text-sm text-slate-400">{t('notifications.noNotifications')}</li>
                        ) : (
                            notifications.map(notif => (
                                <li 
                                    key={notif.id}
                                    className={`px-3 py-3 border-s-4 ${notif.isRead ? 'border-transparent' : 'border-brand-accent bg-slate-900/30'}`}
                                >
                                    <div className="text-sm text-slate-300">
                                       {renderNotificationMessage(notif)}
                                    </div>
                                    <p className="text-xs text-slate-500 mt-1">
                                        {new Date(notif.createdAt).toLocaleString()}
                                    </p>
                                </li>
                            ))
                        )}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default Notifications;