import React, { useState, useEffect, FormEvent } from 'react';
import { SubProgram, Program } from '../types';
import { XIcon } from './icons';

interface EditDepartmentModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (subProgram: SubProgram) => void;
    subProgram: SubProgram | null;
    programs: Program[];
}

const EditDepartmentModal: React.FC<EditDepartmentModalProps> = ({ isOpen, onClose, onSave, subProgram, programs }) => {
    const [editedDepartment, setEditedDepartment] = useState<SubProgram | null>(subProgram);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        setEditedDepartment(subProgram);
        if (isOpen) {
            setError(null);
        }
    }, [isOpen, subProgram]);

    if (!isOpen || !editedDepartment) return null;

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (!editedDepartment.name.trim()) {
            setError('Department name is required.');
            return;
        }
        onSave(editedDepartment);
        onClose();
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const processedValue = name === 'program_id' ? parseInt(value, 10) : value;
        setEditedDepartment(prev => prev ? { ...prev, [name]: processedValue } : null);
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-md relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Edit Department</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4">
                        <div>
                            <label htmlFor="name" className={labelClasses}>Department Name</label>
                            <input type="text" id="name" name="name" value={editedDepartment.name} onChange={handleChange} className={inputClasses} required />
                        </div>

                        <div>
                            <label htmlFor="program_id" className={labelClasses}>Parent Program</label>
                            <select id="program_id" name="program_id" value={editedDepartment.program_id} onChange={handleChange} className={inputClasses}>
                                {programs.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
                            </select>
                        </div>
                        {error && <p className="text-status-error text-xs mt-1">{error}</p>}
                    </div>

                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">Cancel</button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditDepartmentModal;
