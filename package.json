{"name": "rackx", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "latest", "@playwright/test": "^1.55.0", "date-fns": "^3.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "latest", "react-router-dom": "^6.22.0", "xlsx": "latest"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.8.2", "vite": "^6.2.0"}}