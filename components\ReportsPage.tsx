import React, { useMemo } from 'react';
import { TopConsumedItem, MostActiveUser } from '../types';
import { useAppState } from '../context/AppContext';
import { BarChartIcon, TrendingUpIcon, UsersIcon } from './icons';
import { useTranslation } from '../i18n/I18nProvider';

const ReportsPage: React.FC = () => {
    const { appState } = useAppState();
    const { t } = useTranslation();
    const { requests, catalog, auditLog, users } = appState!;

    const topConsumedItems = useMemo((): TopConsumedItem[] => {
        const consumptionMap = new Map<number, number>();

        requests
            .filter(req => req.type === 'CONSUMPTION' && req.status === 'Approved')
            .forEach(req => {
                req.items.forEach(item => {
                    consumptionMap.set(item.item_id, (consumptionMap.get(item.item_id) || 0) + item.quantity);
                });
            });

        const sortedItems = Array.from(consumptionMap.entries())
            .sort(([, qtyA], [, qtyB]) => qtyB - qtyA)
            .slice(0, 5);
        
        const catalogMap = new Map(catalog.map(c => [c.id, c]));

        return sortedItems.map(([itemId, quantity]) => {
            const itemDetails = catalogMap.get(itemId);
            return {
                id: itemId,
                name: itemDetails?.name || 'Unknown Item',
                sku: itemDetails?.sku || 'N/A',
                quantity: quantity
            };
        });

    }, [requests, catalog]);

    const mostActiveUsers = useMemo((): MostActiveUser[] => {
        const activityMap = new Map<number, number>();

        auditLog.forEach(log => {
            activityMap.set(log.user_id, (activityMap.get(log.user_id) || 0) + 1);
        });

        const sortedUsers = Array.from(activityMap.entries())
            .sort(([, countA], [, countB]) => countB - countA)
            .slice(0, 5);

        const userMap = new Map(users.map(u => [u.id, u.name]));

        return sortedUsers.map(([userId, actionCount]) => ({
            id: userId,
            name: userMap.get(userId) || 'Unknown User',
            actionCount: actionCount,
        }));
    }, [auditLog, users]);
    
    const StatDisplayCard: React.FC<{title: string, icon: React.ReactNode, children?: React.ReactNode, isEmpty: boolean, emptyMessage: string}> = 
        ({ title, icon, children, isEmpty, emptyMessage }) => (
        <div className="[background-image:var(--card-bg-reports)] backdrop-blur-md p-6 rounded-lg border border-slate-800 shadow-card">
            <h3 className="text-lg font-semibold text-slate-200 flex items-center mb-4">
                {icon}
                <span className="ms-2">{title}</span>
            </h3>
            {isEmpty ? (
                <div className="text-center py-8 text-slate-500">{emptyMessage}</div>
            ) : (
                <ul className="space-y-3">
                    {children}
                </ul>
            )}
        </div>
    );
    
    return (
        <div className="flex flex-col h-full">
            <div className="[background-image:var(--card-bg-reports)] backdrop-blur-md rounded-lg shadow-lg border border-slate-800 overflow-hidden animate-fade-in flex flex-col flex-grow">
                <div className="p-4 sm:p-6 border-b border-slate-700/50 flex-shrink-0">
                    <div className="flex items-center">
                        <BarChartIcon className="w-8 h-8 me-3 text-brand-accent" />
                        <div>
                            <h2 className="text-3xl font-bold text-slate-100">{t('reports.title')}</h2>
                            <p className="text-slate-400">{t('reports.description')}</p>
                        </div>
                    </div>
                </div>
                <div className="overflow-y-auto flex-grow p-4 sm:p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        <StatDisplayCard 
                            title={t('reports.topConsumedTitle')}
                            icon={<BarChartIcon className="w-5 h-5 text-brand-accent" />}
                            isEmpty={topConsumedItems.length === 0}
                            emptyMessage={t('reports.noConsumptionData')}
                        >
                            {topConsumedItems.map((item, index) => (
                                <li key={item.id} className="flex justify-between items-center bg-slate-800 p-3 rounded-md">
                                    <div>
                                        <p className="font-semibold text-slate-200">{index + 1}. {item.name}</p>
                                        <p className="text-xs text-slate-400 font-mono">{item.sku}</p>
                                    </div>
                                    <p className="font-bold text-xl text-brand-accent">{item.quantity}</p>
                                </li>
                            ))}
                        </StatDisplayCard>
                        
                        <StatDisplayCard 
                            title={t('reports.mostActiveTitle')}
                            icon={<UsersIcon className="w-5 h-5 text-brand-accent" />}
                            isEmpty={mostActiveUsers.length === 0}
                            emptyMessage={t('reports.noUserData')}
                        >
                            {mostActiveUsers.map((user, index) => (
                                <li key={user.id} className="flex justify-between items-center bg-slate-800 p-3 rounded-md">
                                    <p className="font-semibold text-slate-200">{index + 1}. {user.name}</p>
                                    <p className="font-mono text-slate-400">{user.actionCount} {t('reports.actionsUnit')}</p>
                                </li>
                            ))}
                        </StatDisplayCard>

                        <StatDisplayCard 
                            title={t('reports.stockTrendsTitle')}
                            icon={<TrendingUpIcon className="w-5 h-5 text-brand-accent" />}
                            isEmpty={true}
                            emptyMessage={t('reports.stockTrendsDesc')}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ReportsPage;