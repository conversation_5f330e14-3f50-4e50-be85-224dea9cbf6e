import React, { useState, useMemo } from 'react';
import { Program } from '../types';
import { BoxIcon, EditIcon, Trash2Icon } from './icons';
import { useAppState, useAppActions } from '../context/AppContext';
import EditProgramModal from './EditProgramModal';

const ProgramManagementPage: React.FC = () => {
    const { appState } = useAppState();
    const { handleUpdateProgram, handleDeleteProgram } = useAppActions();
    const { programs } = appState!;

    const [searchTerm, setSearchTerm] = useState('');
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);

    const filteredPrograms = useMemo(() => {
        if (!searchTerm) {
            return programs;
        }
        return programs.filter(p => p.name.toLowerCase().includes(searchTerm.toLowerCase()));
    }, [programs, searchTerm]);

    const handleOpenEditModal = (program: Program) => {
        setSelectedProgram(program);
        setIsEditModalOpen(true);
    };

    const handleCloseEditModal = () => {
        setSelectedProgram(null);
        setIsEditModalOpen(false);
    };

    const handleSaveEditedProgram = (program: Program) => {
        handleUpdateProgram(program);
        handleCloseEditModal();
    };

    const handleDeleteClick = (program: Program) => {
        if (window.confirm(`Are you sure you want to delete ${program.name}? This will also delete all associated sub-programs and may affect workshops. This action cannot be undone.`)) {
            handleDeleteProgram(program.id);
        }
    };

    return (
        <>
        <div role="tabpanel" className="animate-fade-in">
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
                <div className="flex items-center">
                    <BoxIcon className="w-6 h-6 mr-3 text-brand-accent" />
                    <h3 className="text-xl font-bold text-slate-200">
                        Program Management
                    </h3>
                </div>
                <div className="flex items-center space-x-3 w-full md:w-auto">
                    <input
                        type="search"
                        placeholder="Search by name..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                        className="w-full md:w-48 bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition"
                    />
                </div>
            </div>
            <p className="text-slate-400 mt-2 mb-6">
                Manage the parent programs that other entities belong to.
            </p>
            <div className="bg-slate-900/50 rounded-lg border border-slate-700">
                <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left text-slate-300">
                        <thead className="text-xs text-slate-400 uppercase bg-slate-800">
                            <tr>
                                <th scope="col" className="px-6 py-3 font-semibold tracking-wider">Program Name</th>
                                <th scope="col" className="px-6 py-3"><span className="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredPrograms.map((program) => (
                                <tr key={program.id} className="border-b border-slate-700 last:border-b-0 hover:bg-slate-700/50 transition-colors duration-200">
                                    <th scope="row" className="px-6 py-4 font-medium text-slate-100 whitespace-nowrap">
                                        {program.name}
                                    </th>
                                    <td className="px-6 py-4 text-right">
                                        <button onClick={() => handleOpenEditModal(program)} className="p-2 text-brand-accent hover:text-amber-300 transition-colors">
                                            <EditIcon className="w-4 h-4" />
                                        </button>
                                        <button onClick={() => handleDeleteClick(program)} className="p-2 text-status-error hover:text-red-400 transition-colors">
                                            <Trash2Icon className="w-4 h-4" />
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <EditProgramModal
            isOpen={isEditModalOpen}
            onClose={handleCloseEditModal}
            onSave={handleSaveEditedProgram}
            program={selectedProgram}
        />
        </>
    );
};

export default ProgramManagementPage;
