import React, { useState, useMemo } from 'react';
import { Program, SubProgram, Workshop } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { EditIcon, Trash2Icon, PlusIcon, ArrowRightIcon, ChevronDownIcon, ArchiveIcon, BuildingIcon, WrenchIcon } from './icons';
import CreateProgramModal from './CreateProgramModal';
import EditProgramModal from './EditProgramModal';
import CreateDepartmentModal from './CreateDepartmentModal';
import EditDepartmentModal from './EditDepartmentModal';
import CreateRackXModal from './CreateRackXModal';
import EditWorkshopModal from './EditWorkshopModal';

const OperationsManagementPage: React.FC = () => {
    const { appState } = useAppState();
    const { programs, subPrograms, workshops } = appState!;
    const actions = useAppActions();

    const [expandedPrograms, setExpandedPrograms] = useState<Set<number>>(new Set());
    const [expandedDepts, setExpandedDepts] = useState<Set<number>>(new Set());

    const [isCreateProgramModalOpen, setCreateProgramModalOpen] = useState(false);
    const [isEditProgramModalOpen, setEditProgramModalOpen] = useState(false);
    const [editingProgram, setEditingProgram] = useState<Program | null>(null);

    const [isCreateDeptModalOpen, setCreateDeptModalOpen] = useState(false);
    const [isEditDeptModalOpen, setEditDeptModalOpen] = useState(false);
    const [editingDept, setEditingDept] = useState<SubProgram | null>(null);
    const [parentProgramForNewDept, setParentProgramForNewDept] = useState<number | null>(null);

    const [isCreateRackModalOpen, setCreateRackModalOpen] = useState(false);
    const [isEditRackModalOpen, setEditRackModalOpen] = useState(false);
    const [editingRack, setEditingRack] = useState<Workshop | null>(null);
    const [parentDeptForNewRack, setParentDeptForNewRack] = useState<number | null>(null);

    const hierarchy = useMemo(() => {
        const subProgramsByProgram = subPrograms.reduce((acc, sp) => {
            (acc[sp.program_id] = acc[sp.program_id] || []).push(sp);
            return acc;
        }, {} as Record<number, SubProgram[]>);

        const workshopsBySubProgram = workshops.reduce((acc, w) => {
            (acc[w.sub_program_id] = acc[w.sub_program_id] || []).push(w);
            return acc;
        }, {} as Record<number, Workshop[]>);

        return programs.map(p => ({
            ...p,
            departments: (subProgramsByProgram[p.id] || []).map(d => ({
                ...d,
                rackx: workshopsBySubProgram[d.id] || [],
            })),
        }));
    }, [programs, subPrograms, workshops]);

    const toggleProgram = (id: number) => setExpandedPrograms(prev => new Set(prev.has(id) ? [...prev].filter(x => x !== id) : [...prev, id]));
    const toggleDept = (id: number) => setExpandedDepts(prev => new Set(prev.has(id) ? [...prev].filter(x => x !== id) : [...prev, id]));

    // Program Actions
    const handleOpenCreateProgram = () => setCreateProgramModalOpen(true);
    const handleOpenEditProgram = (p: Program) => { setEditingProgram(p); setEditProgramModalOpen(true); };
    const handleDeleteProgram = (p: Program) => { if (window.confirm(`Delete ${p.name}? This is permanent.`)) actions.handleDeleteProgram(p.id); };

    // Department Actions
    const handleOpenCreateDept = (programId: number) => { setParentProgramForNewDept(programId); setCreateDeptModalOpen(true); };
    const handleOpenEditDept = (d: SubProgram) => { setEditingDept(d); setEditDeptModalOpen(true); };
    const handleDeleteDept = (d: SubProgram) => { if (window.confirm(`Delete ${d.name}? This is permanent.`)) actions.handleDeleteSubProgram(d.id); };

    // RackX Actions
    const handleOpenCreateRack = (deptId: number) => { setParentDeptForNewRack(deptId); setCreateRackModalOpen(true); };
    const handleOpenEditRack = (r: Workshop) => { setEditingRack(r); setEditRackModalOpen(true); };
    const handleDeleteRack = (r: Workshop) => { if (window.confirm(`Delete ${r.name}? This is permanent.`)) actions.handleDeleteWorkshop(r.id); };


    return (
        <>
            <div className="animate-fade-in">
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                        <ArchiveIcon className="w-6 h-6 mr-3 text-brand-accent" />
                        <h3 className="text-xl font-bold text-slate-200">Operations Management</h3>
                    </div>
                    <button onClick={handleOpenCreateProgram} className="flex items-center px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                        <PlusIcon className="w-5 h-5 mr-2" />
                        New Program
                    </button>
                </div>
                <p className="text-slate-400 mt-2 mb-6">
                    Manage the complete hierarchy of Programs, Departments, and RackX units.
                </p>
                <div className="space-y-2">
                    {hierarchy.map(program => (
                        <div key={program.id} className="bg-slate-800/50 rounded-lg border border-slate-700/50">
                            <div className="flex items-center p-3">
                                <button onClick={() => toggleProgram(program.id)} className="p-1 mr-2 text-slate-400 hover:text-slate-200">
                                    {expandedPrograms.has(program.id) ? <ChevronDownIcon className="w-5 h-5" /> : <ArrowRightIcon className="w-5 h-5" />}
                                </button>
                                <ArchiveIcon className="w-5 h-5 mr-3 text-slate-400" />
                                <span className="font-bold text-lg text-slate-100">{program.name}</span>
                                <div className="ml-auto flex items-center gap-2">
                                    <button onClick={() => handleOpenEditProgram(program)} className="p-2 text-brand-accent hover:text-amber-300 transition-colors"><EditIcon className="w-4 h-4" /></button>
                                    <button onClick={() => handleDeleteProgram(program)} className="p-2 text-status-error hover:text-red-400 transition-colors"><Trash2Icon className="w-4 h-4" /></button>
                                </div>
                            </div>
                            {expandedPrograms.has(program.id) && (
                                <div className="pl-8 pr-4 pb-4 space-y-3">
                                    {program.departments.map(dept => (
                                        <div key={dept.id} className="bg-slate-900/40 rounded-lg border border-slate-700/50">
                                            <div className="flex items-center p-3">
                                                <button onClick={() => toggleDept(dept.id)} className="p-1 mr-2 text-slate-400 hover:text-slate-200">
                                                    {expandedDepts.has(dept.id) ? <ChevronDownIcon className="w-5 h-5" /> : <ArrowRightIcon className="w-5 h-5" />}
                                                </button>
                                                <BuildingIcon className="w-5 h-5 mr-3 text-slate-400" />
                                                <span className="font-semibold text-md text-slate-200">{dept.name}</span>
                                                <div className="ml-auto flex items-center gap-2">
                                                    <button onClick={() => handleOpenEditDept(dept)} className="p-2 text-brand-accent hover:text-amber-300 transition-colors"><EditIcon className="w-4 h-4" /></button>
                                                    <button onClick={() => handleDeleteDept(dept)} className="p-2 text-status-error hover:text-red-400 transition-colors"><Trash2Icon className="w-4 h-4" /></button>
                                                </div>
                                            </div>
                                            {expandedDepts.has(dept.id) && (
                                                <div className="pl-8 pr-4 pb-3 space-y-2">
                                                    {dept.rackx.map(rack => (
                                                        <div key={rack.id} className="flex items-center p-2 bg-slate-800/60 rounded">
                                                            <WrenchIcon className="w-4 h-4 mr-3 text-slate-500" />
                                                            <span className="text-slate-300">{rack.name}</span>
                                                            <div className="ml-auto flex items-center gap-2">
                                                                <button onClick={() => handleOpenEditRack(rack)} className="p-2 text-brand-accent hover:text-amber-300 transition-colors"><EditIcon className="w-4 h-4" /></button>
                                                                <button onClick={() => handleDeleteRack(rack)} className="p-2 text-status-error hover:text-red-400 transition-colors"><Trash2Icon className="w-4 h-4" /></button>
                                                            </div>
                                                        </div>
                                                    ))}
                                                    <button onClick={() => handleOpenCreateRack(dept.id)} className="flex items-center text-sm p-2 text-slate-400 hover:text-slate-200 transition-colors">
                                                        <PlusIcon className="w-4 h-4 mr-1" /> Add RackX
                                                    </button>
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                    <button onClick={() => handleOpenCreateDept(program.id)} className="flex items-center text-sm p-2 text-slate-400 hover:text-slate-200 transition-colors">
                                        <PlusIcon className="w-4 h-4 mr-1" /> Add Department
                                    </button>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Modals */}
            <CreateProgramModal isOpen={isCreateProgramModalOpen} onClose={() => setCreateProgramModalOpen(false)} onSave={actions.handleCreateProgram} />
            <EditProgramModal isOpen={isEditProgramModalOpen} onClose={() => setEditProgramModalOpen(false)} onSave={actions.handleUpdateProgram} program={editingProgram} />

            <CreateDepartmentModal isOpen={isCreateDeptModalOpen} onClose={() => setCreateDeptModalOpen(false)} onSave={actions.handleCreateSubProgram} parentProgramId={parentProgramForNewDept} />
            <EditDepartmentModal isOpen={isEditDeptModalOpen} onClose={() => setEditDeptModalOpen(false)} onSave={actions.handleUpdateSubProgram} subProgram={editingDept} programs={programs} />

            <CreateRackXModal isOpen={isCreateRackModalOpen} onClose={() => setCreateRackModalOpen(false)} onSave={actions.handleCreateWorkshop} parentSubProgramId={parentDeptForNewRack} />
            <EditWorkshopModal isOpen={isEditRackModalOpen} onClose={() => setEditRackModalOpen(false)} onSave={actions.handleUpdateWorkshop} workshop={editingRack} />
        </>
    );
};

export default OperationsManagementPage;
