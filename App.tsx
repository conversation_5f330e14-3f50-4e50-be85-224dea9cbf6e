import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { UserType, Page } from './types';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import SettingsPage from './components/SettingsPage';
import DashboardPage from './components/DashboardPage';
import ReportsPage from './components/ReportsPage';
import WorkshopPage from './components/WorkshopPage';
import DocumentationPage from './components/DocumentationPage';
import InventoryCatalogPage from './components/InventoryCatalogPage';
import AdminConsolePage from './components/AdminConsolePage';
import CreateRequestModal from './components/CreateRequestModal';
import { useAppState, useAppActions } from './context/AppContext';
import IntroPage from './components/IntroPage';
import LoginPage from './components/LoginPage';
import { TeamBreakSchedulerModal } from './components/TeamBreakSchedulerModal';
import MyRackxPage from './components/MyRackxPage';
import { useTranslation } from './i18n/I18nProvider';
import { SpinnerIcon } from './components/icons';

const App: React.FC = () => {
    // App state from hooks
    const { appState, isLoading, isSidebarOpen, isRequestModalOpen, isBreakSchedulerModalOpen, isAuthenticated, isIdle } = useAppState();
    // App actions from hooks
    const { handleLogin, handleLogout, closeRequestModal, handleCreateRequest, closeBreakSchedulerModal, setIsIdle } = useAppActions();
    const { t } = useTranslation();
    
    // UI state for intro flow
    const [showIntro, setShowIntro] = useState(() => !localStorage.getItem('hasSeenIntro'));
    
    // UI state for navigation
    const [activePage, setActivePage] = useState<Page>('Dashboard');
    
    // Auth handlers
    const handleIntroComplete = () => {
        localStorage.setItem('hasSeenIntro', 'true');
        setShowIntro(false);
    };

    // Inactivity timer logic
    useEffect(() => {
        let inactivityTimer: NodeJS.Timeout;

        const resetTimer = () => {
            clearTimeout(inactivityTimer);
            if (isAuthenticated) {
                inactivityTimer = setTimeout(() => {
                    setIsIdle(true);
                }, 40000); // 40 seconds
            }
        };

        const handleActivity = () => {
            resetTimer();
        };

        window.addEventListener('mousemove', handleActivity);
        window.addEventListener('mousedown', handleActivity);
        window.addEventListener('keypress', handleActivity);

        resetTimer();

        return () => {
            clearTimeout(inactivityTimer);
            window.removeEventListener('mousemove', handleActivity);
            window.removeEventListener('mousedown', handleActivity);
            window.removeEventListener('keypress', handleActivity);
        };
    }, [isAuthenticated, setIsIdle]);


    useEffect(() => {
        // When user is switched via the user menu while logged in, reset to their default page.
        if (appState?.currentUser && isAuthenticated) {
            const landingPage = (appState.currentUser.role === 'Admin' || appState.currentUser.role === 'Manager') ? 'Dashboard' : 'My RackX';
            setActivePage(landingPage);
        }
    }, [appState?.currentUser, isAuthenticated]);

    const renderPage = () => {
        if (!appState) return null;
        switch(activePage) {
            case 'Dashboard': return <DashboardPage setActivePage={setActivePage} />;
            case 'My RackX': return <MyRackxPage />;
            case 'InventoryCatalog': return <InventoryCatalogPage />;
            case 'Workshops': return <WorkshopPage />;
            case 'Reports': return <ReportsPage />;
            case 'Documentation': return <DocumentationPage />;
            case 'Settings': return <SettingsPage />;
            case 'AdminConsole': return <AdminConsolePage setActivePage={setActivePage} />;
            default: return <h1>Page not found</h1>;
        }
    };

    if (isLoading || !appState) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-slate-950">
                <div className="text-center">
                    <SpinnerIcon className="animate-spin h-10 w-10 text-brand-accent mx-auto" />
                    <p className="mt-4 text-slate-400">{t('app.loading')}</p>
                </div>
            </div>
        );
    }
    
    if (isIdle) {
        return <IntroPage onComplete={() => setIsIdle(false)} />;
    }

    if (showIntro) {
        return <IntroPage onComplete={handleIntroComplete} />;
    }

    if (!isAuthenticated) {
        return <LoginPage onLogin={handleLogin} appState={appState} />;
    }
    
    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="flex flex-1 pt-16">
                <Sidebar 
                    activePage={activePage}
                    setActivePage={setActivePage}
                    currentUser={appState.currentUser}
                    isSidebarOpen={isSidebarOpen}
                />
                <main 
                    className="flex-1 overflow-y-auto transition-all duration-300 ease-in-out"
                    style={{ marginInlineStart: isSidebarOpen ? '16rem' : '0' }}
                >
                    <div className="p-4 sm:p-6 lg:p-8 h-full">
                        {renderPage()}
                    </div>
                </main>
            </div>
            <CreateRequestModal 
                isOpen={isRequestModalOpen}
                onClose={closeRequestModal}
                onCreateRequest={handleCreateRequest}
            />
            <TeamBreakSchedulerModal
                isOpen={isBreakSchedulerModalOpen}
                onClose={closeBreakSchedulerModal}
            />
        </div>
    );
};

export default App;