import React, { useState, FormEvent, useEffect } from 'react';
import { StagedImport, StagedImportRow } from '../types';
import { XIcon, SaveIcon } from './icons';

interface EditStagedDataModalProps {
    isOpen: boolean;
    onClose: () => void;
    stagedImport: StagedImport | null;
    onUpdateStagedImport: (importId: number, updatedRows: StagedImportRow[]) => Promise<void>;
}

const EditStagedDataModal: React.FC<EditStagedDataModalProps> = ({ isOpen, onClose, stagedImport, onUpdateStagedImport }) => {
    const [editedRows, setEditedRows] = useState<StagedImportRow[]>([]);

    useEffect(() => {
        if (stagedImport) {
            // Deep copy to avoid mutating the original state directly
            setEditedRows(JSON.parse(JSON.stringify(stagedImport.rows)));
        }
    }, [stagedImport]);

    if (!isOpen || !stagedImport) {
        return null;
    }

    const handleRowChange = (rowIndex: number, cellIndex: number, value: string) => {
        setEditedRows(currentRows => {
            const newRows = [...currentRows];
            newRows[rowIndex] = {
                ...newRows[rowIndex],
                data: newRows[rowIndex].data.map((cell, idx) => idx === cellIndex ? value : cell)
            };
            return newRows;
        });
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        onUpdateStagedImport(stagedImport.id, editedRows);
    };

    const inputClasses = "w-full bg-slate-100 dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md p-1 text-slate-900 dark:text-slate-200 focus:ring-1 focus:ring-brand-accent focus:border-brand-accent outline-none transition text-xs";

    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog"
            aria-modal="true"
            onClick={onClose}
        >
            <div 
                className="bg-white dark:[background-image:var(--card-bg-modal)] dark:backdrop-blur-md rounded-lg shadow-xl border border-slate-200 dark:border-slate-700 w-full max-w-4xl h-[90vh] flex flex-col"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700 flex-shrink-0">
                    <div>
                        <h2 className="text-xl font-bold text-slate-900 dark:text-slate-100">Edit Staged Data</h2>
                        <p className="text-sm text-slate-500 dark:text-slate-400 truncate" title={stagedImport.fileName || ''}>{stagedImport.fileName}</p>
                    </div>
                    <button onClick={onClose} className="text-slate-400 hover:text-slate-800 dark:hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>
                
                <form onSubmit={handleSubmit} className="flex-grow flex flex-col overflow-hidden">
                    <div className="p-4 flex-grow overflow-auto">
                        <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
                            Correct any invalid data below. The changes will be re-validated upon saving.
                        </p>
                        <div className="overflow-x-auto">
                            <table className="w-full text-xs">
                                <thead className="bg-slate-100 dark:bg-slate-900/50 sticky top-0">
                                    <tr className="text-left text-slate-600 dark:text-slate-300">
                                        {stagedImport.fileHeaders.map(header => (
                                            <th key={header} className="p-2 font-semibold tracking-wider">{header}</th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className="bg-white dark:bg-slate-800">
                                    {editedRows.map((row, rowIndex) => (
                                        <tr 
                                            key={rowIndex} 
                                            className={`border-b border-slate-200 dark:border-slate-700 last:border-b-0 ${row.status === 'error' ? 'bg-red-50 dark:bg-red-900/20' : ''}`}
                                        >
                                            {row.data.map((cell, cellIndex) => (
                                                <td key={cellIndex} className="p-1 align-top">
                                                    <input
                                                        type="text"
                                                        value={cell}
                                                        onChange={(e) => handleRowChange(rowIndex, cellIndex, e.target.value)}
                                                        className={`${inputClasses} ${row.status === 'error' ? 'border-red-400' : ''}`}
                                                    />
                                                </td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div className="flex justify-end p-4 bg-slate-50 dark:bg-slate-800/50 border-t border-slate-200 dark:border-slate-700 rounded-b-lg space-x-3 flex-shrink-0">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-600 text-white font-semibold rounded-md shadow-md hover:bg-slate-500 transition">
                            Cancel
                        </button>
                        <button type="submit" className="flex items-center gap-2 px-4 py-2 bg-brand-accent text-slate-900 font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                            <SaveIcon className="w-5 h-5"/>
                            Save & Re-validate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditStagedDataModal;