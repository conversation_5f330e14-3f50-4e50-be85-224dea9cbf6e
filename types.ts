
export type Page = 'Dashboard' | 'My RackX' | 'InventoryCatalog' | 'Workshops' | 'Reports' | 'Documentation' | 'AdminConsole' | 'Settings';

export interface Program {
  id: number;
  name: string;
}

export interface SubProgram {
  id: number;
  name: string;
  program_id: number;
}

export interface CatalogCustomField {
  id: string; // e.g., 'supplier'
  name: string; // e.g., 'Supplier'
  type: 'text' | 'number' | 'date' | 'boolean';
}

export interface CatalogItem {
  id: number;
  name: string;
  description: string;
  sku: string;
  image_url: string;
  category: string;
  default_unit_price: number;
  is_restricted: boolean;
  custom_fields: Record<string, any>;
}

export interface Workshop {
  id: number;
  name: string;
  location_desc: string;
  type: 'Central' | 'Local';
  sub_program_id: number;
}

export interface WorkshopInventoryItem {
  id: number;
  workshop_id: number;
  item_id: number; // FK to CatalogItem
  quantity: number;
  low_stock_threshold: number;
}

export interface ConsumedItem {
  id: number;
  name: string;
  quantity: number;
  cost: number;
}

export type UserRole = 'Admin' | 'Manager' | 'Supervisor' | 'Line Leader' | 'TeamLeader' | 'Operator';

export interface UserType {
  id: number;
  name: string;
  employeeId: string;
  role: UserRole;
  password: string;
  primary_workshop_id: number; // FK to Workshop
  avatarUrl: string;
  reelImageUrl: string;
}

export type RequestStatus = 'Pending' | 'Approved' | 'Rejected';

export interface RequestItemDetail {
  item_id: number;
  quantity: number;
}

export interface Request {
  id: number;
  type: 'TRANSFER' | 'CONSUMPTION';
  status: RequestStatus;
  created_by_user_id: number;
  created_at: string;
  items: RequestItemDetail[];
  
  // For TRANSFER
  from_workshop_id?: number;
  to_workshop_id?: number;
  reason?: string;

  // For CONSUMPTION
  workshop_id?: number; // The RackX where consumption happened
  total_cost?: number;
}


export interface AuditLog {
  id: number;
  action: string;
  user_id: number;
  details: object;
  created_at: string;
}

export interface StagedImportRow {
  data: string[];
  status: 'ok' | 'error';
  message?: string;
}

export type ImportType = 'Inventory' | 'Users' | 'Workshop Stock' | 'Add/Update Inventory';

export interface StagedImport {
  id: number;
  source: 'csv' | 'erp';
  importType: ImportType;
  fileName: string | null;
  user_id: number;
  created_at: string;
  status: 'staged' | 'processing' | 'processed' | 'error' | 'discarded';
  rows: StagedImportRow[];
  summary: {
    total: number;
    valid: number;
    invalid: number;
  };
  columnMapping?: { [key: string]: string };
  fileHeaders: string[];
}

export interface BreakSlot {
    startTime: string; // "HH:mm" format
    endTime: string;
}

export type BreakSchedule = Record<string, BreakSlot>; // e.g., { morning: BreakSlot, lunch: BreakSlot }


export interface AppState {
  programs: Program[];
  subPrograms: SubProgram[];
  catalog: CatalogItem[];
  catalogCustomFields: CatalogCustomField[];
  workshops: Workshop[];
  workshopInventories: WorkshopInventoryItem[];
  consumed: ConsumedItem[];
  users: UserType[];
  currentUser: UserType;
  requests: Request[];
  auditLog: AuditLog[];
  stagedImports: StagedImport[];
  breakSchedules: Record<number, BreakSchedule>; // workshopId -> BreakSchedule
}

export interface AISuggestion {
  item_id: number;
  quantity: number;
  reason: string;
  // For display purposes
  name: string;
  sku: string;
  stock: number;
}

export interface TopConsumedItem {
  id: number;
  name:string;
  sku: string;
  quantity: number;
}

export interface MostActiveUser {
  id: number;
  name: string;
  actionCount: number;
}

// --- Data Source Management Types ---

export type DataSourceType = 'SQL_SERVER' | 'SUPABASE' | 'ERP';

export type DataSourceConfig = {
    host?: string;
    port?: number;
    user?: string;
    password?: string;
    database?: string;
    encrypt?: boolean;
    trustServerCertificate?: boolean;
    projectUrl?: string;
    anonKey?: string;
    serviceRoleKey?: string;
    apiUrl?: string;
    apiKey?: string;
};

export interface DataSource {
    Id: number;
    Name: string;
    Type: DataSourceType;
    Config: DataSourceConfig;
    CreatedAt: string;
    UpdatedAt: string;
}