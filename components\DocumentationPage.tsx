import React from 'react';
import { BookOpenIcon } from './icons';

const DocumentationPage: React.FC = () => {
  return (
    <div className="flex flex-col h-full">
        <div className="[background-image:var(--card-bg-panel)] backdrop-blur-md rounded-lg shadow-lg border border-slate-800 overflow-hidden animate-fade-in flex flex-col flex-grow">
            <div className="p-4 sm:p-6 border-b border-slate-700/50 flex-shrink-0">
                <div className="flex items-center">
                    <BookOpenIcon className="w-8 h-8 mr-3 text-brand-accent" />
                    <h2 className="text-3xl font-bold text-slate-100">Documentation</h2>
                </div>
            </div>
            <div className="overflow-y-auto flex-grow p-6 sm:p-8">
                <div className="max-w-4xl mx-auto space-y-8">
                    <div>
                        <h3 className="text-lg font-semibold text-slate-200">System Architecture</h3>
                        <p className="text-slate-400 mt-2">
                            RackX is designed with a modern, scalable architecture. The core of the system is a central **Item Catalog**, which contains the definitions for every unique item. Stock levels are not stored here. Instead, individual **RackX** instances (both 'Central' and 'Local') have their own inventories that reference items from the Catalog. This allows for precise tracking of assets across distributed locations.
                        </p>
                    </div>

                    <div>
                        <h3 className="text-lg font-semibold text-slate-200">Data Persistence & Backend Simulation</h3>
                        <p className="text-slate-400 mt-2">
                        To provide a durable and realistic experience, all data operations are managed through a centralized API service layer that uses your browser's <code className="bg-slate-900 text-amber-400 px-1 py-0.5 rounded-md text-sm">localStorage</code>. This service simulates asynchronous calls to a backend server.
                        </p>
                        <p className="text-slate-400 mt-2">
                            This architecture means the application is ready for future integration. The local storage logic can be easily swapped out for real network requests to a database like <strong className="text-slate-200">MongoDB Atlas</strong> or <strong className="text-slate-200">SQLite</strong> without changing the rest of the application.
                        </p>
                    </div>

                    <div>
                        <h3 className="text-lg font-semibold text-slate-200">Audit Logging</h3>
                        <p className="text-slate-400 mt-2">
                            Every significant action within the application, such as creating a request, approving a transfer, or consuming an item, is recorded in an **Audit Log**. This provides transparency and a clear history of all activities, which can be viewed in the Activity Feed on the dashboard.
                        </p>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-slate-200">Backup & Restore</h3>
                        <p className="text-slate-400 mt-2">
                            For full data portability, Admins can use the "Backup & Restore" feature in Settings. This allows you to export your entire application state to a text file and import it on another machine, ensuring you never lose your work.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
  );
};

export default DocumentationPage;