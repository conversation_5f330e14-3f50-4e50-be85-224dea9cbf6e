import React, { useState, FormEvent, useEffect, useMemo } from 'react';
import { Workshop } from '../types';
import { XIcon } from './icons';
import { useAppState } from '../context/AppContext';

interface CreateRackXModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (workshopData: Omit<Workshop, 'id'>) => void;
    parentSubProgramId?: number | null;
}

const CreateRackXModal: React.FC<CreateRackXModalProps> = ({ isOpen, onClose, onSave, parentSubProgramId }) => {
    const { appState } = useAppState();
    const { subPrograms } = appState!;

    const getInitialState = () => ({
        name: '',
        location_desc: '',
        type: 'Local' as 'Local' | 'Central',
        sub_program_id: parentSubProgramId ?? (subPrograms.length > 0 ? subPrograms[0].id : -1),
    });

    const [newRackX, setNewRackX] = useState(getInitialState());
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    const parentSubProgram = useMemo(() => {
        return subPrograms.find(sp => sp.id === newRackX.sub_program_id);
    }, [subPrograms, newRackX.sub_program_id]);


    useEffect(() => {
        if (isOpen) {
            setNewRackX(getInitialState());
            setErrors({});
        }
    }, [isOpen, parentSubProgramId, subPrograms]);


    if (!isOpen) return null;

    const validate = () => {
        const tempErrors: { [key:string]: string } = {};
        if (!newRackX.name.trim()) {
            tempErrors.name = 'RackX name is required.';
        } else if (!newRackX.name.toLowerCase().includes('rackx')) {
            tempErrors.name = 'Name must contain the term "RackX".';
        }
        if (!newRackX.location_desc.trim()) tempErrors.location_desc = 'Location description is required.';
        if (newRackX.sub_program_id === -1) tempErrors.sub_program_id = 'A department must be selected.';
        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (validate()) {
            onSave({
                name: newRackX.name.trim(),
                location_desc: newRackX.location_desc.trim(),
                type: newRackX.type,
                sub_program_id: newRackX.sub_program_id,
            });
            onClose();
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setNewRackX(prev => ({ ...prev, [name]: value }));
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";

    return (
        <div
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-lg relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Create New RackX</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4">
                        <div>
                            <label htmlFor="name" className={labelClasses}>RackX Name</label>
                            <input type="text" id="name" name="name" value={newRackX.name} onChange={handleChange} className={inputClasses} required placeholder="e.g. RackX - Wing Assembly" />
                            {errors.name && <p className="text-status-error text-xs mt-1">{errors.name}</p>}
                        </div>
                        <div>
                            <label htmlFor="location_desc" className={labelClasses}>Location Description</label>
                            <input type="text" id="location_desc" name="location_desc" value={newRackX.location_desc} onChange={handleChange} className={inputClasses} required />
                            {errors.location_desc && <p className="text-status-error text-xs mt-1">{errors.location_desc}</p>}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="sub_program_id" className={labelClasses}>Department</label>
                                <select
                                    id="sub_program_id"
                                    name="sub_program_id"
                                    value={newRackX.sub_program_id}
                                    onChange={handleChange}
                                    className={inputClasses}
                                    disabled={parentSubProgramId != null}
                                >
                                    {subPrograms.length > 0 ? (
                                        subPrograms.map(sp => <option key={sp.id} value={sp.id}>{sp.name}</option>)
                                    ) : (
                                        <option>No departments available</option>
                                    )}
                                </select>
                                {errors.sub_program_id && <p className="text-status-error text-xs mt-1">{errors.sub_program_id}</p>}
                            </div>
                            <div>
                                <label htmlFor="type" className={labelClasses}>Type</label>
                                <select id="type" name="type" value={newRackX.type} onChange={handleChange} className={inputClasses}>
                                    <option value="Local">Local</option>
                                    <option value="Central">Central</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">Cancel</button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">Create RackX</button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default CreateRackXModal;