import React, { useState, useMemo } from 'react';
import { BoxIcon, SettingsIcon, LayoutDashboardIcon, BarChartIcon, WrenchIcon, BookOpenIcon, ChevronDownIcon, RouteIcon } from './icons';
import CostTracker from './CostTracker';
import { Page, UserType, ConsumedItem } from '../types';
import { useAppState, useAppActions } from '../context/AppContext';
import { useTranslation } from '../i18n/I18nProvider';

interface SidebarProps {
  activePage: Page;
  setActivePage: (page: Page) => void;
  currentUser: UserType;
  isSidebarOpen: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ activePage, setActivePage, currentUser, isSidebarOpen }) => {
  const { appState, totalCost, selectedWorkshopId } = useAppState();
  const { handleSelectWorkshop } = useAppActions();
  const { t } = useTranslation();

  const { programs = [], subPrograms = [], workshops = [], consumed = [] } = appState || {};

  const [expanded, setExpanded] = useState<{ programs: Set<number>, departments: Set<number> }>({
    programs: new Set(),
    departments: new Set()
  });

  const { visiblePrograms, visibleSubPrograms, visibleWorkshops } = useMemo(() => {
    if (!currentUser || !workshops.length || !subPrograms.length || !programs.length) {
      return { visiblePrograms: [], visibleSubPrograms: [], visibleWorkshops: [] };
    }

    if (currentUser.role === 'Admin' || currentUser.role === 'Manager') {
      return { visiblePrograms: programs, visibleSubPrograms: subPrograms, visibleWorkshops: workshops };
    }

    const myWorkshop = workshops.find(w => w.id === currentUser.primary_workshop_id);
    if (!myWorkshop) {
      return { visiblePrograms: [], visibleSubPrograms: [], visibleWorkshops: [] };
    }

    const mySubProgram = subPrograms.find(sp => sp.id === myWorkshop.sub_program_id);
    if (!mySubProgram) {
      return { visiblePrograms: [], visibleSubPrograms: [], visibleWorkshops: [myWorkshop] };
    }

    const myProgram = programs.find(p => p.id === mySubProgram.program_id);
    if (!myProgram) {
      return { visiblePrograms: [], visibleSubPrograms: [mySubProgram], visibleWorkshops: [myWorkshop] };
    }
    
    if (currentUser.role === 'TeamLeader' || currentUser.role === 'Operator' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') {
        return {
            visiblePrograms: [myProgram],
            visibleSubPrograms: [mySubProgram],
            visibleWorkshops: [myWorkshop],
        };
    }

    // Default case for any other roles that might be added
    return { visiblePrograms: [], visibleSubPrograms: [], visibleWorkshops: [] };
  }, [currentUser, programs, subPrograms, workshops]);

  const toggleProgram = (id: number) => {
    setExpanded(prev => {
        const newSet = new Set(prev.programs);
        if (newSet.has(id)) newSet.delete(id);
        else newSet.add(id);
        return { ...prev, programs: newSet };
    });
  };

  const toggleDepartment = (id: number) => {
    setExpanded(prev => {
        const newSet = new Set(prev.departments);
        if (newSet.has(id)) newSet.delete(id);
        else newSet.add(id);
        return { ...prev, departments: newSet };
    });
  };

  const NavButton: React.FC<{ page: Page, label: string, icon: React.ReactNode, isMain: boolean }> = ({ page, label, icon, isMain }) => (
    <li>
      <button
        onClick={() => {
            setActivePage(page);
            if (page !== 'Workshops') {
              handleSelectWorkshop(null); // Deselect workshop when clicking a main nav item
            }
        }}
        className={`flex items-center w-full p-3 rounded-lg transition-all duration-200 text-start ${activePage === page && (page !== 'Workshops' || !selectedWorkshopId) ? 'bg-brand-accent text-brand-text-on-accent font-bold shadow-lg shadow-accent' : 'text-slate-300 hover:bg-slate-700/50'}`}
        aria-current={activePage === page && !selectedWorkshopId ? 'page' : undefined}
      >
        {icon}
        <span className="font-semibold">{label}</span>
      </button>
    </li>
  );
  
  const renderSettingsLink = () => {
    const settingsPage: Page = (currentUser.role === 'Admin' || currentUser.role === 'Manager') ? 'AdminConsole' : 'Settings';
    const label = (currentUser.role === 'Admin' || currentUser.role === 'Manager') ? t('sidebar.adminPanel') : t('sidebar.settings');
    return (
        <li>
            <button
                onClick={() => { setActivePage(settingsPage); handleSelectWorkshop(null); }}
                className={`flex items-center w-full p-3 rounded-lg transition-colors duration-200 text-start ${activePage === settingsPage ? 'bg-brand-accent text-brand-text-on-accent font-bold shadow-lg shadow-accent' : 'text-slate-300 hover:bg-slate-700/50'}`}
                aria-current={activePage === settingsPage ? 'page' : undefined}
            >
                <SettingsIcon className="w-5 h-5 me-3" />
                <span className="font-semibold">{label}</span>
            </button>
        </li>
    );
  }

  const isOperator = currentUser.role === 'Operator';

  const handleWorkshopClick = (workshopId: number) => {
      setActivePage('Workshops');
      handleSelectWorkshop(workshopId);
  }

  return (
    <aside 
        className={`fixed top-0 start-0 h-full bg-gradient-to-b from-sidebar-gradient-start via-sidebar-gradient-middle to-sidebar-gradient-end flex-shrink-0 flex flex-col justify-between border-e border-slate-800 transition-transform duration-300 ease-in-out z-10 w-64 ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full rtl:translate-x-full'}`}
        style={{paddingTop: '4rem'}} // Offset for header height
    >
        <div className="p-4 overflow-y-auto">
          <nav>
            <ul className="space-y-2">
              <NavButton page="Dashboard" label={t('sidebar.dashboard')} icon={<LayoutDashboardIcon className="w-5 h-5 me-3" />} isMain />
              {(currentUser.role !== 'Admin' && currentUser.role !== 'Manager') && (
                <NavButton page="My RackX" label={t('sidebar.myRackX')} icon={<WrenchIcon className="w-5 h-5 me-3" />} isMain />
              )}
              <NavButton page="InventoryCatalog" label={t('sidebar.inventoryCatalog')} icon={<BoxIcon className="w-5 h-5 me-3" />} isMain />
              
              {/* RackX Tree */}
              <li>
                <div className="flex items-center w-full p-3 text-slate-300">
                    <RouteIcon className="w-5 h-5 me-3" />
                    <span className="font-semibold">{t('sidebar.browseRackX')}</span>
                </div>
                <ul className="ps-4 mt-1 space-y-1">
                    {visiblePrograms.map(program => (
                        <li key={program.id}>
                            <button onClick={() => toggleProgram(program.id)} className="flex items-center justify-between w-full text-start p-2 rounded-md text-slate-400 hover:bg-slate-700/50 hover:text-slate-200">
                                <span className="font-semibold text-sm">{program.name}</span>
                                <ChevronDownIcon className={`w-4 h-4 transition-transform ${expanded.programs.has(program.id) ? 'rotate-180' : ''}`} />
                            </button>
                            {expanded.programs.has(program.id) && (
                                <ul className="ps-3 mt-1 space-y-1 border-s-2 border-slate-700">
                                    {visibleSubPrograms.filter(sp => sp.program_id === program.id).map(department => (
                                        <li key={department.id}>
                                            <button onClick={() => toggleDepartment(department.id)} className="flex items-center justify-between w-full text-start p-2 rounded-md text-slate-400 hover:bg-slate-700/50 hover:text-slate-200">
                                                <span className="text-sm">{department.name}</span>
                                                <ChevronDownIcon className={`w-4 h-4 transition-transform ${expanded.departments.has(department.id) ? 'rotate-180' : ''}`} />
                                            </button>
                                            {expanded.departments.has(department.id) && (
                                                <ul className="ps-3 mt-1 space-y-1 border-s-2 border-slate-600">
                                                    {visibleWorkshops.filter(w => w.sub_program_id === department.id && w.type === 'Local').map(workshop => (
                                                        <li key={workshop.id}>
                                                            <button 
                                                                onClick={() => handleWorkshopClick(workshop.id)}
                                                                className={`w-full text-start p-2 rounded-md text-xs truncate ${selectedWorkshopId === workshop.id && activePage === 'Workshops' ? 'bg-brand-accent text-brand-text-on-accent font-bold' : 'text-slate-400 hover:text-slate-200'}`}
                                                            >
                                                                {workshop.name}
                                                            </button>
                                                        </li>
                                                    ))}
                                                </ul>
                                            )}
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </li>
                    ))}
                </ul>
              </li>

              <NavButton page="Reports" label={t('sidebar.reports')} icon={<BarChartIcon className="w-5 h-5 me-3" />} isMain />
            </ul>
          </nav>
        </div>
        <div className="p-4 flex-shrink-0">
          {!isOperator && <CostTracker consumedItems={consumed} totalCost={totalCost} className="hidden md:block" />}
          <div className={`pt-4 ${!isOperator ? 'mt-4 border-t border-slate-700/50' : ''}`}>
              <ul className="space-y-2">
                  <NavButton page="Documentation" label={t('sidebar.documentation')} icon={<BookOpenIcon className="w-5 h-5 me-3" />} isMain />
                  {(currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') && renderSettingsLink()}
              </ul>
          </div>
        </div>
    </aside>
  );
};

export default Sidebar;