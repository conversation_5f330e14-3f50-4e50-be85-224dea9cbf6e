import React, { useState, useEffect, useCallback } from 'react';
import { Workshop, Request, AISuggestion } from '../types';
import { useAppState } from '../context/AppContext';
import { XIcon, SparklesIcon, SendIcon } from './icons';
import { getSmartReplenishmentSuggestion } from '../services/geminiService';
import { useTranslation } from '../i18n/I18nProvider';

interface SmartReplenishmentModalProps {
    isOpen: boolean;
    onClose: () => void;
    rackx: Workshop; // Retaining 'rackx' here as it's the term used in the prompt to the AI
    onSmartReplenish: (requestsData: Omit<Request, 'id' | 'status' | 'created_at' | 'created_by_user_id'>[]) => Promise<void>;
}

type ViewState = 'idle' | 'loading' | 'results' | 'error';

const SmartReplenishmentModal: React.FC<SmartReplenishmentModalProps> = ({ isOpen, onClose, rackx: workshop, onSmartReplenish }) => {
    const { appState } = useAppState();
    const { catalog, workshopInventories, requests } = appState!;
    const { t } = useTranslation();
    
    const [viewState, setViewState] = useState<ViewState>('idle');
    const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (isOpen) {
            setViewState('idle');
            setSuggestions([]);
            setError(null);
        }
    }, [isOpen]);

    const handleGetSuggestions = useCallback(async () => {
        setViewState('loading');
        setError(null);

        try {
            const catalogMap = new Map(catalog.map(i => [i.id, i]));
            const workshopInventory = workshopInventories
                .filter(wi => wi.workshop_id === workshop.id)
                .map(wi => ({ ...wi, catalogItem: catalogMap.get(wi.item_id) as any }));

            const centralInventory = workshopInventories
                .filter(wi => wi.workshop_id === 1) // Central warehouse ID is 1
                .map(wi => ({ ...wi, catalogItem: catalogMap.get(wi.item_id) as any }));
                
            const recentRequests = requests.filter(r => 
                r.to_workshop_id === workshop.id || r.workshop_id === workshop.id
            ).slice(-20);

            const aiSuggestions = await getSmartReplenishmentSuggestion(workshop, workshopInventory, centralInventory, recentRequests);
            setSuggestions(aiSuggestions);
            setViewState('results');
        } catch (err) {
            console.error("Error getting AI suggestions:", err);
            setError(err instanceof Error ? err.message : "An unknown error occurred.");
            setViewState('error');
        }
    }, [workshop, workshopInventories, catalog, requests]);
    
    const handleQuantityChange = (itemId: number, newQuantity: number) => {
        setSuggestions(currentSuggestions =>
            currentSuggestions.map(s =>
                s.item_id === itemId ? { ...s, quantity: Math.max(1, newQuantity) } : s
            )
        );
    };

    const handleSubmit = async () => {
        const requestsToCreate = suggestions
            .filter(s => s.quantity > 0)
            .map(s => ({
                type: 'TRANSFER' as const,
                from_workshop_id: 1, // Central warehouse
                to_workshop_id: workshop.id,
                items: [{ item_id: s.item_id, quantity: s.quantity }],
                reason: `Smart Replenishment Suggestion\nReason: ${s.reason}`,
            }));

        if (requestsToCreate.length > 0) {
            await onSmartReplenish(requestsToCreate);
        }
        onClose();
    };

    if (!isOpen) return null;

    const renderContent = () => {
        switch (viewState) {
            case 'loading':
                return (
                    <div className="text-center py-16">
                        <SparklesIcon className="w-12 h-12 text-brand-accent mx-auto animate-pulse" />
                        <h3 className="mt-4 text-lg font-semibold text-slate-800 dark:text-slate-200">{t('smartReplenish.loadingTitle')}</h3>
                        <p className="mt-1 text-slate-500 dark:text-slate-400">{t('smartReplenish.loadingDesc')}</p>
                    </div>
                );
            case 'error':
                 return (
                    <div className="text-center py-16 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <h3 className="text-lg font-semibold text-red-700 dark:text-red-300">{t('smartReplenish.errorTitle')}</h3>
                        <p className="mt-2 text-slate-700 dark:text-slate-300 max-w-sm mx-auto">{error}</p>
                        <button onClick={handleGetSuggestions} className="mt-4 px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                            {t('smartReplenish.tryAgain')}
                        </button>
                    </div>
                );
            case 'results':
                if (suggestions.length === 0) {
                     return (
                        <div className="text-center py-16">
                            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200">{t('smartReplenish.noReplenishTitle')}</h3>
                            <p className="mt-1 text-slate-500 dark:text-slate-400">{t('smartReplenish.noReplenishDesc', { name: workshop.name })}</p>
                        </div>
                    );
                }
                return (
                    <div className="space-y-3">
                         <div className="overflow-y-auto max-h-96 pe-2">
                            <table className="w-full text-sm text-start">
                                <thead className="text-xs text-slate-500 dark:text-slate-400 uppercase bg-slate-50 dark:bg-slate-800 sticky top-0">
                                    <tr>
                                        <th className="px-4 py-2">{t('smartReplenish.tableHeaderItem')}</th>
                                        <th className="px-4 py-2">{t('smartReplenish.tableHeaderReason')}</th>
                                        <th className="px-4 py-2 text-center">{t('smartReplenish.tableHeaderQty')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {suggestions.map(s => (
                                        <tr key={s.item_id} className="border-b border-slate-200 dark:border-slate-700/50">
                                            <td className="px-4 py-3 font-medium text-slate-800 dark:text-slate-100">{s.name}</td>
                                            <td className="px-4 py-3 text-slate-500 dark:text-slate-400 italic">"{s.reason}"</td>
                                            <td className="px-4 py-3">
                                                <input
                                                    type="number"
                                                    value={s.quantity}
                                                    onChange={(e) => handleQuantityChange(s.item_id, parseInt(e.target.value, 10) || 1)}
                                                    min="1"
                                                    max={s.stock}
                                                    className="w-20 bg-slate-100 dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md p-1.5 text-slate-800 dark:text-slate-100 text-center focus:ring-2 focus:ring-brand-accent outline-none"
                                                    aria-label={`Quantity for ${s.name}`}
                                                />
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                );
            case 'idle':
            default:
                return (
                    <div className="text-center py-16">
                        <SparklesIcon className="w-12 h-12 text-brand-accent mx-auto" />
                        <h3 className="mt-4 text-lg font-semibold text-slate-800 dark:text-slate-200">{t('smartReplenish.title')}</h3>
                        <p className="mt-1 text-slate-500 dark:text-slate-400 max-w-sm mx-auto">{t('smartReplenish.description')}</p>
                        <button onClick={handleGetSuggestions} className="mt-6 flex items-center mx-auto px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                            <SparklesIcon className="w-5 h-5 me-2" />
                            {t('smartReplenish.button')}
                        </button>
                    </div>
                );
        }
    };
    
    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            onClick={onClose}
        >
            <div 
                className="bg-white dark:[background-image:var(--card-bg-modal)] dark:backdrop-blur-md rounded-lg shadow-xl border border-slate-200 dark:border-slate-700 w-full max-w-2xl relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
                    <h2 className="text-xl font-bold text-slate-900 dark:text-slate-100 flex items-center">
                        <SparklesIcon className="w-6 h-6 me-3 text-brand-accent"/>
                        {t('smartReplenish.modalTitle', { name: workshop.name })}
                    </h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-slate-800 dark:hover:text-white transition" aria-label={t('close')}>
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>
                
                <div className="p-6">
                    {renderContent()}
                </div>
                
                {(viewState === 'results' && suggestions.length > 0) && (
                    <div className="flex justify-end p-4 bg-slate-50 dark:bg-slate-800/50 border-t border-slate-200 dark:border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-600 text-white font-semibold rounded-md shadow-md hover:bg-slate-500 transition">
                            {t('cancel')}
                        </button>
                        <button 
                            onClick={handleSubmit} 
                            className="flex items-center px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition"
                        >
                            <SendIcon className="w-5 h-5 me-2" />
                            {t('smartReplenish.submitButton')}
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default SmartReplenishmentModal;