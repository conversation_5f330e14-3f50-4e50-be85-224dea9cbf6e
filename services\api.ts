
import { AppState, AuditLog, UserType, Workshop, CatalogItem, UserRole, DataSource } from '../types';
import { INITIAL_DATA } from '../constants';
import { config } from '../config';

const LOCAL_STORAGE_KEY = 'rackx-app-state';

// Simulate network latency
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const getFullState = (): AppState => {
    try {
        const item = window.localStorage.getItem(LOCAL_STORAGE_KEY);
        return item ? JSON.parse(item) : INITIAL_DATA;
    } catch (error) {
        console.error("Error reading from local storage", error);
        return INITIAL_DATA;
    }
}

const getAppData = async (): Promise<AppState> => {
    await delay(250); // Simulate fetching data from a server
    return getFullState();
};

const saveAppData = async (state: AppState): Promise<void> => {
    await delay(100); // Simulate saving data to a server
    try {
        window.localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
        console.error("Error writing to local storage", error);
    }
};

const resetAppData = async (): Promise<AppState> => {
    await delay(100);
    try {
        window.localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(INITIAL_DATA));
    } catch (error) {
        console.error("Error resetting local storage", error);
    }
    return INITIAL_DATA;
};

const getRawDataForBackup = async (): Promise<string> => {
    await delay(50);
    return window.localStorage.getItem(LOCAL_STORAGE_KEY) || JSON.stringify(INITIAL_DATA);
}

const restoreDataFromBackup = async (backupJSON: string): Promise<AppState> => {
    await delay(250);
    let parsedData: AppState;
    try {
        parsedData = JSON.parse(backupJSON);
        if (!parsedData.users || !parsedData.workshopInventories) {
            throw new Error("Invalid backup format");
        }
    } catch (error) {
        console.error("Error parsing backup data", error);
        throw error;
    }

    try {
        window.localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(parsedData));
    } catch (error) {
        console.error("Error restoring local storage", error);
        throw error;
    }
    return parsedData;
}


// --- New Backend-style API methods ---

const createApiGet = <T>(url: string) => {
    return async (params: any): Promise<T> => {
        const query = new URLSearchParams();
        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== '') {
                    if (typeof value === 'object' && value !== null) {
                        // Express doesn't natively parse nested objects in query strings,
                        // so we stringify the filters. The backend will need to parse it.
                        Object.entries(value).forEach(([k, v]) => {
                             if (v !== undefined && v !== '') {
                                query.set(`${key}[${k}]`, String(v));
                             }
                        });
                    } else {
                        query.set(key, String(value));
                    }
                }
            });
        }
        const response = await fetch(`${API_BASE_URL}/${url}?${query.toString()}`);
        return handleApiResponse(response);
    }
}

const getUsersFromApi = createApiGet<UserType[]>('users');

export interface GetUsersParams {
    sortKey?: keyof UserType;
    sortDirection?: 'asc' | 'desc';
    filters?: {
        role?: UserRole;
        workshopId?: number;
        searchTerm?: string;
    };
}

const getUsers = async (params: GetUsersParams): Promise<UserType[]> => {
    if (config.dataSource === 'supabase') {
        return getUsersFromApi(params);
    }

    // --- Local storage implementation ---
    await delay(50);
    let { users } = getFullState();
    const { sortKey = 'name', sortDirection = 'asc', filters = {} } = params;

    if (filters.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        users = users.filter(u => u.name.toLowerCase().includes(term) || u.employeeId.toLowerCase().includes(term));
    }
    if (filters.role) {
        users = users.filter(u => u.role === filters.role);
    }

    users.sort((a, b) => {
        const aValue = a[sortKey];
        const bValue = b[sortKey];
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    return users;
}

const getWorkshopsFromApi = createApiGet<Workshop[]>('workshops');

export interface GetWorkshopsParams {
     sortKey?: keyof Workshop;
     sortDirection?: 'asc' | 'desc';
     filters?: { searchTerm?: string; };
}

const getWorkshops = async (params: GetWorkshopsParams): Promise<Workshop[]> => {
    if (config.dataSource === 'supabase') {
        return getWorkshopsFromApi(params);
    }

    // --- Local storage implementation ---
    await delay(50);
    let { workshops } = getFullState();
    const { sortKey = 'name', sortDirection = 'asc', filters = {} } = params;
    
    // sorting and filtering logic would go here if needed
    
    return workshops;
}

export interface GetCatalogParams {
    sortKey?: keyof CatalogItem;
    sortDirection?: 'asc' | 'desc';
    filters?: { searchTerm?: string; };
}

const getCatalogFromApi = createApiGet<CatalogItem[]>('catalog');

const getCatalog = async (params: GetCatalogParams): Promise<CatalogItem[]> => {
    if (config.dataSource === 'supabase') {
        return getCatalogFromApi(params);
    }

    // --- Local storage implementation ---
     await delay(50);
    let { catalog } = getFullState();
    const { sortKey = 'name', sortDirection = 'asc', filters = {} } = params;

    if (filters.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        catalog = catalog.filter(c => c.name.toLowerCase().includes(term) || c.sku.toLowerCase().includes(term));
    }
    // sorting logic could be added
    return catalog;
}

export interface GetAuditLogParams {
    page?: number;
    limit?: number;
    sortKey?: keyof AuditLog | 'user';
    sortDirection?: 'asc' | 'desc';
    filters?: {
        userId?: number;
        actionType?: string;
        startDate?: string;
        endDate?: string;
        searchTerm?: string;
    }
}

const getAuditLogFromApi = createApiGet<{ logs: AuditLog[], total: number }>('audit-log');

const getAuditLog = async (params: GetAuditLogParams = {}): Promise<{ logs: AuditLog[], total: number }> => {
    if (config.dataSource === 'supabase') {
        return getAuditLogFromApi(params);
    }

    // --- Local storage implementation ---
    await delay(150);
    const { auditLog, users } = getFullState();
    const userMap = new Map(users.map(u => [u.id, u.name]));
    const { page = 1, limit = 15, sortKey = 'created_at', sortDirection = 'desc', filters = {} } = params;

    let filteredLogs = [...auditLog];

    if (filters.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        filteredLogs = filteredLogs.filter(log => JSON.stringify(log.details).toLowerCase().includes(term));
    }
    if (filters.userId) {
        filteredLogs = filteredLogs.filter(log => log.user_id === filters.userId);
    }
    if (filters.actionType) {
        filteredLogs = filteredLogs.filter(log => log.action === filters.actionType);
    }
    if (filters.startDate) {
        filteredLogs = filteredLogs.filter(log => new Date(log.created_at) >= new Date(filters.startDate));
    }
    if (filters.endDate) {
        filteredLogs = filteredLogs.filter(log => new Date(log.created_at) <= new Date(filters.endDate + 'T23:59:59'));
    }

    filteredLogs.sort((a, b) => {
        let aValue: any, bValue: any;
        if (sortKey === 'user') {
            aValue = userMap.get(a.user_id) || '';
            bValue = userMap.get(b.user_id) || '';
        } else {
            aValue = a[sortKey as keyof AuditLog];
            bValue = b[sortKey as keyof AuditLog];
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    const total = filteredLogs.length;
    const paginatedLogs = filteredLogs.slice((page - 1) * limit, page * limit);

    return { logs: paginatedLogs, total };
}


// --- Data Source API Methods ---
const API_BASE_URL = 'http://localhost:3001/api';

const handleApiResponse = async (response: Response) => {
    if (response.status === 204) { // No Content
        return;
    }
    // Try to parse JSON, but handle cases where body might be empty or not JSON
    const text = await response.text();
    const data = text ? JSON.parse(text) : {};

    if (!response.ok) {
        const error = new Error(data.message || data.error || `Request failed with status ${response.status}`);
        // It's helpful to attach the full response to the error object
        (error as any).response = response;
        (error as any).data = data;
        throw error;
    }
    return data;
};

const getDataSources = async (): Promise<DataSource[]> => {
    const response = await fetch(`${API_BASE_URL}/datasources`);
    return handleApiResponse(response);
};

const getDataSource = async (id: number): Promise<DataSource> => {
    const response = await fetch(`${API_BASE_URL}/datasources/${id}`);
    return handleApiResponse(response);
};

const createDataSource = async (dataSource: Partial<Omit<DataSource, 'Id' | 'CreatedAt' | 'UpdatedAt'>>) => {
    const response = await fetch(`${API_BASE_URL}/datasources`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataSource),
    });
    return handleApiResponse(response);
};

const updateDataSource = async (id: number, dataSource: Partial<DataSource>) => {
    const response = await fetch(`${API_BASE_URL}/datasources/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataSource),
    });
    return handleApiResponse(response);
};

const deleteDataSource = async (id: number) => {
    const response = await fetch(`${API_BASE_URL}/datasources/${id}`, {
        method: 'DELETE',
    });
    return handleApiResponse(response);
};

const testDataSource = async (payload: { type: string, config: any }): Promise<{success: boolean, message: string}> => {
    const response = await fetch(`${API_BASE_URL}/datasources/test`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
    });
    return handleApiResponse(response);
};


const migrateToSupabase = async (data: string) => {
    const response = await fetch(`${API_BASE_URL}/migrate-data`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: data,
    });
    return handleApiResponse(response);
};

export const api = {
    getAppData,
    saveAppData,
    resetAppData,
    getRawDataForBackup,
    restoreDataFromBackup,
    // New granular methods
    getUsers,
    getWorkshops,
    getCatalog,
    getAuditLog,
    // Data Source methods
    getDataSources,
    getDataSource,
    createDataSource,
    updateDataSource,
    deleteDataSource,
    testDataSource,
    migrateToSupabase,
};
