-- Create the 'programs' table
CREATE TABLE programs (
    id BIGINT PRIMARY KEY,
    name TEXT NOT NULL
);

-- Create the 'sub_programs' table
CREATE TABLE sub_programs (
    id BIGINT PRIMARY KEY,
    name TEXT NOT NULL,
    program_id BIGINT REFERENCES programs(id)
);

-- Create the 'catalog_items' table
CREATE TABLE catalog_items (
    id BIGINT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT NOT NULL UNIQUE,
    image_url TEXT,
    category TEXT,
    default_unit_price NUMERIC(10, 2) NOT NULL,
    is_restricted BOOLEAN NOT NULL,
    custom_fields JSONB
);

-- Create the 'workshops' table
CREATE TABLE workshops (
    id BIGINT PRIMARY KEY,
    name TEXT NOT NULL,
    location_desc TEXT,
    type TEXT NOT NULL,
    sub_program_id BIGINT REFERENCES sub_programs(id)
);

-- Create the 'users' table
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    name TEXT NOT NULL,
    employee_id TEXT NOT NULL UNIQUE,
    role TEXT NOT NULL,
    password TEXT NOT NULL,
    primary_workshop_id BIGINT REFERENCES workshops(id),
    avatar_url TEXT,
    reel_image_url TEXT
);

-- Create the 'workshop_inventories' table
CREATE TABLE workshop_inventories (
    id BIGINT PRIMARY KEY,
    workshop_id BIGINT NOT NULL REFERENCES workshops(id),
    item_id BIGINT NOT NULL REFERENCES catalog_items(id),
    quantity INTEGER NOT NULL,
    low_stock_threshold INTEGER NOT NULL,
    UNIQUE(workshop_id, item_id)
);

-- Create the 'requests' table
CREATE TABLE requests (
    id BIGINT PRIMARY KEY,
    type TEXT NOT NULL,
    status TEXT NOT NULL,
    created_by_user_id BIGINT NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL,
    items JSONB,
    from_workshop_id BIGINT REFERENCES workshops(id),
    to_workshop_id BIGINT REFERENCES workshops(id),
    reason TEXT,
    workshop_id BIGINT REFERENCES workshops(id),
    total_cost NUMERIC(10, 2)
);

-- Create the 'audit_log' table
CREATE TABLE audit_log (
    id BIGINT PRIMARY KEY,
    action TEXT NOT NULL,
    user_id BIGINT NOT NULL REFERENCES users(id),
    details JSONB,
    created_at TIMESTAMPTZ NOT NULL
);

-- Create the 'consumed_items' table
CREATE TABLE consumed_items (
    id BIGINT PRIMARY KEY,
    name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    cost NUMERIC(10, 2) NOT NULL
);

-- Create the 'catalog_custom_fields' table
CREATE TABLE catalog_custom_fields (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL
);

-- Create the 'break_schedules' table
CREATE TABLE break_schedules (
    workshop_id BIGINT PRIMARY KEY REFERENCES workshops(id),
    schedule JSONB NOT NULL
);
