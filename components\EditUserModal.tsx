import React, { useState, FormEvent, useEffect, useMemo } from 'react';
import { UserType, UserRole, Workshop } from '../types';
import { XIcon } from './icons';

interface EditUserModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (userData: UserType) => void;
    user: UserType | null;
    workshops: Workshop[];
    currentUser: UserType;
}

const ROLES: UserRole[] = ['Admin', 'Manager', 'Supervisor', 'Line Leader', 'TeamLeader', 'Operator'];

export const EditUserModal: React.FC<EditUserModalProps> = ({ isOpen, onClose, onSave, user, workshops, currentUser }) => {
    const [editedUser, setEditedUser] = useState<UserType | null>(user);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    useEffect(() => {
        if (isOpen && user) {
            setEditedUser(user);
            setErrors({});
        } else {
            setEditedUser(null);
        }
    }, [isOpen, user]);

    const availableWorkshops = useMemo(() => {
        if (currentUser.role === 'Admin' || currentUser.role === 'Manager' || currentUser.role === 'Supervisor' || currentUser.role === 'Line Leader') {
            return workshops;
        }
        if (currentUser.role === 'TeamLeader') {
            // TeamLeaders can assign users to their own RackX or return them to the central pool.
            return workshops.filter(w => w.id === currentUser.primary_workshop_id || w.id === 1);
        }
        return [];
    }, [workshops, currentUser]);

    if (!isOpen || !editedUser) return null;

    const validate = () => {
        const tempErrors: { [key: string]: string } = {};
        if (!editedUser.name.trim()) tempErrors.name = 'User name is required.';
        if (!editedUser.employeeId.trim()) {
            tempErrors.employeeId = 'Employee ID is required.';
        }
        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        if (validate() && editedUser) {
            onSave(editedUser);
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        const processedValue = name === 'primary_workshop_id' ? parseInt(value, 10) : value;
        setEditedUser(prev => prev ? { ...prev, [name]: processedValue } : null);
    };

    const inputClasses = "w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent outline-none transition disabled:opacity-50 disabled:cursor-not-allowed";
    const labelClasses = "block text-sm font-medium text-slate-300 mb-1";
    
    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 animate-fade-in"
            role="dialog" aria-modal="true" onClick={onClose}
        >
            <div 
                className="dark:[background-image:var(--card-bg-modal)] backdrop-blur-md rounded-lg shadow-xl border border-slate-700 w-full max-w-md relative"
                onClick={e => e.stopPropagation()}
            >
                <div className="flex items-center justify-between p-4 border-b border-slate-700">
                    <h2 className="text-xl font-bold text-slate-100">Edit User</h2>
                    <button onClick={onClose} className="text-slate-400 hover:text-white transition" aria-label="Close modal">
                        <XIcon className="w-6 h-6" />
                    </button>
                </div>
                
                <form onSubmit={handleSubmit} noValidate>
                    <div className="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
                        <div>
                            <label htmlFor="name" className={labelClasses}>Full Name</label>
                            <input type="text" id="name" name="name" value={editedUser.name} onChange={handleChange} className={inputClasses} required />
                            {errors.name && <p className="text-status-error text-xs mt-1">{errors.name}</p>}
                        </div>
                        <div>
                            <label htmlFor="employeeId" className={labelClasses}>Employee ID</label>
                            <input type="text" id="employeeId" name="employeeId" value={editedUser.employeeId} onChange={handleChange} className={inputClasses} required />
                            {errors.employeeId && <p className="text-status-error text-xs mt-1">{errors.employeeId}</p>}
                        </div>
                        <div>
                           <label htmlFor="primary_workshop_id" className={labelClasses}>Assigned RackX</label>
                           <select id="primary_workshop_id" name="primary_workshop_id" value={editedUser.primary_workshop_id} onChange={handleChange} className={inputClasses}>
                               {availableWorkshops.map(workshop => (
                                   <option key={workshop.id} value={workshop.id}>{workshop.name}</option>
                               ))}
                           </select>
                       </div>
                        <div>
                            <label htmlFor="role" className={labelClasses}>Role</label>
                            <select id="role" name="role" value={editedUser.role} onChange={handleChange} className={inputClasses} disabled={currentUser.role !== 'Admin' && currentUser.role !== 'Manager'}>
                                {ROLES.map(role => (
                                    <option key={role} value={role}>{role}</option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label htmlFor="reelImageUrl" className={labelClasses}>Reel Image URL</label>
                            {editedUser.reelImageUrl && (
                                <img src={editedUser.reelImageUrl} alt="Reel image preview" className="w-full h-32 object-cover rounded-md mb-2 border border-slate-600" />
                            )}
                            <input type="text" id="reelImageUrl" name="reelImageUrl" value={editedUser.reelImageUrl} onChange={handleChange} className={inputClasses} placeholder="https://..." />
                        </div>
                    </div>
                    
                    <div className="flex justify-end p-4 bg-slate-800/50 border-t border-slate-700 rounded-b-lg space-x-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 bg-slate-700 text-white font-semibold rounded-md shadow-md hover:bg-slate-600 transition">
                            Cancel
                        </button>
                        <button type="submit" className="px-4 py-2 bg-brand-accent text-brand-text-on-accent font-bold rounded-md shadow-md hover:bg-brand-accent-hover transition">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};